#!/usr/bin/env python3
"""
GRID Speaker S4 Processing Pipeline
==================================

Processes GRID speaker s4 data for pretraining:
1. Analyzes GRID files and extracts words using GRID filename decoding
2. Filters for viseme-matched vocabulary from grid_proxy_map.json
3. Preprocesses videos using existing pipeline
4. Organizes data for GRID pretraining
5. Executes GRID pretraining with single speaker

Usage:
    python process_grid_s4_for_pretraining.py

Author: Augment Agent
Date: 2025-09-29
"""

import os
import sys
import json
import shutil
import cv2
import numpy as np
from pathlib import Path
from typing import Dict, List, Set, Optional, Tuple
import logging
from collections import defaultdict, Counter
import subprocess
import time

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GRIDProcessor:
    """Processes GRID speaker s4 data for pretraining."""
    
    def __init__(self):
        self.grid_s4_path = Path("GRID_talker_sets/talker 4/s4")
        self.output_base = Path("sagemaker_training_files/data")
        self.grid_raw_dir = self.output_base / "grid_raw"
        self.grid_subset_dir = self.output_base / "grid_subset_3spk"
        
        # GRID decoding mappings (from build_grid_manifest.py)
        self.command_map = {'b': 'bin', 'l': 'lay', 'p': 'place', 's': 'set'}
        self.color_map = {'b': 'blue', 'g': 'green', 'r': 'red', 'w': 'white'}
        self.prep_map = {'a': 'at', 'b': 'by', 'i': 'in', 'w': 'with'}
        self.digit_map = {'0': 'zero', '1': 'one', '2': 'two', '3': 'three', '4': 'four',
                         '5': 'five', '6': 'six', '7': 'seven', '8': 'eight', '9': 'nine'}
        self.adverb_map = {'a': 'again', 'n': 'now', 'p': 'please', 's': 'soon'}
        
        # Load required words from proxy mapping
        self.required_words = self.load_required_words()
        
    def load_required_words(self) -> Set[str]:
        """Load required words from grid_proxy_map.json."""
        config_path = Path("sagemaker_training_files/configs/grid_proxy_map.json")
        
        if not config_path.exists():
            logger.error(f"Grid proxy map not found: {config_path}")
            return set()
        
        with open(config_path, 'r') as f:
            proxy_map = json.load(f)
        
        required_words = set()
        for icu_class, mapping in proxy_map.items():
            required_words.update(mapping.get("primary", []))
            required_words.update(mapping.get("secondary", []))
        
        logger.info(f"Required words: {sorted(required_words)}")
        return required_words
    
    def decode_grid_filename(self, filename: str) -> Optional[List[str]]:
        """
        Decode GRID filename to extract words.
        
        GRID filenames like 'bbae9n.mpg' encode 6-word sentences:
        - 1st char: command (b=bin, l=lay, p=place, s=set)
        - 2nd char: color (b=blue, g=green, r=red, w=white)  
        - 3rd char: preposition (a=at, b=by, i=in, w=with)
        - 4th char: letter (a-z except w)
        - 5th char: digit (0-9)
        - 6th char: adverb (a=again, n=now, p=please, s=soon)
        """
        # Remove extension and get base name
        sentence_id = Path(filename).stem
        
        if len(sentence_id) != 6:
            return None
        
        try:
            words = [
                self.command_map[sentence_id[0]],
                self.color_map[sentence_id[1]], 
                self.prep_map[sentence_id[2]],
                sentence_id[3],  # letter as-is
                self.digit_map[sentence_id[4]],
                self.adverb_map[sentence_id[5]]
            ]
            return words
        except KeyError as e:
            logger.debug(f"Could not decode {filename}: {e}")
            return None
    
    def analyze_grid_s4_data(self) -> Dict[str, List[Path]]:
        """Analyze GRID s4 data and group by required words."""
        
        if not self.grid_s4_path.exists():
            logger.error(f"GRID s4 directory not found: {self.grid_s4_path}")
            return {}
        
        logger.info(f"Analyzing GRID s4 data in: {self.grid_s4_path}")
        
        # Find all video files
        video_files = list(self.grid_s4_path.glob("*.mpg"))
        logger.info(f"Found {len(video_files)} video files")
        
        # Group by words
        word_groups = defaultdict(list)
        word_counts = Counter()
        
        for video_file in video_files:
            words = self.decode_grid_filename(video_file.name)
            if words:
                # Check each word in the sentence
                for word in words:
                    word_counts[word] += 1
                    if word in self.required_words:
                        word_groups[word].append(video_file)
        
        # Log analysis results
        logger.info(f"Word analysis complete:")
        logger.info(f"Total unique words found: {len(word_counts)}")
        logger.info(f"Required words found: {len(word_groups)}")
        
        for word in sorted(self.required_words):
            count = len(word_groups.get(word, []))
            logger.info(f"  {word}: {count} videos")
        
        return dict(word_groups)
    
    def convert_mpg_to_mp4(self, input_path: Path, output_path: Path) -> bool:
        """Convert MPG to MP4 using ffmpeg."""
        try:
            cmd = [
                'ffmpeg', '-i', str(input_path),
                '-c:v', 'libx264', '-c:a', 'aac',
                '-y', str(output_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return True
            else:
                logger.error(f"FFmpeg error converting {input_path}: {result.stderr}")
                return False
                
        except FileNotFoundError:
            logger.error("FFmpeg not found. Please install: brew install ffmpeg")
            return False
        except Exception as e:
            logger.error(f"Error converting {input_path}: {e}")
            return False
    
    def organize_grid_data(self, word_groups: Dict[str, List[Path]]) -> bool:
        """Organize GRID data into required structure."""
        
        # Create output directories
        self.grid_raw_dir.mkdir(parents=True, exist_ok=True)
        speaker_dir = self.grid_raw_dir / "s4"
        speaker_dir.mkdir(exist_ok=True)
        
        total_organized = 0
        
        for word, video_files in word_groups.items():
            if not video_files:
                continue
                
            word_dir = speaker_dir / word
            word_dir.mkdir(exist_ok=True)
            
            logger.info(f"Organizing {len(video_files)} videos for word '{word}'")
            
            for i, video_file in enumerate(video_files):
                # Convert MPG to MP4
                output_filename = f"s4_{word}_{i+1:03d}.mp4"
                output_path = word_dir / output_filename
                
                if self.convert_mpg_to_mp4(video_file, output_path):
                    total_organized += 1
                    if i == 0:  # Log first file
                        logger.info(f"  Converted: {video_file.name} -> {output_filename}")
                else:
                    logger.warning(f"Failed to convert: {video_file.name}")
        
        logger.info(f"Total videos organized: {total_organized}")
        return total_organized > 0
    
    def preprocess_videos(self) -> bool:
        """Preprocess organized videos using existing pipeline."""
        
        logger.info("Starting video preprocessing...")
        
        # Use existing preprocessing tool
        preprocessing_script = Path("tools/stabilize_mouth_roi.py")
        
        if not preprocessing_script.exists():
            logger.error(f"Preprocessing script not found: {preprocessing_script}")
            return False
        
        # Create preprocessed output directory
        preprocessed_dir = self.grid_subset_dir
        preprocessed_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            # Run preprocessing
            cmd = [
                sys.executable, str(preprocessing_script),
                "--input-dir", str(self.grid_raw_dir),
                "--output-dir", str(preprocessed_dir),
                "--max-videos-per-class", "50"  # Limit for single speaker
            ]
            
            logger.info(f"Running preprocessing: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")
            
            if result.returncode == 0:
                logger.info("Preprocessing completed successfully")
                logger.info(f"Preprocessing output:\n{result.stdout}")
                return True
            else:
                logger.error(f"Preprocessing failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error running preprocessing: {e}")
            return False
    
    def create_grid_manifest(self) -> bool:
        """Create manifest file for GRID pretraining."""
        
        manifest_path = self.output_base / "grid_manifest.csv"
        
        try:
            import csv
            
            manifest_data = []
            
            # Scan preprocessed data
            for speaker_dir in self.grid_subset_dir.iterdir():
                if not speaker_dir.is_dir():
                    continue
                    
                speaker_id = speaker_dir.name
                
                for word_dir in speaker_dir.iterdir():
                    if not word_dir.is_dir():
                        continue
                        
                    word = word_dir.name
                    
                    for video_file in word_dir.glob("*.mp4"):
                        relative_path = video_file.relative_to(self.grid_subset_dir)
                        manifest_data.append({
                            'video_path': str(relative_path),
                            'speaker_id': speaker_id,
                            'word': word,
                            'class_label': word,  # For GRID pretraining
                            'split': 'train'
                        })
            
            # Write manifest
            with open(manifest_path, 'w', newline='') as f:
                if manifest_data:
                    writer = csv.DictWriter(f, fieldnames=manifest_data[0].keys())
                    writer.writeheader()
                    writer.writerows(manifest_data)
            
            logger.info(f"Created manifest with {len(manifest_data)} entries: {manifest_path}")
            return len(manifest_data) > 0
            
        except Exception as e:
            logger.error(f"Error creating manifest: {e}")
            return False
    
    def update_grid_config_for_single_speaker(self) -> bool:
        """Update GRID pretraining config for single speaker."""
        
        config_path = Path("sagemaker_training_files/configs/grid_pretrain.yaml")
        
        if not config_path.exists():
            logger.error(f"GRID config not found: {config_path}")
            return False
        
        try:
            import yaml
            
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            # Update for single speaker
            config['data_root'] = 'data/grid_subset_3spk'
            config['batch_size'] = 8  # Smaller batch for single speaker
            config['epochs'] = 30     # More epochs for single speaker
            config['val_split'] = 0.3  # Larger validation split
            
            # Save updated config
            single_speaker_config = Path("sagemaker_training_files/configs/grid_pretrain_s4.yaml")
            with open(single_speaker_config, 'w') as f:
                yaml.dump(config, f, default_flow_style=False)
            
            logger.info(f"Created single-speaker config: {single_speaker_config}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating config: {e}")
            return False
    
    def run_grid_pretraining(self) -> bool:
        """Execute GRID pretraining."""
        
        logger.info("Starting GRID pretraining...")
        
        # Change to sagemaker directory
        sagemaker_dir = Path("sagemaker_training_files")
        
        if not sagemaker_dir.exists():
            logger.error(f"SageMaker directory not found: {sagemaker_dir}")
            return False
        
        try:
            # Run GRID pretraining
            training_script = sagemaker_dir / "training" / "train_grid_pretrain.py"
            config_file = sagemaker_dir / "configs" / "grid_pretrain_s4.yaml"
            
            if not training_script.exists():
                logger.error(f"Training script not found: {training_script}")
                return False
            
            cmd = [
                sys.executable, str(training_script),
                "--config", str(config_file)
            ]
            
            logger.info(f"Running GRID pretraining: {' '.join(cmd)}")
            
            # Run training with real-time output
            process = subprocess.Popen(
                cmd, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                cwd=str(sagemaker_dir)
            )
            
            # Stream output
            for line in process.stdout:
                print(line.rstrip())
            
            process.wait()
            
            if process.returncode == 0:
                logger.info("GRID pretraining completed successfully!")
                return True
            else:
                logger.error(f"GRID pretraining failed with return code: {process.returncode}")
                return False
                
        except Exception as e:
            logger.error(f"Error running GRID pretraining: {e}")
            return False

def main():
    """Main execution function."""
    
    print("🎬 GRID Speaker S4 Processing Pipeline")
    print("=" * 60)
    
    processor = GRIDProcessor()
    
    # Step 1: Analyze GRID s4 data
    print("\n📊 Step 1: Analyzing GRID s4 data...")
    word_groups = processor.analyze_grid_s4_data()
    
    if not word_groups:
        print("❌ No matching words found in GRID s4 data")
        return False
    
    # Step 2: Organize data
    print("\n📁 Step 2: Organizing GRID data...")
    if not processor.organize_grid_data(word_groups):
        print("❌ Failed to organize GRID data")
        return False
    
    # Step 3: Preprocess videos
    print("\n🔧 Step 3: Preprocessing videos...")
    if not processor.preprocess_videos():
        print("❌ Failed to preprocess videos")
        return False
    
    # Step 4: Create manifest
    print("\n📋 Step 4: Creating manifest...")
    if not processor.create_grid_manifest():
        print("❌ Failed to create manifest")
        return False
    
    # Step 5: Update config
    print("\n⚙️  Step 5: Updating config for single speaker...")
    if not processor.update_grid_config_for_single_speaker():
        print("❌ Failed to update config")
        return False
    
    # Step 6: Run pretraining
    print("\n🚂 Step 6: Running GRID pretraining...")
    if not processor.run_grid_pretraining():
        print("❌ GRID pretraining failed")
        return False
    
    print("\n🎉 GRID Speaker S4 Processing Complete!")
    print("✅ Trained encoder ready for ICU fine-tuning")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
