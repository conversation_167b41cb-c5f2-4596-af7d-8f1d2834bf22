#!/usr/bin/env python3
"""
Test the fixed smart processing on a single video
"""

import sys
from pathlib import Path
sys.path.append('scripts')

from smart import smart_process_video

def test_single_video():
    """Test processing a single video with the fixed function"""
    
    # Test with the same video we debugged
    input_video = Path("speaker_sets/partial_speaker_sets_top7/speaker 9 volunteer lady green shirt 6 done/my_back_hurts/my_back_hurts__useruser01__40to64__female__caucasian__20250716T055041.mp4")
    output_video = Path("test clips/FIXED_test_video.mp4")
    
    print("🧪 TESTING FIXED SMART PROCESSING")
    print("=" * 50)
    print(f"Input: {input_video}")
    print(f"Output: {output_video}")
    print()
    
    if not input_video.exists():
        print(f"❌ Input video not found: {input_video}")
        return
    
    # Process the video
    success = smart_process_video(input_video, output_video)
    
    if success:
        print("✅ Processing completed successfully!")
        
        # Check the result
        import cv2
        cap = cv2.VideoCapture(str(output_video))
        ret, frame = cap.read()
        
        if ret:
            print(f"📹 Output video shape: {frame.shape}")
            print(f"📹 Output video dtype: {frame.dtype}")
            print(f"📹 Is grayscale: {len(frame.shape) == 2}")
            
            # Save frame for inspection
            cv2.imwrite("FIXED_output_frame.png", frame)
            print("💾 Saved output frame as FIXED_output_frame.png")
            
        cap.release()
        
    else:
        print("❌ Processing failed!")

if __name__ == "__main__":
    test_single_video()
