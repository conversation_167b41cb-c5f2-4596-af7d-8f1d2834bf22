#!/usr/bin/env python3
"""
Test different crop coordinates to find where lips actually are
"""

import cv2
import numpy as np
from pathlib import Path

def test_crop_coordinates():
    """Test different crop coordinates to find lips"""
    
    # Test with a 400x200 video first
    video_path = "speaker_sets/partial_speaker_sets_top7/speaker 9 volunteer lady green shirt 6 done/my_back_hurts/my_back_hurts__useruser01__40to64__female__caucasian__20250716T055041.mp4"
    
    if not Path(video_path).exists():
        print(f"❌ Video not found: {video_path}")
        return
    
    print("🔍 TESTING DIFFERENT CROP COORDINATES FOR LIPS")
    print("=" * 60)
    print(f"Video: {Path(video_path).name}")
    
    cap = cv2.VideoCapture(video_path)
    ret, frame = cap.read()
    
    if not ret:
        print("❌ Could not read video")
        return
    
    h, w = frame.shape[:2]
    print(f"Dimensions: {w}×{h}")
    
    # Test different crop regions systematically
    test_regions = [
        # Format: (name, x_start%, x_end%, y_start%, y_end%)
        ("Current (NOT WORKING)", 0.20, 0.80, 0.30, 0.70),
        ("Upper Region", 0.20, 0.80, 0.10, 0.50),
        ("Lower Region", 0.20, 0.80, 0.50, 0.90),
        ("Top Quarter", 0.20, 0.80, 0.00, 0.40),
        ("Bottom Quarter", 0.20, 0.80, 0.60, 1.00),
        ("Very Top", 0.20, 0.80, 0.00, 0.25),
        ("Very Bottom", 0.20, 0.80, 0.75, 1.00),
        ("Center Narrow", 0.25, 0.75, 0.35, 0.65),
        ("Wide Top", 0.10, 0.90, 0.00, 0.40),
        ("Wide Bottom", 0.10, 0.90, 0.60, 1.00),
    ]
    
    print(f"\nTesting {len(test_regions)} different crop regions:")
    print()
    
    for i, (name, x1_pct, x2_pct, y1_pct, y2_pct) in enumerate(test_regions, 1):
        # Calculate coordinates
        crop_x1 = int(w * x1_pct)
        crop_x2 = int(w * x2_pct)
        crop_y1 = int(h * y1_pct)
        crop_y2 = int(h * y2_pct)
        
        print(f"{i:2d}. {name}")
        print(f"    Coordinates: x={crop_x1}-{crop_x2}, y={crop_y1}-{crop_y2}")
        print(f"    Size: {crop_x2-crop_x1}×{crop_y2-crop_y1}")
        
        # Extract the crop
        cropped = frame[crop_y1:crop_y2, crop_x1:crop_x2]
        
        # Save the cropped region
        output_name = f"crop_test_{i:02d}_{name.replace(' ', '_').replace('(', '').replace(')', '')}.png"
        cv2.imwrite(output_name, cropped)
        print(f"    💾 Saved: {output_name}")
        
        # Also create a version showing where this crop is on the original
        frame_marked = frame.copy()
        cv2.rectangle(frame_marked, (crop_x1, crop_y1), (crop_x2, crop_y2), (0, 255, 0), 2)
        cv2.putText(frame_marked, f"{i}. {name}", (crop_x1, crop_y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        marked_name = f"marked_{i:02d}_{name.replace(' ', '_').replace('(', '').replace(')', '')}.png"
        cv2.imwrite(marked_name, frame_marked)
        print(f"    💾 Marked: {marked_name}")
        print()
    
    cap.release()
    
    print("🎯 COORDINATE TESTING COMPLETE!")
    print()
    print("Now check all the crop_test_XX.png files to see which one actually contains the lips!")
    print("Also check the marked_XX.png files to see where each crop region is located.")
    print()
    print("Once you identify which crop region contains the lips, we can update the coordinates.")

if __name__ == "__main__":
    test_crop_coordinates()
