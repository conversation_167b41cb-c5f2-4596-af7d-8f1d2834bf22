#!/usr/bin/env python3
"""
Test GRID Preprocessing Pipeline
================================

Test the GRID preprocessing pipeline on a random video from speaker_1.
"""

import sys
import random
import cv2
import base64
from pathlib import Path

# Add tools directory to path
sys.path.append('tools/sagemaker_training_29.9.25')
from grid_preprocessing_pipeline_corrected_saved import GRIDPreprocessingPipelineCorrected

def create_verification_html(input_path, output_path, result):
    """Create HTML to verify the processing results."""
    
    html_content = f'''<!DOCTYPE html>
<html>
<head>
    <title>🎯 GRID Preprocessing Test Results</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background: #f0f9ff; }}
        .header {{ background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; padding: 25px; border-radius: 12px; margin-bottom: 25px; }}
        .result-container {{ background: white; padding: 20px; border-radius: 12px; margin-bottom: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }}
        .success {{ border-left: 5px solid #10b981; }}
        .failed {{ border-left: 5px solid #ef4444; }}
        .video-comparison {{ display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }}
        .video-section {{ text-align: center; }}
        .frame-image {{ max-width: 100%; border: 2px solid #e5e7eb; border-radius: 8px; }}
        .stats {{ background: #f9fafb; padding: 15px; border-radius: 8px; margin: 15px 0; }}
        .quality-checks {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 15px 0; }}
        .check-item {{ padding: 10px; border-radius: 6px; text-align: center; }}
        .check-pass {{ background: #d1fae5; color: #065f46; }}
        .check-fail {{ background: #fee2e2; color: #991b1b; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 GRID Preprocessing Pipeline Test</h1>
        <p><strong>Testing:</strong> {input_path.name}</p>
        <p><strong>Status:</strong> {'✅ SUCCESS' if result['success'] else '❌ FAILED'}</p>
    </div>
    
    <div class="result-container {'success' if result['success'] else 'failed'}">
        <h3>📊 Processing Results</h3>
        <div class="stats">
            <p><strong>Input File:</strong> {input_path.name}</p>
            <p><strong>Output File:</strong> {output_path.name if output_path.exists() else 'Not created'}</p>
            <p><strong>Processing Time:</strong> {result['processing_time']:.2f} seconds</p>
            <p><strong>Original Frames:</strong> {result['frame_count_original']}</p>
            <p><strong>Processed Frames:</strong> {result['frame_count_processed']}</p>
            <p><strong>Face Detection Rate:</strong> {result['face_detection_rate']:.1%}</p>
            <p><strong>Lips Already Centered:</strong> {'✅ YES' if result.get('lips_already_centered', False) else '❌ NO'}</p>
            {'<p><strong>Error:</strong> ' + str(result['error']) + '</p>' if result['error'] else ''}
        </div>
        
        {'<div class="quality-checks">' + ''.join([
            f'<div class="check-item check-{"pass" if v else "fail"}">'
            f'<strong>{k.replace("_", " ").title()}:</strong><br>{"✅ PASS" if v else "❌ FAIL"}</div>'
            for k, v in result.get('quality_checks', {}).items()
        ]) + '</div>' if result.get('quality_checks') else ''}
    </div>'''
    
    # Add video comparison if output exists
    if output_path.exists() and result['success']:
        try:
            # Get original frame
            cap_orig = cv2.VideoCapture(str(input_path))
            ret_orig, frame_orig = cap_orig.read()
            cap_orig.release()
            
            # Get processed frame
            cap_proc = cv2.VideoCapture(str(output_path))
            ret_proc, frame_proc = cap_proc.read()
            cap_proc.release()
            
            if ret_orig and ret_proc:
                # Convert to base64
                _, buffer_orig = cv2.imencode('.jpg', frame_orig, [cv2.IMWRITE_JPEG_QUALITY, 95])
                frame_orig_b64 = base64.b64encode(buffer_orig).decode('utf-8')
                
                _, buffer_proc = cv2.imencode('.jpg', frame_proc, [cv2.IMWRITE_JPEG_QUALITY, 95])
                frame_proc_b64 = base64.b64encode(buffer_proc).decode('utf-8')
                
                # Get dimensions
                orig_h, orig_w = frame_orig.shape[:2]
                proc_h, proc_w = frame_proc.shape[:2]
                
                html_content += f'''
    <div class="result-container success">
        <h3>🎬 Video Comparison</h3>
        <div class="video-comparison">
            <div class="video-section">
                <h4>📹 Original Video</h4>
                <img src="data:image/jpeg;base64,{frame_orig_b64}" alt="Original Frame" class="frame-image">
                <p><strong>Resolution:</strong> {orig_w}×{orig_h}</p>
                <p><strong>Channels:</strong> {len(frame_orig.shape)} (Color)</p>
            </div>
            <div class="video-section">
                <h4>✂️ Processed Video</h4>
                <img src="data:image/jpeg;base64,{frame_proc_b64}" alt="Processed Frame" class="frame-image">
                <p><strong>Resolution:</strong> {proc_w}×{proc_h}</p>
                <p><strong>Channels:</strong> {len(frame_proc.shape)} (Grayscale)</p>
            </div>
        </div>
    </div>'''
        except Exception as e:
            html_content += f'''
    <div class="result-container failed">
        <h3>❌ Video Comparison Error</h3>
        <p>Could not extract frames for comparison: {e}</p>
    </div>'''
    
    html_content += '''
    <div class="result-container">
        <h3>📋 GRID Preprocessing Pipeline Summary</h3>
        <p>This test uses the <strong>GRID Preprocessing Pipeline (Corrected Version)</strong> which:</p>
        <ul>
            <li>✅ Extracts mouth ROI from <strong>lower 50%</strong> of detected face</li>
            <li>✅ Applies geometric cropping to <strong>bottom 60%</strong> of ROI</li>
            <li>✅ Converts to grayscale and resizes to <strong>96×64</strong> pixels</li>
            <li>✅ Samples to exactly <strong>32 frames</strong> for consistency</li>
            <li>✅ Includes comprehensive quality validation</li>
        </ul>
        <p><strong>Key Fix:</strong> This corrected version targets the actual mouth region instead of the nose area.</p>
    </div>
</body>
</html>'''
    
    with open('grid_preprocessing_test_results.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Verification HTML created: grid_preprocessing_test_results.html")

def main():
    """Test GRID preprocessing on a random speaker_1 video."""
    print("🎯 GRID PREPROCESSING PIPELINE TEST")
    print("="*50)
    
    # Use the specific video we checked
    input_video = Path("speaker_sets/full_speaker_sets_top7/speaker_1 /glasses/glasses__useruser01__18to39__male__not_specified__20250824T025257.mp4")
    output_dir = Path("test_grid_output")
    output_video = output_dir / "processed_glasses_video.mp4"
    
    if not input_video.exists():
        print(f"❌ Input video not found: {input_video}")
        return
    
    print(f"📹 Input: {input_video.name}")
    print(f"📁 Output: {output_video}")
    print()
    
    # Create output directory
    output_dir.mkdir(exist_ok=True)
    
    # Initialize pipeline
    print("🚀 Initializing GRID preprocessing pipeline...")
    pipeline = GRIDPreprocessingPipelineCorrected(
        target_resolution=(96, 64),
        target_frames=32
    )
    
    # Process the video
    print("⚙️ Processing video...")
    result = pipeline.process_video(input_video, output_video, save_debug=True)
    
    # Print results
    print("\n" + "="*50)
    print("📊 PROCESSING RESULTS")
    print("="*50)
    print(f"✅ Success: {result['success']}")
    print(f"⏱️ Processing Time: {result['processing_time']:.2f}s")
    print(f"📹 Original Frames: {result['frame_count_original']}")
    print(f"🎬 Processed Frames: {result['frame_count_processed']}")
    print(f"👤 Face Detection Rate: {result['face_detection_rate']:.1%}")
    print(f"🎯 Lips Already Centered: {'✅ YES' if result.get('lips_already_centered', False) else '❌ NO'}")

    if result['error']:
        print(f"❌ Error: {result['error']}")
    
    if result.get('quality_checks'):
        print("\n🔍 Quality Checks:")
        for check, passed in result['quality_checks'].items():
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"   {check.replace('_', ' ').title()}: {status}")
    
    # Create verification HTML
    print("\n🌐 Creating verification browser...")
    create_verification_html(input_video, output_video, result)
    
    print(f"\n🎉 TEST COMPLETED!")
    print(f"📄 Results: grid_preprocessing_test_results.html")
    print(f"📁 Output video: {output_video}")
    
    if result['success']:
        print("✅ GRID preprocessing pipeline working correctly!")
    else:
        print("❌ Processing failed - check logs for details")

if __name__ == '__main__':
    main()
