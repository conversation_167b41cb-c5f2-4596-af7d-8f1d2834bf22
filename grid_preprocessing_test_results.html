<!DOCTYPE html>
<html>
<head>
    <title>🎯 GRID Preprocessing Test Results</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f0f9ff; }
        .header { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; padding: 25px; border-radius: 12px; margin-bottom: 25px; }
        .result-container { background: white; padding: 20px; border-radius: 12px; margin-bottom: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .success { border-left: 5px solid #10b981; }
        .failed { border-left: 5px solid #ef4444; }
        .video-comparison { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .video-section { text-align: center; }
        .frame-image { max-width: 100%; border: 2px solid #e5e7eb; border-radius: 8px; }
        .stats { background: #f9fafb; padding: 15px; border-radius: 8px; margin: 15px 0; }
        .quality-checks { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 15px 0; }
        .check-item { padding: 10px; border-radius: 6px; text-align: center; }
        .check-pass { background: #d1fae5; color: #065f46; }
        .check-fail { background: #fee2e2; color: #991b1b; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 GRID Preprocessing Pipeline Test</h1>
        <p><strong>Testing:</strong> glasses__useruser01__18to39__male__not_specified__20250824T025257.mp4</p>
        <p><strong>Status:</strong> ❌ FAILED</p>
    </div>
    
    <div class="result-container failed">
        <h3>📊 Processing Results</h3>
        <div class="stats">
            <p><strong>Input File:</strong> glasses__useruser01__18to39__male__not_specified__20250824T025257.mp4</p>
            <p><strong>Output File:</strong> Not created</p>
            <p><strong>Processing Time:</strong> 0.00 seconds</p>
            <p><strong>Original Frames:</strong> 92</p>
            <p><strong>Processed Frames:</strong> 0</p>
            <p><strong>Face Detection Rate:</strong> 0.0%</p>
            <p><strong>Error:</strong> Low face detection rate: 0.00</p>
        </div>
        
        
    </div>
    <div class="result-container">
        <h3>📋 GRID Preprocessing Pipeline Summary</h3>
        <p>This test uses the <strong>GRID Preprocessing Pipeline (Corrected Version)</strong> which:</p>
        <ul>
            <li>✅ Extracts mouth ROI from <strong>lower 50%</strong> of detected face</li>
            <li>✅ Applies geometric cropping to <strong>bottom 60%</strong> of ROI</li>
            <li>✅ Converts to grayscale and resizes to <strong>96×64</strong> pixels</li>
            <li>✅ Samples to exactly <strong>32 frames</strong> for consistency</li>
            <li>✅ Includes comprehensive quality validation</li>
        </ul>
        <p><strong>Key Fix:</strong> This corrected version targets the actual mouth region instead of the nose area.</p>
    </div>
</body>
</html>