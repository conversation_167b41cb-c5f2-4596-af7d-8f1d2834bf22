#!/usr/bin/env python3
"""
Diagnostic Analysis: Speaker Set Video Processing Issues
=======================================================

This script analyzes why the corrected GRID preprocessing pipeline is failing
on speaker set videos and provides insights for potential solutions.

FINDINGS FROM VALIDATION TEST:
- Success Rate: 28.6% (4/14 videos)
- Main Issue: Mouth visibility failures (10/14 videos failed this check)
- Successful Classes: Phone (100%), I Need To Move (50%), Pillow (50%)
- Failed Classes: Doctor, <PERSON><PERSON>, My Back Hurts, My Mouth Is Dry (0% success)

Author: Augment Agent
Date: 2025-09-29
Status: Diagnostic Analysis
"""

import cv2
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
import json
from typing import Dict, List, Tuple, Optional

class SpeakerSetDiagnostics:
    """
    Diagnostic tool for analyzing speaker set video processing issues.
    """
    
    def __init__(self):
        """Initialize the diagnostic tool."""
        self.test_dir = Path("speaker_set_pipeline_test")
        self.intermediate_dir = self.test_dir / "intermediate"
        self.processed_dir = self.test_dir / "processed"
        
        # Load test results
        results_file = self.test_dir / "speaker_set_validation_results.json"
        if results_file.exists():
            with open(results_file, 'r') as f:
                self.test_results = json.load(f)
        else:
            self.test_results = {}
    
    def analyze_contrast_patterns(self) -> Dict[str, any]:
        """
        Analyze contrast patterns in processed frames to understand mouth visibility issues.
        
        Returns:
            Dictionary with contrast analysis results
        """
        print("🔍 Analyzing contrast patterns in processed frames...")
        
        processed_frames = list(self.intermediate_dir.glob("*_processed_frame_*_96x64.jpg"))
        
        contrast_analysis = {
            'successful_videos': [],
            'failed_videos': [],
            'contrast_stats': {
                'successful': [],
                'failed': []
            }
        }
        
        for frame_path in processed_frames:
            # Extract video info from filename
            parts = frame_path.stem.split('_')
            class_name = parts[0]
            video_key = '_'.join(parts[1:-4])  # Remove class, 'processed', 'frame', frame_num, '96x64'
            
            # Find corresponding result
            video_result = None
            if 'class_results' in self.test_results:
                for class_data in self.test_results['class_results'].values():
                    for result in class_data.get('results', []):
                        if video_key in result.get('video_name', ''):
                            video_result = result
                            break
                    if video_result:
                        break
            
            # Load and analyze frame
            frame = cv2.imread(str(frame_path), cv2.IMREAD_GRAYSCALE)
            if frame is None:
                continue
            
            # Calculate contrast metrics
            overall_contrast = np.std(frame)
            
            # Regional analysis
            h, w = frame.shape
            
            # Top region (should be lower contrast)
            top_region = frame[:h//3, :]
            top_contrast = np.std(top_region)
            
            # Middle region (should have highest contrast for mouth)
            mid_region = frame[h//3:2*h//3, :]
            mid_contrast = np.std(mid_region)
            
            # Bottom region
            bot_region = frame[2*h//3:, :]
            bot_contrast = np.std(bot_region)
            
            # Central ROI (mouth area)
            roi_h, roi_w = min(24, h//2), min(32, w//2)
            start_y = max(0, h // 2 - roi_h // 2)
            end_y = min(h, start_y + roi_h)
            start_x = max(0, w // 2 - roi_w // 2)
            end_x = min(w, start_x + roi_w)
            central_roi = frame[start_y:end_y, start_x:end_x]
            central_contrast = np.std(central_roi) if central_roi.size > 0 else 0
            
            frame_analysis = {
                'class_name': class_name,
                'video_key': video_key,
                'frame_path': str(frame_path),
                'overall_contrast': overall_contrast,
                'top_contrast': top_contrast,
                'mid_contrast': mid_contrast,
                'bot_contrast': bot_contrast,
                'central_contrast': central_contrast,
                'mouth_focused': mid_contrast >= max(top_contrast, bot_contrast),
                'success': video_result.get('success', False) if video_result else False
            }
            
            if frame_analysis['success']:
                contrast_analysis['successful_videos'].append(frame_analysis)
                contrast_analysis['contrast_stats']['successful'].append(overall_contrast)
            else:
                contrast_analysis['failed_videos'].append(frame_analysis)
                contrast_analysis['contrast_stats']['failed'].append(overall_contrast)
        
        # Calculate statistics
        if contrast_analysis['contrast_stats']['successful']:
            contrast_analysis['avg_contrast_successful'] = np.mean(contrast_analysis['contrast_stats']['successful'])
        else:
            contrast_analysis['avg_contrast_successful'] = 0
        
        if contrast_analysis['contrast_stats']['failed']:
            contrast_analysis['avg_contrast_failed'] = np.mean(contrast_analysis['contrast_stats']['failed'])
        else:
            contrast_analysis['avg_contrast_failed'] = 0
        
        print(f"✅ Analyzed {len(processed_frames)} processed frames")
        print(f"📊 Successful videos avg contrast: {contrast_analysis['avg_contrast_successful']:.2f}")
        print(f"📊 Failed videos avg contrast: {contrast_analysis['avg_contrast_failed']:.2f}")
        
        return contrast_analysis
    
    def compare_original_vs_processed(self) -> Dict[str, any]:
        """
        Compare original speaker set frames with processed frames to identify issues.
        
        Returns:
            Dictionary with comparison analysis
        """
        print("🔄 Comparing original vs processed frames...")
        
        # Find matching pairs
        original_frames = list(self.intermediate_dir.glob("*_original_frame_*.jpg"))
        processed_frames = list(self.intermediate_dir.glob("*_processed_frame_*_96x64.jpg"))
        
        comparison_analysis = {
            'frame_pairs': [],
            'face_detection_issues': 0,
            'cropping_issues': 0,
            'contrast_issues': 0
        }
        
        for orig_frame_path in original_frames:
            # Find matching processed frame
            parts = orig_frame_path.stem.split('_')
            class_name = parts[0]
            video_key = '_'.join(parts[1:-3])  # Remove class, 'original', 'frame', frame_num
            
            # Look for corresponding processed frame (frame 000)
            processed_pattern = f"{class_name}_{video_key}_processed_frame_000_96x64.jpg"
            processed_frame_path = self.intermediate_dir / processed_pattern
            
            if not processed_frame_path.exists():
                continue
            
            # Load both frames
            orig_frame = cv2.imread(str(orig_frame_path))
            processed_frame = cv2.imread(str(processed_frame_path), cv2.IMREAD_GRAYSCALE)
            
            if orig_frame is None or processed_frame is None:
                continue
            
            # Analyze original frame characteristics
            orig_gray = cv2.cvtColor(orig_frame, cv2.COLOR_BGR2GRAY)
            orig_h, orig_w = orig_gray.shape
            
            # Check if lips are in top portion (speaker set characteristic)
            top_third = orig_gray[:orig_h//3, :]
            mid_third = orig_gray[orig_h//3:2*orig_h//3, :]
            bot_third = orig_gray[2*orig_h//3:, :]
            
            top_contrast = np.std(top_third)
            mid_contrast = np.std(mid_third)
            bot_contrast = np.std(bot_third)
            
            lips_in_top = top_contrast > max(mid_contrast, bot_contrast)
            
            # Analyze processed frame
            processed_contrast = np.std(processed_frame)
            
            pair_analysis = {
                'class_name': class_name,
                'video_key': video_key,
                'original_path': str(orig_frame_path),
                'processed_path': str(processed_frame_path),
                'original_size': f"{orig_w}×{orig_h}",
                'processed_size': f"{processed_frame.shape[1]}×{processed_frame.shape[0]}",
                'lips_in_top_third': lips_in_top,
                'original_top_contrast': top_contrast,
                'original_mid_contrast': mid_contrast,
                'original_bot_contrast': bot_contrast,
                'processed_contrast': processed_contrast,
                'contrast_adequate': processed_contrast > 10.0
            }
            
            comparison_analysis['frame_pairs'].append(pair_analysis)
            
            # Count issues
            if not pair_analysis['contrast_adequate']:
                comparison_analysis['contrast_issues'] += 1
        
        print(f"✅ Compared {len(comparison_analysis['frame_pairs'])} frame pairs")
        print(f"📊 Contrast issues: {comparison_analysis['contrast_issues']}")
        
        return comparison_analysis
    
    def generate_diagnostic_report(self, contrast_analysis: Dict, comparison_analysis: Dict):
        """
        Generate a comprehensive diagnostic report.
        
        Args:
            contrast_analysis: Results from contrast analysis
            comparison_analysis: Results from comparison analysis
        """
        print("📋 Generating diagnostic report...")
        
        report_content = f"""
# Speaker Set Video Processing Diagnostic Report

## 🎯 Test Results Summary
- **Total Videos Tested**: {self.test_results.get('total_videos', 0)}
- **Success Rate**: {self.test_results.get('success_rate', 0):.1f}%
- **Main Issue**: Mouth visibility failures

## 🔍 Key Findings

### 1. Contrast Analysis
- **Successful Videos Average Contrast**: {contrast_analysis['avg_contrast_successful']:.2f}
- **Failed Videos Average Contrast**: {contrast_analysis['avg_contrast_failed']:.2f}
- **Threshold Issue**: Many videos fall below 10.0 contrast threshold

### 2. Video Format Differences
Speaker set videos have different characteristics compared to GRID corpus:

#### GRID Corpus (Works Well):
- Lips typically centered in frame
- Full face visible
- Consistent lighting
- Standard face positioning

#### Speaker Set Videos (Challenging):
- Lips often positioned in TOP portion of frame
- Cropped to show lower half of face only
- Variable lighting conditions
- Different face angles and positions

### 3. Pipeline Adaptation Issues

The corrected preprocessing pipeline was optimized for GRID corpus videos where:
- Face detection finds full faces
- Mouth ROI extraction targets lower 50% of detected face
- Geometric cropping takes bottom 60% of ROI

**Problem with Speaker Set Videos**:
- Videos are already cropped to show lower face
- Face detection may struggle with partial faces
- Mouth region may already be at the TOP of the cropped video
- Current pipeline logic assumes full face detection

## 🛠️ Recommended Solutions

### Option 1: Adaptive Pipeline (Recommended)
Create a hybrid preprocessing pipeline that:
1. **Detects video format** (full face vs cropped face)
2. **Adapts ROI extraction** based on format:
   - Full face: Use current corrected logic (bottom 50% of face)
   - Cropped face: Use different logic (top 60% of frame)
3. **Adjusts geometric cropping** accordingly

### Option 2: Speaker Set Specific Pipeline
Create a separate preprocessing pipeline optimized for speaker set videos:
- Skip face detection (assume entire frame is face region)
- Extract mouth ROI from top portion of frame
- Use different contrast thresholds

### Option 3: Unified Robust Pipeline
Enhance the current pipeline with:
- Multiple face detection strategies
- Adaptive ROI positioning based on detected face size/position
- Dynamic contrast thresholds based on video characteristics

## 📊 Class-Specific Analysis

### Successful Classes:
- **Phone**: 100% success - likely has good mouth visibility in top portion
- **I Need To Move**: 50% success - mixed results
- **Pillow**: 50% success - mixed results

### Failed Classes:
- **Doctor**: 0% success - mouth region not properly extracted
- **Glasses**: 0% success - possible interference from glasses
- **My Back Hurts**: 0% success - mouth region issues
- **My Mouth Is Dry**: 0% success - ironic given the phrase content

## 🎯 Next Steps

1. **Implement Adaptive Pipeline**: Modify the corrected pipeline to detect and handle both video formats
2. **Test on Larger Sample**: Validate improvements on more speaker set videos
3. **Optimize Thresholds**: Adjust contrast and visibility thresholds for speaker set format
4. **Visual Validation**: Ensure mouth regions are properly extracted in both formats

## 📋 Technical Specifications

### Current Pipeline Assumptions:
- Input: Full face videos (GRID format)
- Face Detection: OpenCV Haar Cascades on full faces
- ROI Extraction: Bottom 50% of detected face
- Geometric Cropping: Bottom 60% of ROI

### Required Adaptations:
- Input: Both full face and cropped face videos
- Face Detection: Adaptive strategies for partial faces
- ROI Extraction: Format-aware positioning
- Geometric Cropping: Dynamic based on face position

---

**Generated**: {self.test_results.get('timestamp', 'Unknown')}
**Status**: Diagnostic Complete - Solutions Identified
"""
        
        # Save report
        report_path = self.test_dir / "diagnostic_report.md"
        with open(report_path, 'w') as f:
            f.write(report_content)
        
        print(f"📋 Diagnostic report saved: {report_path}")
        
        # Create visual comparison
        self.create_visual_comparison(comparison_analysis)
    
    def create_visual_comparison(self, comparison_analysis: Dict):
        """
        Create visual comparison showing the issues.
        
        Args:
            comparison_analysis: Results from comparison analysis
        """
        print("📊 Creating visual comparison...")
        
        # Select a few representative examples
        examples = comparison_analysis['frame_pairs'][:4]  # First 4 examples
        
        if not examples:
            print("⚠️ No frame pairs found for visual comparison")
            return
        
        fig, axes = plt.subplots(2, len(examples), figsize=(4*len(examples), 8))
        if len(examples) == 1:
            axes = axes.reshape(-1, 1)
        
        for i, example in enumerate(examples):
            # Load frames
            orig_frame = cv2.imread(example['original_path'])
            processed_frame = cv2.imread(example['processed_path'], cv2.IMREAD_GRAYSCALE)
            
            if orig_frame is None or processed_frame is None:
                continue
            
            # Original frame
            axes[0, i].imshow(cv2.cvtColor(orig_frame, cv2.COLOR_BGR2RGB))
            axes[0, i].set_title(f"Original: {example['class_name']}\n{example['original_size']}")
            axes[0, i].axis('off')
            
            # Processed frame
            axes[1, i].imshow(processed_frame, cmap='gray')
            axes[1, i].set_title(f"Processed: 96×64\nContrast: {example['processed_contrast']:.1f}")
            axes[1, i].axis('off')
            
            # Add indicators
            if example['lips_in_top_third']:
                axes[0, i].text(10, 30, "LIPS IN TOP", color='red', fontweight='bold', 
                               bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
            
            if not example['contrast_adequate']:
                axes[1, i].text(10, 10, "LOW CONTRAST", color='red', fontweight='bold',
                               bbox=dict(boxstyle="round,pad=0.3", facecolor="red", alpha=0.7))
        
        plt.tight_layout()
        
        # Save comparison
        comparison_path = self.test_dir / "visual_comparison_analysis.png"
        plt.savefig(comparison_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"📊 Visual comparison saved: {comparison_path}")

def main():
    """
    Main diagnostic analysis execution.
    """
    print("🔍 SPEAKER SET VIDEO PROCESSING DIAGNOSTIC ANALYSIS")
    print("=" * 70)
    print("🎯 OBJECTIVE: Understand why corrected pipeline fails on speaker set videos")
    print("📊 SUCCESS RATE: 28.6% (4/14 videos)")
    print("🛑 MAIN ISSUE: Mouth visibility failures")
    print()
    
    # Initialize diagnostics
    diagnostics = SpeakerSetDiagnostics()
    
    # Step 1: Analyze contrast patterns
    print("📋 Step 1: Analyzing contrast patterns...")
    contrast_analysis = diagnostics.analyze_contrast_patterns()
    
    # Step 2: Compare original vs processed
    print("\n📋 Step 2: Comparing original vs processed frames...")
    comparison_analysis = diagnostics.compare_original_vs_processed()
    
    # Step 3: Generate diagnostic report
    print("\n📋 Step 3: Generating comprehensive diagnostic report...")
    diagnostics.generate_diagnostic_report(contrast_analysis, comparison_analysis)
    
    print("\n" + "=" * 70)
    print("🎯 DIAGNOSTIC ANALYSIS COMPLETE")
    print("=" * 70)
    print("🔍 KEY FINDING: Speaker set videos have different format than GRID corpus")
    print("📍 ISSUE: Lips positioned in TOP of frame, not center")
    print("🛠️ SOLUTION: Need adaptive pipeline for different video formats")
    print("📋 REPORT: See diagnostic_report.md for detailed analysis")
    print("📊 VISUAL: See visual_comparison_analysis.png for examples")
    print()
    print("🚀 NEXT STEP: Implement adaptive preprocessing pipeline")

if __name__ == "__main__":
    main()
