#!/usr/bin/env python3
"""
🎯 DEMO BACKEND SERVER - 75.9% Checkpoint Integration
Flask server that uses the restored 75.9% validation accuracy model
for real-time lip-reading predictions via iOS Expo Go app.
"""

import os
import cv2
import torch
import numpy as np
from flask import Flask, request, jsonify
from flask_cors import CORS
from werkzeug.utils import secure_filename
import tempfile
import logging
from datetime import datetime
from pathlib import Path
import socket
import time
import shutil

# CRITICAL: Training-Data-Compatible Preprocessing Pipeline
TRAINING_COMPATIBLE_PREPROCESSING = True
print("🎯 CRITICAL: Training-data-compatible preprocessing pipeline enabled!")

class TrainingCompatibleLipDetector:
    """
    High-precision lip detection that exactly matches training data preprocessing.
    Implements MediaPipe-equivalent landmark detection using OpenCV with precise parameters.
    """
    def __init__(self):
        # Load OpenCV detection models
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        self.mouth_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_smile.xml')

        # CRITICAL: Training data parameters (exact match required)
        self.mouth_y_ratio_start = 0.65  # Mouth starts at 65% down face
        self.mouth_y_ratio_end = 0.88    # Mouth ends at 88% down face
        self.mouth_x_ratio_start = 0.25  # Mouth starts at 25% from left
        self.mouth_x_ratio_end = 0.75    # Mouth ends at 75% from right

        # CRITICAL: 1.3x padding expansion (matches training data)
        self.padding_expansion = 0.30    # 30% expansion = 1.3x total size

    def detect_training_compatible_roi(self, frame):
        """
        Detect mouth ROI using training-data-compatible parameters.
        Returns (x1, y1, x2, y2) bounding box matching training preprocessing.
        """
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        h, w = frame.shape[:2]

        # Step 1: High-precision face detection
        faces = self.face_cascade.detectMultiScale(
            gray,
            scaleFactor=1.05,  # More precise detection
            minNeighbors=6,    # Higher confidence
            minSize=(80, 80),  # Larger minimum face
            flags=cv2.CASCADE_SCALE_IMAGE
        )

        if len(faces) > 0:
            # Use the largest, most centered face
            face_scores = []
            for (fx, fy, fw, fh) in faces:
                area = fw * fh
                center_x, center_y = fx + fw//2, fy + fh//2
                center_distance = abs(center_x - w//2) + abs(center_y - h//2)
                score = area - center_distance * 0.1  # Prefer centered faces
                face_scores.append(score)

            best_face_idx = np.argmax(face_scores)
            x, y, fw, fh = faces[best_face_idx]

            # Step 2: CRITICAL - Extract mouth region using training parameters
            mouth_y1 = y + int(fh * self.mouth_y_ratio_start)
            mouth_y2 = y + int(fh * self.mouth_y_ratio_end)
            mouth_x1 = x + int(fw * self.mouth_x_ratio_start)
            mouth_x2 = x + int(fw * self.mouth_x_ratio_end)

            # Step 3: Refine with mouth detection if possible
            mouth_roi = gray[mouth_y1:mouth_y2, mouth_x1:mouth_x2]
            if mouth_roi.size > 100:  # Minimum size check
                mouths = self.mouth_cascade.detectMultiScale(
                    mouth_roi, 1.1, 3, minSize=(15, 8)
                )

                if len(mouths) > 0:
                    # Use the largest detected mouth for refinement
                    mouth_areas = [mw * mh for (mx, my, mw, mh) in mouths]
                    best_mouth_idx = np.argmax(mouth_areas)
                    mx, my, mw, mh = mouths[best_mouth_idx]

                    # Refine mouth coordinates
                    mouth_x1 += mx
                    mouth_y1 += my
                    mouth_x2 = mouth_x1 + mw
                    mouth_y2 = mouth_y1 + mh

            # Step 4: CRITICAL - Apply 1.3x padding expansion (training data match)
            mouth_w = mouth_x2 - mouth_x1
            mouth_h = mouth_y2 - mouth_y1

            padding_x = int(mouth_w * self.padding_expansion)
            padding_y = int(mouth_h * self.padding_expansion)

            final_x1 = max(0, mouth_x1 - padding_x)
            final_y1 = max(0, mouth_y1 - padding_y)
            final_x2 = min(w, mouth_x2 + padding_x)
            final_y2 = min(h, mouth_y2 + padding_y)

            return (final_x1, final_y1, final_x2, final_y2)

        # CRITICAL: Training-compatible fallback
        fallback_h = int(h * 0.30)  # 30% height
        fallback_w = int(w * 0.45)  # 45% width
        start_y = int(h * 0.60)     # Start at 60% down
        start_x = (w - fallback_w) // 2  # Center horizontally

        return (start_x, start_y, start_x + fallback_w, start_y + fallback_h)

# Import our restored checkpoint loader
from load_75_9_checkpoint import load_checkpoint

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Flask app setup
app = Flask(__name__)
CORS(app)  # Enable CORS for Expo Go requests

# Configuration
UPLOAD_FOLDER = tempfile.mkdtemp()
DEBUG_FOLDER = os.path.join(os.getcwd(), 'debug_uploads')
DEBUG_ROI_FOLDER = os.path.join(os.getcwd(), 'debug_roi')
os.makedirs(DEBUG_FOLDER, exist_ok=True)
os.makedirs(DEBUG_ROI_FOLDER, exist_ok=True)
# CRITICAL: Per-User Calibration Configuration
ENABLE_BIAS_CORRECTION = False
ENABLE_TTA = False
TOPK = 2
TEMPERATURE = 1.5
ALLOWED_EXTENSIONS = {'mp4', 'mov', 'avi', 'webm'}  # Added WebM support for web browsers
MAX_CONTENT_LENGTH = 10 * 1024 * 1024  # 10MB max file size
CONFIDENCE_THRESHOLD = float(os.getenv('CONFIDENCE_THRESHOLD', '0.30'))
MODEL_PATH = os.getenv('MODEL_PATH', './checkpoint_75_9_percent.pth')

# Per-user calibration storage
calibration_data = {}

# EMERGENCY: Reduced reliability thresholds for better acceptance
TAU = {
    "doctor": 0.60,  # Reduced from 0.85
    "my_mouth_is_dry": 0.55,  # Reduced from 0.70
    "i_need_to_move": 0.55,  # Reduced from 0.70
    "pillow": 0.60   # Reduced from 0.70
}
MARGIN_THRESHOLD = 0.15  # Reduced from 0.20
ENTROPY_THRESHOLD = 1.2  # Increased from 1.0

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

# Global model variables
model = None
class_to_idx = None
idx_to_class = None
checkpoint = None

def get_local_ip():
    """Get the local IP address for network connections."""
    try:
        # Connect to a remote address to determine local IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        return "localhost"

def allowed_file(filename):
    """Check if the uploaded file has an allowed extension."""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def load_model():
    """Load the 75.9% checkpoint model."""
    global model, class_to_idx, idx_to_class, checkpoint
    
    try:
        logger.info("Loading 75.9% validation accuracy checkpoint...")
        model, class_to_idx, idx_to_class, checkpoint = load_checkpoint()
        model.eval()
        logger.info(f"✅ Model loaded successfully with {sum(p.numel() for p in model.parameters()):,} parameters")
        logger.info(f"✅ Classes: {list(class_to_idx.keys())}")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to load model: {e}")
        return False

# Initialize training-compatible lip detector
training_lip_detector = TrainingCompatibleLipDetector()

def save_debug_roi_frames(frames, roi_boxes, video_name, max_frames=5):
    """
    Save debug frames showing detected ROI rectangles for visual verification.
    """
    try:
        # Select frames to save (evenly spaced)
        if len(frames) <= max_frames:
            frame_indices = list(range(len(frames)))
        else:
            step = len(frames) // max_frames
            frame_indices = [i * step for i in range(max_frames)]

        for i, frame_idx in enumerate(frame_indices):
            if frame_idx < len(frames) and frame_idx < len(roi_boxes):
                frame = frames[frame_idx].copy()
                roi_box = roi_boxes[frame_idx]

                if roi_box:
                    x1, y1, x2, y2 = roi_box
                    # Draw ROI rectangle in green
                    cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    # Add text label
                    cv2.putText(frame, f"ROI Frame {frame_idx}", (x1, y1-10),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

                # Save debug frame
                debug_filename = f"{video_name}_frame{frame_idx:03d}.png"
                debug_path = os.path.join(DEBUG_ROI_FOLDER, debug_filename)
                cv2.imwrite(debug_path, frame)

        logger.info(f"🔍 Saved {len(frame_indices)} debug ROI frames to {DEBUG_ROI_FOLDER}")
    except Exception as e:
        logger.warning(f"⚠️ Failed to save debug ROI frames: {e}")

        x1 = max(0, x1 - padding_x)
        y1 = max(0, y1 - padding_y)
        x2 = min(w, x2 + padding_x)
        y2 = min(h, y2 + padding_y)

        return (x1, y1, x2, y2)

def extract_temporal_windows(frames, window_size=32):
    """
    Extract 3 temporal windows: start, center, end for Test-Time Augmentation.
    Each window contains exactly 32 contiguous frames.
    """
    total_frames = len(frames)

    if total_frames <= window_size:
        # If video is too short, use the same frames for all windows
        return [frames] * 3

    # Extract 3 windows: start, center, end
    start_window = frames[:window_size]

    center_start = (total_frames - window_size) // 2
    center_window = frames[center_start:center_start + window_size]

    end_window = frames[-window_size:]

    return [start_window, center_window, end_window]

def preprocess_video_with_tta(video_path, target_frames=32):
    """
    CRITICAL: Preprocess video with Test-Time Augmentation (3 temporal windows).
    Returns 3 tensors for start/center/end windows to improve prediction stability.
    Each tensor matches training data preprocessing exactly.
    """
    try:
        logger.info(f"🎬 Processing user video: {video_path}")

        cap = cv2.VideoCapture(str(video_path))

        if not cap.isOpened():
            logger.error(f"❌ Failed to open video file: {video_path}")
            return None

        # Log video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = frame_count / fps if fps > 0 else 0

        logger.info(f"📊 Video info: {frame_count} frames, {fps:.2f} FPS, {duration:.2f}s duration")

        # Detect if this is already a pre-processed mouth crop or full face video
        first_frame_ret, first_frame = cap.read()
        if first_frame_ret:
            h, w = first_frame.shape[:2]
            is_mouth_crop = (h <= 100 and w <= 100) or (frame_count <= 50 and fps <= 10)
            cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # Reset to beginning

            if is_mouth_crop:
                logger.info("🎯 Detected pre-processed mouth crop - using direct processing")
            else:
                logger.info("✅ Using training-compatible lip detection (MediaPipe-equivalent precision)")
        else:
            is_mouth_crop = False
            logger.info("✅ Using enhanced OpenCV lip detection (training-data-compatible)")

        frames = []
        processed_crops = []
        roi_boxes = []  # Track ROI boxes for debug visualization
        detection_count = 0

        # Read all frames and process them
        frame_counter = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break

            frames.append(frame)

            if is_mouth_crop:
                # Already a mouth crop - use entire frame
                h, w = frame.shape[:2]
                full_frame_bbox = (0, 0, w, h)
                roi_boxes.append(full_frame_bbox)
                processed_crops.append(frame)
                detection_count += 1
            else:
                # Use training-compatible lip detection for full face videos
                try:
                    lip_bbox = training_lip_detector.detect_training_compatible_roi(frame)
                    roi_boxes.append(lip_bbox)  # Track for debug visualization

                    if lip_bbox:
                        x1, y1, x2, y2 = lip_bbox
                        lip_crop = frame[y1:y2, x1:x2]
                        if lip_crop.size > 0:
                            processed_crops.append(lip_crop)
                            detection_count += 1
                        else:
                            # Use training-compatible fallback
                            h, w = frame.shape[:2]
                            fallback_bbox = (int(w*0.275), int(h*0.60), int(w*0.725), int(h*0.90))
                            roi_boxes[-1] = fallback_bbox  # Update debug info
                            fallback_crop = frame[int(h*0.60):int(h*0.90), int(w*0.275):int(w*0.725)]
                            processed_crops.append(fallback_crop)
                            detection_count += 1
                    else:
                        # Use training-compatible fallback
                        h, w = frame.shape[:2]
                        fallback_bbox = (int(w*0.275), int(h*0.60), int(w*0.725), int(h*0.90))
                        roi_boxes[-1] = fallback_bbox  # Update debug info
                        fallback_crop = frame[int(h*0.60):int(h*0.90), int(w*0.275):int(w*0.725)]
                        processed_crops.append(fallback_crop)
                        detection_count += 1
                except Exception as e:
                    logger.warning(f"⚠️ Training-compatible lip detection failed on frame {frame_counter}: {e}")
                    # Use training-compatible fallback
                    h, w = frame.shape[:2]
                    fallback_bbox = (int(w*0.275), int(h*0.60), int(w*0.725), int(h*0.90))
                    roi_boxes.append(fallback_bbox)  # Add debug info
                    fallback_crop = frame[int(h*0.60):int(h*0.90), int(w*0.275):int(w*0.725)]
                    processed_crops.append(fallback_crop)
                    detection_count += 1

            frame_counter += 1
            if frame_counter > 1000:  # Safety limit
                break

        cap.release()

        logger.info(f"✅ Extracted {len(frames)} frames, {detection_count} lip detections")

        # Save debug ROI frames for visual verification
        video_name = Path(video_path).stem
        save_debug_roi_frames(frames, roi_boxes, video_name)

        if detection_count == 0:
            logger.error("❌ No lip regions detected in video")
            return None

        # Filter out frames without detections and use valid crops
        valid_crops = [crop for crop in processed_crops if crop is not None]

        if len(valid_crops) == 0:
            logger.error("❌ No valid lip crops found")
            return None

        # CRITICAL: Use 32 contiguous center frames (training data match)
        if len(valid_crops) >= target_frames:
            # Extract 32 contiguous frames from center of video
            start_idx = (len(valid_crops) - target_frames) // 2
            sampled_crops = valid_crops[start_idx:start_idx + target_frames]
            logger.info(f"🎯 CRITICAL: Using 32 contiguous center frames ({start_idx} to {start_idx + target_frames - 1})")
        else:
            # Pad with repeated frames if needed
            sampled_crops = valid_crops[:]
            while len(sampled_crops) < target_frames:
                sampled_crops.append(valid_crops[-1])
            logger.info(f"🎯 CRITICAL: Padded to 32 frames (original: {len(valid_crops)})")

        # Process crops: resize to 64x96, convert to grayscale, normalize
        processed_frames = []
        for crop in sampled_crops:
            # Resize to 64x96 (height x width) - CRITICAL training data match
            resized = cv2.resize(crop, (96, 64))  # cv2.resize uses (width, height)

            # Convert to grayscale - CRITICAL training data match
            gray = cv2.cvtColor(resized, cv2.COLOR_BGR2GRAY)

            # Normalize to [0,1] - CRITICAL training data match
            normalized = gray.astype(np.float32) / 255.0
            processed_frames.append(normalized)

        # Convert to tensor format
        video_tensor = np.array(processed_frames)  # Shape: (32, 64, 96)
        video_tensor = video_tensor[np.newaxis, np.newaxis, ...]  # Shape: (1, 1, 32, 64, 96)

        # Log tensor statistics
        tensor_mean = np.mean(video_tensor)
        tensor_std = np.std(video_tensor)
        tensor_checksum = np.sum(video_tensor)
        logger.info(f"🔢 Tensor stats: mean={tensor_mean:.4f}, std={tensor_std:.4f}, checksum={tensor_checksum:.2f}")

        logger.info(f"👄 Successfully processed {detection_count}/{len(frames)} frames with lip detection")

        return torch.FloatTensor(video_tensor)  # Return single tensor

    except Exception as e:
        logger.error(f"Error preprocessing video: {e}")
        return None

def predict_video_calibrated(video_tensor, temperature=1.5):
    """
    CRITICAL: Make prediction with per-user calibration and reliability gate.
    Single-window processing with calibration bias correction.
    """
    try:
        logger.info(f"🎯 CRITICAL: Single-window prediction with calibration (T={temperature})")
        logger.info(f"🧠 Model input tensor shape: {video_tensor.shape}")

        # Get raw logits from model
        with torch.no_grad():
            output = model(video_tensor)
            raw_logits = output.detach().numpy().flatten()
            logger.info(f"🔢 Raw model logits: {raw_logits}")

        # Apply per-user calibration if available
        if len(calibration_data) == 4:
            logger.info("🎯 CRITICAL: Applying per-user calibration")
            calibrated_logits = apply_calibration(raw_logits)
            logger.info(f"🔧 Calibrated logits: {calibrated_logits}")
        else:
            calibrated_logits = raw_logits.copy()
            logger.info(f"⚠️ No calibration data available ({len(calibration_data)}/4 classes)")

        # Apply temperature scaling
        temperature_scaled_logits = calibrated_logits / temperature
        logger.info(f"🌡️ Temperature-scaled logits (T={temperature}): {temperature_scaled_logits}")

        # Convert to probabilities
        temperature_scaled_logits_tensor = torch.FloatTensor(temperature_scaled_logits).unsqueeze(0)
        probabilities = torch.softmax(temperature_scaled_logits_tensor, dim=1)
        final_probs = probabilities.detach().numpy().flatten()

        logger.info(f"📊 Final probabilities: {final_probs}")

        # Get top 2 predictions
        top2_probs, top2_indices = torch.topk(probabilities, TOPK, dim=1)

        results = []
        for i in range(TOPK):
            class_idx = top2_indices[0, i].item()
            confidence = top2_probs[0, i].item()
            class_name = idx_to_class[class_idx]
            results.append({
                "class": class_name,
                "confidence": float(confidence)
            })

        # Apply reliability gate
        reliability_result = apply_reliability_gate(results, final_probs)

        logger.info(f"🎯 CRITICAL Calibrated Results:")
        logger.info(f"   - Temperature scaling: {temperature}")
        logger.info(f"   - Calibration applied: {len(calibration_data) == 4}")
        logger.info(f"   - Top prediction: {results[0]['class']} ({results[0]['confidence']:.3f})")
        logger.info(f"   - Reliability: {reliability_result['reliable']}")

        response = {
            "success": True,
            "top2": results,
            "model_info": "75.9% Validation Accuracy Checkpoint (Per-User Calibration)",
            "temperature": temperature,
            "calibrated": len(calibration_data) == 4,
            "raw_logits": raw_logits.tolist(),
            "calibrated_logits": calibrated_logits.tolist(),
            "final_probabilities": final_probs.tolist(),
            **reliability_result
        }

        return response

    except Exception as e:
        logger.error(f"Error making prediction: {e}")
        return None

def apply_calibration(raw_logits):
    """Apply per-user calibration bias correction."""
    calibrated_logits = raw_logits.copy()

    # Calculate per-class biases
    class_names = ['my_mouth_is_dry', 'i_need_to_move', 'doctor', 'pillow']
    for i, class_name in enumerate(class_names):
        if class_name in calibration_data:
            # Calculate bias: calibration_logits[c] - mean(calibration_logits[others])
            cal_logit = calibration_data[class_name][i]  # This class's calibration logit
            other_logits = [calibration_data[other_class][i] for other_class in class_names if other_class != class_name and other_class in calibration_data]

            if other_logits:
                mean_others = np.mean(other_logits)
                bias = cal_logit - mean_others
                # Clamp bias to prevent extreme corrections
                clamped_bias = np.clip(bias, -0.4, 0.4)
                calibrated_logits[i] += clamped_bias
                logger.info(f"   - {class_name}: bias={bias:.3f}, clamped={clamped_bias:.3f}")

    return calibrated_logits

def apply_reliability_gate(results, probabilities):
    """Apply reliability gate with accept/reject decision."""
    top1_class = results[0]['class']
    top1_conf = results[0]['confidence']
    top2_conf = results[1]['confidence'] if len(results) > 1 else 0.0

    # Calculate entropy
    entropy = -np.sum([p * np.log(p + 1e-8) for p in probabilities if p > 1e-8])

    # Check reliability criteria
    threshold_check = top1_conf >= TAU[top1_class]
    margin_check = (top1_conf - top2_conf) >= MARGIN_THRESHOLD
    entropy_check = entropy <= ENTROPY_THRESHOLD

    reliable = threshold_check and margin_check and entropy_check

    logger.info(f"🔍 Reliability Gate:")
    logger.info(f"   - Threshold check: {top1_conf:.3f} >= {TAU[top1_class]} = {threshold_check}")
    logger.info(f"   - Margin check: {top1_conf - top2_conf:.3f} >= {MARGIN_THRESHOLD} = {margin_check}")
    logger.info(f"   - Entropy check: {entropy:.3f} <= {ENTROPY_THRESHOLD} = {entropy_check}")
    logger.info(f"   - Overall reliable: {reliable}")

    if reliable:
        return {
            "prediction": top1_class,
            "confidence": float(top1_conf),
            "reliable": True,
            "entropy": float(entropy)
        }
    else:
        return {
            "prediction": "unsure",
            "top2": results,
            "reliable": False,
            "entropy": float(entropy),
            "hint": "Keep lips centered in the box, steady lighting"
        }

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({
        "status": "healthy",
        "model_loaded": model is not None,
        "classes": list(class_to_idx.keys()) if class_to_idx else [],
        "confidence_threshold": CONFIDENCE_THRESHOLD,
        "model_parameters": sum(p.numel() for p in model.parameters()) if model else 0,
        "server_info": "75.9% Checkpoint Demo Server"
    })

@app.route('/predict', methods=['POST'])
def predict():
    """Main prediction endpoint for video files."""
    try:
        logger.info("🎯 Received prediction request")

        # Check if model is loaded
        if model is None:
            logger.error("❌ Model not loaded")
            return jsonify({
                "error": "Model not loaded",
                "success": False
            }), 500
        
        # Check if file is present
        if 'video' not in request.files:
            return jsonify({
                "error": "No video file provided",
                "success": False
            }), 400
        
        file = request.files['video']
        
        # Check if file is selected
        if file.filename == '':
            return jsonify({
                "error": "No file selected",
                "success": False
            }), 400
        
        # Check file type
        logger.info(f"📁 Received file: {file.filename}")
        if not allowed_file(file.filename):
            logger.error(f"❌ File type not allowed: {file.filename}")
            return jsonify({
                "error": f"File type not allowed. Supported: {', '.join(ALLOWED_EXTENSIONS)}",
                "success": False
            }), 400

        # Save uploaded file
        filename = secure_filename(file.filename)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{timestamp}_{filename}"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        logger.info(f"💾 Saving file to: {filepath}")
        file.save(filepath)

        # Check file size
        file_size = os.path.getsize(filepath)
        logger.info(f"📊 File size: {file_size / 1024:.1f} KB ({file_size} bytes)")

        # Copy to debug folder for inspection
        debug_filepath = os.path.join(DEBUG_FOLDER, f"latest_{filename}")
        import shutil
        shutil.copy2(filepath, debug_filepath)
        logger.info(f"🔍 Debug copy saved: {debug_filepath}")

        logger.info(f"Processing video: {filename}")

        # CRITICAL: Preprocess video with single-window processing
        start_time = time.time()
        video_tensor = preprocess_video_with_tta(filepath)
        if video_tensor is None:
            return jsonify({
                "error": "Failed to preprocess video",
                "success": False
            }), 400

        # CRITICAL: Make prediction with per-user calibration
        prediction_result = predict_video_calibrated(video_tensor, temperature=TEMPERATURE)
        processing_time = time.time() - start_time
        logger.info(f"⏱️ Total processing time: {processing_time:.2f}s")

        if prediction_result is None:
            return jsonify({
                "error": "Failed to make prediction",
                "success": False
            }), 500

        # Log prediction results
        logger.info(f"🎯 Top predictions: {prediction_result['top2'][0]['class']} ({prediction_result['top2'][0]['confidence']:.3f}), {prediction_result['top2'][1]['class']} ({prediction_result['top2'][1]['confidence']:.3f})")
        
        # Clean up uploaded file
        try:
            os.remove(filepath)
        except:
            pass
        
        # Log prediction
        logger.info(f"Prediction: {prediction_result['top2'][0]['class']} "
                   f"({prediction_result['top2'][0]['confidence']:.3f})")
        
        # Return results
        # Return results based on reliability
        response = {
            "success": True,
            "timestamp": datetime.now().isoformat(),
            "model_info": "75.9% Validation Accuracy Checkpoint (Per-User Calibration)",
            **prediction_result
        }

        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Error in prediction endpoint: {e}")
        return jsonify({
            "error": f"Server error: {str(e)}",
            "success": False
        }), 500

@app.route('/calibrate', methods=['POST'])
def calibrate():
    """Per-user calibration endpoint for 4-shot calibration."""
    try:
        logger.info("🎯 Received calibration request")

        # Check if model is loaded
        if model is None:
            logger.error("❌ Model not loaded")
            return jsonify({
                "error": "Model not loaded",
                "success": False
            }), 500

        # Check if file and class are present
        if 'video' not in request.files:
            return jsonify({
                "error": "No video file provided",
                "success": False
            }), 400

        if 'class' not in request.form:
            return jsonify({
                "error": "No class parameter provided",
                "success": False
            }), 400

        file = request.files['video']
        class_name = request.form['class']

        # Validate class name
        valid_classes = ['my_mouth_is_dry', 'i_need_to_move', 'doctor', 'pillow']
        if class_name not in valid_classes:
            return jsonify({
                "error": f"Invalid class. Must be one of: {valid_classes}",
                "success": False
            }), 400

        # Check if file is selected
        if file.filename == '':
            return jsonify({
                "error": "No file selected",
                "success": False
            }), 400

        # Check file type
        logger.info(f"📁 Calibration file: {file.filename} for class: {class_name}")
        if not allowed_file(file.filename):
            logger.error(f"❌ File type not allowed: {file.filename}")
            return jsonify({
                "error": f"File type not allowed. Supported: {', '.join(ALLOWED_EXTENSIONS)}",
                "success": False
            }), 400

        # Save uploaded file
        filename = secure_filename(file.filename)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"cal_{class_name}_{timestamp}_{filename}"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        logger.info(f"💾 Saving calibration file to: {filepath}")
        file.save(filepath)

        # Preprocess video
        start_time = time.time()
        video_tensor = preprocess_video_with_tta(filepath)
        if video_tensor is None:
            return jsonify({
                "error": "Failed to preprocess calibration video",
                "success": False
            }), 400

        # Get raw logits for calibration
        with torch.no_grad():
            output = model(video_tensor)
            raw_logits = output.detach().numpy().flatten()
            logger.info(f"🔧 Calibration logits for {class_name}: {raw_logits}")

        # Store calibration data
        calibration_data[class_name] = raw_logits.tolist()
        processing_time = time.time() - start_time

        logger.info(f"✅ Calibration stored for {class_name}")
        logger.info(f"📊 Calibration progress: {len(calibration_data)}/4 classes")

        # Clean up uploaded file
        try:
            os.remove(filepath)
        except:
            pass

        return jsonify({
            "success": True,
            "class": class_name,
            "logits": raw_logits.tolist(),
            "message": f"Calibration stored for {class_name}",
            "progress": f"{len(calibration_data)}/4 classes calibrated",
            "processing_time": f"{processing_time:.2f}s"
        })

    except Exception as e:
        logger.error(f"Error in calibration endpoint: {e}")
        return jsonify({
            "error": f"Calibration error: {str(e)}",
            "success": False
        }), 500

@app.route('/test', methods=['GET'])
def test_endpoint():
    """Test endpoint with dummy prediction."""
    if model is None:
        return jsonify({
            "error": "Model not loaded",
            "success": False
        }), 500
    
    # Create dummy input for testing
    dummy_input = torch.randn(1, 1, 32, 64, 96)
    
    try:
        prediction_result = predict_video(dummy_input)
        return jsonify({
            "success": True,
            "test_mode": True,
            "top2": prediction_result["top2"],
            "abstain": prediction_result["abstain"],
            "message": "Model is working correctly"
        })
    except Exception as e:
        return jsonify({
            "error": f"Model test failed: {str(e)}",
            "success": False
        }), 500

if __name__ == '__main__':
    print("🎯 DEMO BACKEND SERVER - 75.9% Checkpoint")
    print("=" * 50)
    
    # Load the model
    if not load_model():
        print("❌ Failed to load model. Exiting.")
        exit(1)
    
    # Get network info
    local_ip = get_local_ip()
    port = 5000
    
    print(f"✅ Model loaded: Balanced 4-class model (reduced bias)")
    print(f"✅ Classes: {', '.join(class_to_idx.keys())}")
    print(f"✅ Confidence threshold: {CONFIDENCE_THRESHOLD}")
    print(f"✅ Max file size: {MAX_CONTENT_LENGTH // (1024*1024)}MB")
    
    print(f"\n🌐 Server starting on:")
    print(f"   Local: http://localhost:{port}")
    print(f"   Network: http://{local_ip}:{port}")
    
    print(f"\n📱 FOR EXPO GO APP:")
    print(f"   Set EXPO_PUBLIC_API_URL to: http://{local_ip}:{port}")
    
    print(f"\n🧪 Test endpoints:")
    print(f"   Health: http://{local_ip}:{port}/health")
    print(f"   Test: http://{local_ip}:{port}/test")
    
    print(f"\n🚀 Ready for iOS demo app connections!")
    print("=" * 50)
    
    # Start the server
    app.run(
        host='0.0.0.0',  # Allow external connections
        port=port,
        debug=False,  # Disable debug for demo
        use_reloader=False
    )
