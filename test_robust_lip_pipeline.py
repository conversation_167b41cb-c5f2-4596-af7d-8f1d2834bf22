#!/usr/bin/env python3
"""
Test Robust Lip Detection Pipeline - Final Version
==================================================

Tests the ultimate robust lip detection pipeline with comprehensive quality assessment.
"""

import sys
import random
import cv2
import base64
import logging
from pathlib import Path
from typing import List, Dict, Any

# Add src to path
sys.path.append('src')
from preprocessing.robust_lip_detection_pipeline import RobustLipDetectionPipeline

def collect_diverse_test_videos(base_dir: Path, count: int = 12) -> List[Path]:
    """Collect diverse test videos for comprehensive testing."""
    all_videos = []
    
    # Collect all MP4 videos
    for speaker_dir in base_dir.iterdir():
        if speaker_dir.is_dir() and speaker_dir.name.startswith('speaker_'):
            for class_dir in speaker_dir.iterdir():
                if class_dir.is_dir():
                    videos = list(class_dir.glob('*.mp4'))
                    all_videos.extend(videos)
    
    print(f"📁 Found {len(all_videos)} total videos")
    
    # Separate by type and class for diverse testing
    original_videos = [v for v in all_videos if '_topmid' not in v.name.lower()]
    cropped_videos = [v for v in all_videos if '_topmid' in v.name.lower()]
    
    # Group by class
    classes = {}
    for video in all_videos:
        class_name = video.parent.name
        if class_name not in classes:
            classes[class_name] = []
        classes[class_name].append(video)
    
    print(f"   🎬 Original videos: {len(original_videos)}")
    print(f"   ✂️  Cropped videos: {len(cropped_videos)}")
    print(f"   📚 Classes found: {len(classes)}")
    
    # Select diverse mix
    selected = []
    
    # Try to get videos from each class
    for class_name, class_videos in classes.items():
        if len(selected) >= count:
            break
        
        # Get mix of original and cropped from this class
        class_original = [v for v in class_videos if '_topmid' not in v.name.lower()]
        class_cropped = [v for v in class_videos if '_topmid' in v.name.lower()]
        
        if class_original:
            selected.append(random.choice(class_original))
        if class_cropped and len(selected) < count:
            selected.append(random.choice(class_cropped))
    
    # Fill remaining slots randomly
    remaining = count - len(selected)
    if remaining > 0:
        remaining_videos = [v for v in all_videos if v not in selected]
        if remaining_videos:
            selected.extend(random.sample(remaining_videos, min(remaining, len(remaining_videos))))
    
    print(f"🎲 Selected {len(selected)} diverse videos for testing")
    return selected

def create_ultimate_html_browser(results: List[Dict[str, Any]], frame_data: Dict[str, Any], 
                                output_file: Path) -> None:
    """Create ultimate HTML browser with comprehensive quality assessment."""
    
    # Calculate comprehensive statistics
    total_videos = len(results)
    successful = sum(1 for r in results if r['processing_status'] == 'success')
    failed = sum(1 for r in results if r['processing_status'] == 'failed')
    
    # Quality distribution
    excellent = sum(1 for r in results if r.get('quality_category') == 'excellent')
    high = sum(1 for r in results if r.get('quality_category') == 'high')
    medium = sum(1 for r in results if r.get('quality_category') == 'medium')
    low = sum(1 for r in results if r.get('quality_category') == 'low')
    
    # Method and confidence statistics
    method_counts = {}
    confidences = []
    validation_scores = []
    
    for result in results:
        if result['processing_status'] == 'success':
            method = result.get('detection_method', 'unknown')
            method_counts[method] = method_counts.get(method, 0) + 1
            confidences.append(result.get('detection_confidence', 0))
            validation_scores.append(result.get('validation_score', 0))
    
    avg_confidence = sum(confidences) / len(confidences) if confidences else 0
    avg_validation = sum(validation_scores) / len(validation_scores) if validation_scores else 0
    
    html_content = f'''<!DOCTYPE html>
<html>
<head>
    <title>🎯 Robust Lip Detection Pipeline - Ultimate Test Results</title>
    <style>
        body {{ font-family: 'Segoe UI', Arial, sans-serif; margin: 20px; background: #f8fafc; }}
        .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 12px; margin-bottom: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }}
        .stats-section {{ background: white; border: 2px solid #e2e8f0; padding: 20px; border-radius: 12px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }}
        .quality-section {{ background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); border: 2px solid #f6ad55; padding: 20px; border-radius: 12px; margin-bottom: 20px; }}
        .stats-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }}
        .stat-item {{ background: white; padding: 15px; border-radius: 10px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .frames-section {{ background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }}
        .frames-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); gap: 20px; margin-top: 25px; }}
        .frame-item {{ border: 3px solid #e2e8f0; border-radius: 12px; padding: 15px; background: #fafafa; transition: transform 0.2s; }}
        .frame-item:hover {{ transform: translateY(-2px); }}
        .frame-item.excellent {{ border-color: #10b981; background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%); }}
        .frame-item.high {{ border-color: #3b82f6; background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%); }}
        .frame-item.medium {{ border-color: #f59e0b; background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%); }}
        .frame-item.low {{ border-color: #ef4444; background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%); }}
        .frame-title {{ font-size: 13px; font-weight: bold; margin-bottom: 10px; color: #1f2937; }}
        .frame-image {{ width: 100%; border: 2px solid #e5e7eb; border-radius: 8px; }}
        .frame-specs {{ font-size: 11px; color: #6b7280; margin-top: 10px; line-height: 1.5; }}
        .quality-badge {{ display: inline-block; padding: 4px 12px; border-radius: 20px; font-size: 11px; font-weight: bold; margin: 3px; }}
        .quality-excellent {{ background: #10b981; color: white; }}
        .quality-high {{ background: #3b82f6; color: white; }}
        .quality-medium {{ background: #f59e0b; color: white; }}
        .quality-low {{ background: #ef4444; color: white; }}
        .confidence-bar {{ width: 100%; height: 10px; background: #e5e7eb; border-radius: 5px; margin: 6px 0; overflow: hidden; }}
        .confidence-fill {{ height: 100%; border-radius: 5px; transition: width 0.3s; }}
        .confidence-excellent {{ background: linear-gradient(90deg, #10b981, #059669); }}
        .confidence-high {{ background: linear-gradient(90deg, #3b82f6, #2563eb); }}
        .confidence-medium {{ background: linear-gradient(90deg, #f59e0b, #d97706); }}
        .confidence-low {{ background: linear-gradient(90deg, #ef4444, #dc2626); }}
        .method-tag {{ display: inline-block; background: #e0e7ff; color: #3730a3; padding: 3px 8px; border-radius: 6px; font-size: 10px; margin: 2px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 Robust Lip Detection Pipeline - Ultimate Test Results</h1>
        <p><strong>Mission:</strong> Find actual lips with maximum quality and reliability</p>
        <p><strong>Advanced Features:</strong> Multi-method detection, comprehensive validation, adaptive processing</p>
    </div>
    
    <div class="stats-section">
        <h3>📊 Processing Performance</h3>
        <div class="stats-grid">
            <div class="stat-item">
                <h4>Total Videos</h4>
                <p style="font-size: 28px; color: #4c51bf; margin: 8px 0;">{total_videos}</p>
            </div>
            <div class="stat-item">
                <h4>✅ Successful</h4>
                <p style="font-size: 28px; color: #10b981; margin: 8px 0;">{successful}</p>
            </div>
            <div class="stat-item">
                <h4>❌ Failed</h4>
                <p style="font-size: 28px; color: #ef4444; margin: 8px 0;">{failed}</p>
            </div>
            <div class="stat-item">
                <h4>Avg Confidence</h4>
                <p style="font-size: 28px; color: #8b5cf6; margin: 8px 0;">{avg_confidence:.3f}</p>
            </div>
            <div class="stat-item">
                <h4>Avg Validation</h4>
                <p style="font-size: 28px; color: #06b6d4; margin: 8px 0;">{avg_validation:.3f}</p>
            </div>
        </div>
    </div>
    
    <div class="quality-section">
        <h3>🎯 Quality Distribution Analysis</h3>
        <div class="stats-grid">
            <div class="stat-item">
                <h4>🌟 Excellent</h4>
                <p style="font-size: 24px; color: #10b981; margin: 8px 0;">{excellent}</p>
                <p style="font-size: 12px; color: #6b7280;">Confidence ≥ 0.8</p>
            </div>
            <div class="stat-item">
                <h4>🟢 High Quality</h4>
                <p style="font-size: 24px; color: #3b82f6; margin: 8px 0;">{high}</p>
                <p style="font-size: 12px; color: #6b7280;">Confidence 0.6-0.8</p>
            </div>
            <div class="stat-item">
                <h4>🟡 Medium Quality</h4>
                <p style="font-size: 24px; color: #f59e0b; margin: 8px 0;">{medium}</p>
                <p style="font-size: 12px; color: #6b7280;">Confidence 0.4-0.6</p>
            </div>
            <div class="stat-item">
                <h4>🔴 Low Quality</h4>
                <p style="font-size: 24px; color: #ef4444; margin: 8px 0;">{low}</p>
                <p style="font-size: 12px; color: #6b7280;">Confidence < 0.4</p>
            </div>
        </div>
        
        <h4 style="margin-top: 25px;">🔍 Advanced Detection Methods Used:</h4>
        <p>'''
    
    for method, count in method_counts.items():
        html_content += f'<span class="method-tag">{method.replace("_", " ").title()}: {count}</span>'
    
    html_content += f'''
        </p>
    </div>
    
    <div class="frames-section">
        <h3>📹 Comprehensive Lip Detection Results ({len(frame_data)} videos)</h3>
        <p><strong>Quality Assessment:</strong> Each result shows detection confidence, validation score, and comprehensive quality metrics</p>
        
        <div class="frames-grid">'''
    
    # Sort by quality for optimal presentation
    def sort_key(item):
        data = item[1]
        quality_order = {'excellent': 4, 'high': 3, 'medium': 2, 'low': 1}
        return (quality_order.get(data.get('quality_category', 'low'), 0), 
                data.get('detection_confidence', 0))
    
    sorted_items = sorted(frame_data.items(), key=sort_key, reverse=True)
    
    for i, (filename, data) in enumerate(sorted_items, 1):
        quality = data.get('quality_category', 'low')
        confidence = data.get('detection_confidence', 0)
        validation = data.get('validation_score', 0)
        
        # Determine confidence bar style
        if confidence >= 0.8:
            conf_style = 'confidence-excellent'
        elif confidence >= 0.6:
            conf_style = 'confidence-high'
        elif confidence >= 0.4:
            conf_style = 'confidence-medium'
        else:
            conf_style = 'confidence-low'
        
        html_content += f'''
            <div class="frame-item {quality}">
                <div class="frame-title">{i}. {filename[:50]}...</div>
                <img src="data:image/jpeg;base64,{data['frame_b64']}" alt="Frame {i}" class="frame-image">
                <div class="frame-specs">
                    <span class="quality-badge quality-{quality}">{quality.upper()}</span><br>
                    <strong>Method:</strong> {data.get('detection_method', 'unknown').replace('_', ' ').title()}<br>
                    <strong>Detection Confidence:</strong> {confidence:.3f}
                    <div class="confidence-bar">
                        <div class="confidence-fill {conf_style}" style="width: {min(confidence*100, 100)}%"></div>
                    </div>
                    <strong>Validation Score:</strong> {validation:.3f}<br>
                    <strong>Video Type:</strong> {data.get('video_type', 'unknown')}<br>
                    <strong>Resolution:</strong> {data['width']}×{data['height']}<br>
                    <strong>Frames:</strong> {data['frame_count']}<br>
                    <strong>Processing:</strong> {data.get('processing_time', 0):.2f}s<br>
                    <strong>Frames Analyzed:</strong> {data.get('frames_analyzed', 'N/A')}
                </div>
            </div>'''
    
    html_content += '''
        </div>
    </div>
    
    <div class="quality-section">
        <h3>🔍 Ultimate Quality Assessment Guide</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px;">
            <div style="background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%); padding: 20px; border-radius: 12px; border: 3px solid #10b981;">
                <h4 style="color: #065f46;">🌟 Excellent Quality (≥0.8)</h4>
                <p style="font-size: 13px; color: #047857;">Perfect lip detection with high confidence. Optimal for training high-performance models.</p>
            </div>
            <div style="background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%); padding: 20px; border-radius: 12px; border: 3px solid #3b82f6;">
                <h4 style="color: #1e40af;">🟢 High Quality (0.6-0.8)</h4>
                <p style="font-size: 13px; color: #1d4ed8;">Very good lip detection. Suitable for training with minimal quality concerns.</p>
            </div>
            <div style="background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%); padding: 20px; border-radius: 12px; border: 3px solid #f59e0b;">
                <h4 style="color: #92400e;">🟡 Medium Quality (0.4-0.6)</h4>
                <p style="font-size: 13px; color: #b45309;">Acceptable lip detection. Review recommended before training use.</p>
            </div>
            <div style="background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%); padding: 20px; border-radius: 12px; border: 3px solid #ef4444;">
                <h4 style="color: #991b1b;">🔴 Low Quality (<0.4)</h4>
                <p style="font-size: 13px; color: #dc2626;">Poor detection quality. Consider reprocessing or exclusion from training.</p>
            </div>
        </div>
        
        <div style="margin-top: 25px; padding: 20px; background: white; border-radius: 12px; border: 2px solid #e5e7eb;">
            <h4>🎯 Success Metrics Summary</h4>
            <p><strong>Overall Success Rate:</strong> {(successful/total_videos*100):.1f}%</p>
            <p><strong>High+ Quality Rate:</strong> {((excellent+high)/total_videos*100):.1f}%</p>
            <p><strong>Average Detection Confidence:</strong> {avg_confidence:.3f}</p>
            <p><strong>Average Validation Score:</strong> {avg_validation:.3f}</p>
        </div>
    </div>
</body>
</html>'''
    
    # Save HTML file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Ultimate HTML browser created: {output_file.absolute()}")

def extract_comprehensive_frame_data(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Extract frames with comprehensive metadata."""
    frame_data = {}
    
    for result in results:
        if result['processing_status'] == 'success':
            output_path = result['output_path']
            
            try:
                cap = cv2.VideoCapture(output_path)
                if cap.isOpened():
                    ret, frame = cap.read()
                    if ret:
                        # Convert to base64
                        _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 95])
                        frame_b64 = base64.b64encode(buffer).decode('utf-8')
                        
                        # Get properties
                        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                        
                        frame_data[result['source_filename']] = {
                            'frame_b64': frame_b64,
                            'width': width,
                            'height': height,
                            'frame_count': frame_count,
                            'detection_method': result.get('detection_method', 'unknown'),
                            'detection_confidence': result.get('detection_confidence', 0),
                            'validation_score': result.get('validation_score', 0),
                            'quality_category': result.get('quality_category', 'unknown'),
                            'video_type': result.get('video_type', 'unknown'),
                            'processing_time': result.get('processing_time', 0),
                            'frames_analyzed': result.get('frames_analyzed', 'N/A'),
                            'bbox_used': result.get('bbox_used', 'N/A')
                        }
                    cap.release()
            except Exception as e:
                print(f"⚠️  Error extracting frame from {output_path}: {e}")
    
    return frame_data

def main():
    """Main test function for robust pipeline."""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    print("🎯 ROBUST LIP DETECTION PIPELINE - ULTIMATE TEST")
    print("="*60)
    print("🚀 Mission: Find actual lips with maximum quality!")
    print()
    
    # Configuration
    base_dir = Path('speaker_sets/full_speaker_sets_top7')
    output_dir = Path('test_robust_lip_detection_output')
    manifest_path = Path('robust_lip_test_manifest.csv')
    html_output = Path('robust_lip_detection_ultimate_results.html')
    
    # Check input
    if not base_dir.exists():
        print(f"❌ Input directory not found: {base_dir}")
        return
    
    # Collect diverse test videos
    test_videos = collect_diverse_test_videos(base_dir, count=12)
    
    if not test_videos:
        print("❌ No videos found for testing")
        return
    
    # Initialize robust pipeline
    pipeline = RobustLipDetectionPipeline(
        output_dir=output_dir,
        manifest_path=manifest_path
    )
    
    print(f"\n🎬 Processing {len(test_videos)} videos with robust detection...")
    print("-" * 60)
    
    # Process all videos
    results = []
    for i, video_path in enumerate(test_videos, 1):
        print(f"\n[{i}/{len(test_videos)}] Processing: {video_path.name}")
        result = pipeline.process_single_video(video_path)
        results.append(result)
    
    # Save manifest
    pipeline.save_manifest(results)
    
    # Print statistics
    pipeline.print_statistics()
    
    # Extract comprehensive frame data
    print("\n📸 Extracting comprehensive frame data...")
    frame_data = extract_comprehensive_frame_data(results)
    print(f"✅ Extracted {len(frame_data)} frames with full metadata")
    
    # Create ultimate HTML browser
    print("\n🌐 Creating ultimate HTML browser...")
    create_ultimate_html_browser(results, frame_data, html_output)
    
    print(f"\n🎉 ROBUST PIPELINE TEST COMPLETED!")
    print(f"📄 Manifest: {manifest_path.absolute()}")
    print(f"🌐 Ultimate browser: {html_output.absolute()}")
    print(f"📁 Output videos: {output_dir.absolute()}")
    print("\n🔍 Open the HTML browser to see the ultimate lip detection results!")

if __name__ == '__main__':
    main()
