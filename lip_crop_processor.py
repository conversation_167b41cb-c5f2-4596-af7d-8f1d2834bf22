#!/usr/bin/env python3
"""
Lip Crop Processor
==================

Process a single video with proper MediaPipe-based lip detection and tight cropping.
Based on the proven crop_mouth.py implementation.

Specifications:
- Tight lip/mouth region cropping using MediaPipe Face Mesh
- Convert to grayscale
- Resize to 96×64 pixels
- Sample to exactly 32 frames (uniform)
- Save as MP4 at 15 FPS
"""

import cv2
import numpy as np
from pathlib import Path
from typing import List, Tuple, Optional, Dict, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LipCropProcessor:
    """OpenCV-based lip detection and cropping processor for cropped face videos."""

    def __init__(self):
        """Initialize the processor with OpenCV face detection."""
        # OpenCV face detection setup
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

        # Processing parameters (from proven implementation)
        self.EMA_ALPHA = 0.6  # Exponential moving average smoothing
        self.EXPAND_W = 1.4   # Width expansion factor
        self.EXPAND_H = 1.6   # Height expansion factor
        
    def detect_lip_region(self, frame: np.ndarray) -> Tuple[int, int, int, int]:
        """
        Detect lip region targeting the TOP-MIDDLE area of the frame.
        These videos contain lips in the top-middle portion of the frame.

        Args:
            frame: Input frame (BGR format)

        Returns:
            Lip region bounding box as (x1, y1, x2, y2) - always returns a region
        """
        h, w = frame.shape[:2]

        # TARGET: TOP-MIDDLE area where lips are located - CROP HIGHER
        # Focus on the very top portion of the frame and middle width
        lip_x_start = int(w * 0.2)   # Start at 20% from left
        lip_x_end = int(w * 0.8)     # End at 80% from left (middle 60% of width)
        lip_y_start = 0              # Start at TOP EDGE of frame (0% from top)
        lip_y_end = int(h * 0.4)     # End at 40% from top (upper 40% of height)

        return (lip_x_start, lip_y_start, lip_x_end, lip_y_end)
    
    def refine_lip_region_with_edges(self, frame: np.ndarray, initial_bbox: Tuple[int, int, int, int]) -> Tuple[int, int, int, int]:
        """
        Refine lip region using edge detection to find actual mouth boundaries.

        Args:
            frame: Input frame (BGR format)
            initial_bbox: Initial bounding box (x1, y1, x2, y2)

        Returns:
            Refined bounding box as (x1, y1, x2, y2)
        """
        x1, y1, x2, y2 = initial_bbox

        # Extract ROI
        roi = frame[y1:y2, x1:x2]
        if roi.size == 0:
            return initial_bbox

        # Convert to grayscale and apply edge detection
        gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
        blurred = cv2.GaussianBlur(gray_roi, (5, 5), 0)
        edges = cv2.Canny(blurred, 30, 80)

        # Find horizontal edges (lips have strong horizontal features)
        kernel_horizontal = np.array([[-1, -1, -1], [2, 2, 2], [-1, -1, -1]], dtype=np.float32)
        horizontal_edges = cv2.filter2D(blurred, -1, kernel_horizontal)
        horizontal_edges = np.abs(horizontal_edges)

        # Find the region with most edge activity (likely the mouth)
        edge_points = np.where(edges > 0)
        if len(edge_points[0]) > 0:
            # Calculate center of edge activity
            center_y = int(np.mean(edge_points[0]))
            center_x = int(np.mean(edge_points[1]))

            # Create tighter bounding box around edge activity
            roi_h, roi_w = roi.shape[:2]
            tight_w = min(int(roi_w * 0.8), roi_w)
            tight_h = min(int(roi_h * 0.6), roi_h)

            tight_x1 = max(0, center_x - tight_w // 2)
            tight_y1 = max(0, center_y - tight_h // 2)
            tight_x2 = min(roi_w, tight_x1 + tight_w)
            tight_y2 = min(roi_h, tight_y1 + tight_h)

            # Convert back to frame coordinates
            refined_x1 = x1 + tight_x1
            refined_y1 = y1 + tight_y1
            refined_x2 = x1 + tight_x2
            refined_y2 = y1 + tight_y2

            return (refined_x1, refined_y1, refined_x2, refined_y2)

        return initial_bbox
    
    def expand_and_clip_bbox(self, x1: int, y1: int, x2: int, y2: int, 
                           frame_width: int, frame_height: int) -> Tuple[int, int, int, int]:
        """
        Expand bounding box for context and clip to frame boundaries.
        
        Args:
            x1, y1, x2, y2: Original bounding box coordinates
            frame_width, frame_height: Frame dimensions
            
        Returns:
            Expanded and clipped bounding box as (x1, y1, x2, y2)
        """
        # Calculate current dimensions
        width = x2 - x1
        height = y2 - y1
        
        # Expand dimensions
        new_width = int(width * self.EXPAND_W)
        new_height = int(height * self.EXPAND_H)
        
        # Calculate center
        center_x = (x1 + x2) // 2
        center_y = (y1 + y2) // 2
        
        # Calculate new coordinates
        new_x1 = center_x - new_width // 2
        new_y1 = center_y - new_height // 2
        new_x2 = new_x1 + new_width
        new_y2 = new_y1 + new_height
        
        # Clip to frame boundaries
        new_x1 = max(0, new_x1)
        new_y1 = max(0, new_y1)
        new_x2 = min(frame_width, new_x2)
        new_y2 = min(frame_height, new_y2)
        
        return (new_x1, new_y1, new_x2, new_y2)
    
    def apply_ema_smoothing(self, previous_bbox: Optional[Tuple[int, int, int, int]], 
                          current_bbox: Tuple[int, int, int, int]) -> Tuple[int, int, int, int]:
        """
        Apply exponential moving average smoothing to bounding box.
        
        Args:
            previous_bbox: Previous smoothed bounding box or None
            current_bbox: Current bounding box
            
        Returns:
            Smoothed bounding box
        """
        if previous_bbox is None:
            return current_bbox
        
        # Apply EMA smoothing
        x1 = int(self.EMA_ALPHA * current_bbox[0] + (1 - self.EMA_ALPHA) * previous_bbox[0])
        y1 = int(self.EMA_ALPHA * current_bbox[1] + (1 - self.EMA_ALPHA) * previous_bbox[1])
        x2 = int(self.EMA_ALPHA * current_bbox[2] + (1 - self.EMA_ALPHA) * previous_bbox[2])
        y2 = int(self.EMA_ALPHA * current_bbox[3] + (1 - self.EMA_ALPHA) * previous_bbox[3])
        
        return (x1, y1, x2, y2)
    
    def process_video(self, input_path: Path, output_path: Path) -> Dict[str, Any]:
        """
        Process video with lip detection and cropping.
        
        Args:
            input_path: Path to input video
            output_path: Path to save processed video
            
        Returns:
            Processing result dictionary
        """
        logger.info(f"🎬 Processing video with lip cropping: {input_path.name}")
        
        # Load video
        cap = cv2.VideoCapture(str(input_path))
        
        if not cap.isOpened():
            return {'success': False, 'error': f'Cannot open video: {input_path}'}
        
        # Read all frames
        frames = []
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            frames.append(frame)
        
        cap.release()
        
        if not frames:
            return {'success': False, 'error': 'No frames found in video'}
        
        logger.info(f"📹 Loaded {len(frames)} frames, {frames[0].shape[1]}×{frames[0].shape[0]} pixels")
        
        # Detect lip regions in all frames
        lip_bboxes = []
        detection_count = 0

        for frame in frames:
            # Detect lip region in TOP-MIDDLE area (always succeeds)
            initial_bbox = self.detect_lip_region(frame)

            # Refine with edge detection
            refined_bbox = self.refine_lip_region_with_edges(frame, initial_bbox)

            # Expand for context
            h, w = frame.shape[:2]
            expanded_bbox = self.expand_and_clip_bbox(*refined_bbox, w, h)
            lip_bboxes.append(expanded_bbox)
            detection_count += 1
        
        detection_rate = detection_count / len(frames)
        logger.info(f"👄 Lip detection rate: {detection_rate:.1%} ({detection_count}/{len(frames)} frames)")

        # Apply smoothing (all frames have detections now)
        smoothed_bboxes = []
        previous_smoothed = None

        for bbox in lip_bboxes:
            # Apply EMA smoothing
            smoothed_bbox = self.apply_ema_smoothing(previous_smoothed, bbox)
            previous_smoothed = smoothed_bbox
            smoothed_bboxes.append(smoothed_bbox)
        
        # Process frames with lip cropping
        processed_frames = []
        
        for frame, bbox in zip(frames, smoothed_bboxes):
            x1, y1, x2, y2 = bbox
            
            # Extract lip crop
            crop = frame[y1:y2, x1:x2]
            
            # Safety check for empty crop
            if crop.size == 0:
                crop = frame
            
            # Convert to grayscale
            gray = cv2.cvtColor(crop, cv2.COLOR_BGR2GRAY)
            
            # Resize to 96×64
            resized = cv2.resize(gray, (96, 64), interpolation=cv2.INTER_LINEAR)
            
            processed_frames.append(resized)
        
        # Sample to exactly 32 frames (uniform sampling)
        if len(processed_frames) >= 32:
            indices = np.linspace(0, len(processed_frames) - 1, 32, dtype=int)
            sampled_frames = [processed_frames[i] for i in indices]
        else:
            # Pad with repeated frames if too few
            sampled_frames = processed_frames[:]
            while len(sampled_frames) < 32:
                sampled_frames.extend(processed_frames[:32 - len(sampled_frames)])
            sampled_frames = sampled_frames[:32]
        
        logger.info(f"✅ Processed: {len(sampled_frames)} frames, 96×64 pixels, grayscale with lip cropping")
        
        # Create output directory
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Save as MP4 at 15 FPS
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(output_path), fourcc, 15.0, (96, 64), isColor=False)
        
        for frame in sampled_frames:
            out.write(frame)
        
        out.release()
        
        logger.info(f"💾 Saved lip-cropped video: {output_path}")
        
        return {
            'success': True,
            'original_frames': len(frames),
            'processed_frames': len(sampled_frames),
            'detection_rate': detection_rate,
            'output_path': str(output_path)
        }

def main():
    """Main function to process the video with lip cropping."""
    # Input video path
    input_video = Path("speaker_sets/full_speaker_sets_top7/speaker_1 /my_back_hurts/my_back_hurts__useruser01__18to39__male__not_specified__20250722T014409.mp4")
    
    # Output path
    output_video = Path("/Users/<USER>/Desktop/LRP classifier 11.9.25/test clips/lip_cropped_my_back_hurts.mp4")
    
    if not input_video.exists():
        print(f"❌ Error: Input video not found: {input_video}")
        return
    
    print("🎯 LIP CROP PROCESSOR - TOP-MIDDLE TARGETING")
    print("="*60)
    print("Features:")
    print("• Target TOP-MIDDLE area where lips are located")
    print("• Tight lip/mouth region cropping")
    print("• Convert → grayscale")
    print("• Resize → 96×64 pixels")
    print("• Sample to exactly 32 frames (uniform)")
    print("• Save as MP4 at 15 FPS")
    print("="*60)
    
    # Initialize processor
    processor = LipCropProcessor()
    
    # Process the video
    result = processor.process_video(input_video, output_video)
    
    if result['success']:
        print("\n🎉 SUCCESS!")
        print(f"📄 Lip-cropped video: {result['output_path']}")
        print(f"👄 Lip detection rate: {result['detection_rate']:.1%}")
        print(f"📹 Frames: {result['original_frames']} → {result['processed_frames']}")
    else:
        print(f"\n❌ FAILED: {result['error']}")

if __name__ == "__main__":
    main()
