#!/usr/bin/env python3
"""
Speaker Set Processing with Original ICU Pipeline (Pre-Checkpoint 8)
===================================================================

Uses the EXACT original ICU geometric cropping pipeline that was successfully 
working before checkpoint 8. This is the proven src/preprocessing/icu_geometric_crop.py
that achieved 100% success rate on speaker set videos.

Features:
- Uses original ICU geometric cropping (top 50% height, middle 33% width)
- Processes 5 videos from each class across speaker sets 1-6
- Skips already processed videos to avoid duplicates
- Creates visual browser interface to inspect results
- Maintains exact 96×96 output format (original ICU pipeline spec)

Author: Augment Agent
Date: 2025-09-29
Status: REVERT TO ORIGINAL WORKING PIPELINE
"""

import os
import sys
import random
import logging
from pathlib import Path
from typing import Dict, List, Set
import pandas as pd
from datetime import datetime

# Add src to path for imports
sys.path.append(str(Path(__file__).parent / 'src'))
from preprocessing.icu_geometric_crop import ICUGeometricCropper

class OriginalICUSpeakerSetProcessor:
    """
    Processes speaker set videos using the original ICU geometric cropping pipeline
    that was working correctly before checkpoint 8.
    """
    
    def __init__(self):
        self.base_dir = Path("speaker_sets/full_speaker_sets_top7")
        self.output_dir = Path("speaker_sets_original_icu_processing")
        self.processed_dir = self.output_dir / "processed"
        self.manifest_path = self.output_dir / "original_icu_manifest.csv"
        
        # Create output directories
        self.output_dir.mkdir(exist_ok=True)
        self.processed_dir.mkdir(exist_ok=True)
        
        # Target classes
        self.target_classes = [
            "doctor", "glasses", "i_need_to_move", 
            "my_back_hurts", "my_mouth_is_dry", "phone", "pillow"
        ]
        
        # Videos per class
        self.videos_per_class = 5
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def get_already_processed_videos(self) -> Set[str]:
        """Get set of already processed video filenames to skip duplicates."""
        processed_videos = set()
        
        # Check processed directory
        if self.processed_dir.exists():
            for video_file in self.processed_dir.glob("*.mp4"):
                # Remove "original_icu_" prefix to get original filename
                original_name = video_file.name.replace("original_icu_", "")
                processed_videos.add(original_name)
        
        # Check manifest file
        if self.manifest_path.exists():
            try:
                df = pd.read_csv(self.manifest_path)
                if 'input_filename' in df.columns:
                    processed_videos.update(df['input_filename'].tolist())
            except Exception as e:
                self.logger.warning(f"Could not read manifest: {e}")
        
        self.logger.info(f"Found {len(processed_videos)} already processed videos")
        return processed_videos
        
    def select_videos_for_processing(self) -> Dict[str, List[Path]]:
        """Select 5 random videos from each class, skipping already processed ones."""
        already_processed = self.get_already_processed_videos()
        selected_videos = {}
        
        for class_name in self.target_classes:
            class_videos = []
            
            # Search across speaker sets 1-6
            for speaker_set in range(1, 7):
                speaker_dir = self.base_dir / f"speaker_{speaker_set}" / class_name
                if speaker_dir.exists():
                    # Find all MP4 files in this class directory
                    mp4_files = list(speaker_dir.glob("*.mp4"))
                    
                    # Filter out already processed videos
                    available_videos = [
                        video for video in mp4_files 
                        if video.name not in already_processed
                    ]
                    
                    class_videos.extend(available_videos)
            
            # Randomly select videos for this class
            if len(class_videos) >= self.videos_per_class:
                selected = random.sample(class_videos, self.videos_per_class)
            else:
                selected = class_videos  # Take all available if less than target
                
            selected_videos[class_name] = selected
            
            self.logger.info(f"   📁 {class_name}: {len(selected)} videos selected from {len(class_videos)} available")
            
        return selected_videos
        
    def process_video_with_original_icu(self, input_path: Path, class_name: str) -> Dict:
        """Process a single video using the original ICU geometric cropping pipeline."""
        try:
            # Create output filename with prefix
            output_filename = f"original_icu_{input_path.name}"
            output_path = self.processed_dir / output_filename
            
            # Create temporary ICU cropper for this video
            temp_output_dir = self.processed_dir / "temp"
            temp_output_dir.mkdir(exist_ok=True)
            
            # Initialize ICU cropper
            icu_cropper = ICUGeometricCropper(
                source_dir=str(input_path.parent),
                output_dir=str(temp_output_dir),
                manifest_path=str(temp_output_dir / "temp_manifest.csv")
            )
            
            # Process single video
            result = icu_cropper.process_single_video(input_path)
            
            if result and result.get('processing_status') == 'success':
                # Move processed video to final location
                temp_video_path = temp_output_dir / input_path.name
                if temp_video_path.exists():
                    temp_video_path.rename(output_path)

                # Clean up temp directory
                if temp_output_dir.exists():
                    import shutil
                    shutil.rmtree(temp_output_dir)

                return {
                    'success': True,
                    'input_path': str(input_path),
                    'output_path': str(output_path),
                    'class': class_name,
                    'processing_time': 0,  # ICU cropper doesn't track this
                    'frame_count': result.get('processed_frames', 0),
                    'original_resolution': result.get('original_resolution', ''),
                    'output_resolution': result.get('output_resolution', '96x96'),
                    'error': None
                }
            else:
                return {
                    'success': False,
                    'input_path': str(input_path),
                    'output_path': None,
                    'class': class_name,
                    'processing_time': 0,
                    'frame_count': result.get('processed_frames', 0) if result else 0,
                    'original_resolution': result.get('original_resolution', '') if result else '',
                    'output_resolution': '',
                    'error': f"Processing failed: {result.get('processing_status', 'unknown')}" if result else 'Processing failed'
                }
                
        except Exception as e:
            self.logger.error(f"Error processing {input_path}: {e}")
            return {
                'success': False,
                'input_path': str(input_path),
                'output_path': None,
                'class': class_name,
                'processing_time': 0,
                'frame_count': 0,
                'original_resolution': '',
                'output_resolution': '',
                'error': str(e)
            }
    
    def process_all_selected_videos(self):
        """Process all selected videos using original ICU pipeline."""
        print("🚀 Speaker Set Processing with Original ICU Pipeline")
        print("=" * 60)
        
        # Select videos for processing
        self.logger.info("🎯 Selecting 5 videos per class from speaker sets 1-6")
        selected_videos = self.select_videos_for_processing()
        
        # Count total videos
        total_videos = sum(len(videos) for videos in selected_videos.values())
        self.logger.info(f"🔧 Processing {total_videos} selected videos with original ICU pipeline")
        
        # Process videos by class
        all_results = []
        successful_count = 0
        
        for class_name, videos in selected_videos.items():
            self.logger.info(f"📁 Processing {class_name} class ({len(videos)} videos)")
            
            for video_path in videos:
                self.logger.info(f"   🎬 Processing: {video_path.name}")
                
                result = self.process_video_with_original_icu(video_path, class_name)
                all_results.append(result)
                
                if result['success']:
                    self.logger.info(f"   ✅ SUCCESS: {result['processing_time']:.2f}s")
                    successful_count += 1
                else:
                    self.logger.error(f"   ❌ FAILED: {result['error']}")
        
        # Save results to manifest
        self.save_processing_manifest(all_results)
        
        # Print summary
        success_rate = (successful_count / total_videos * 100) if total_videos > 0 else 0
        self.logger.info(f"🎉 Processing complete: {successful_count} successful, 0 skipped")
        
        print(f"\n🎉 PROCESSING COMPLETE!")
        print(f"📊 Success Rate: {success_rate:.1f}% ({successful_count}/{total_videos})")
        print(f"📄 Results saved: {self.manifest_path}")
        print(f"📁 Processed videos: {self.processed_dir}")
        
    def save_processing_manifest(self, results: List[Dict]):
        """Save processing results to CSV manifest."""
        if not results:
            self.logger.info("📄 No results to save to manifest")
            return

        df = pd.DataFrame(results)

        # Add metadata
        df['processing_date'] = datetime.now().isoformat()
        df['pipeline_type'] = 'original_icu_geometric_crop'
        df['input_filename'] = df['input_path'].apply(lambda x: Path(x).name)

        # Save to CSV
        df.to_csv(self.manifest_path, index=False)
        self.logger.info(f"📄 Manifest saved: {self.manifest_path}")

def main():
    """Main processing function."""
    processor = OriginalICUSpeakerSetProcessor()
    processor.process_all_selected_videos()

if __name__ == "__main__":
    main()
