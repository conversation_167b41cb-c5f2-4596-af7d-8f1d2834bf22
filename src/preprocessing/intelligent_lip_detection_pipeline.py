#!/usr/bin/env python3
"""
Intelligent Lip-Detection Preprocessing Pipeline
===============================================

Advanced preprocessing pipeline that intelligently handles mixed datasets containing both
original videos and already-cropped videos. Uses multi-level detection strategy with
MediaPipe Face Mesh, geometric analysis, and fallback methods.

Features:
- Intelligent video classification (original vs already-cropped)
- Multi-level lip detection hierarchy (MediaPipe → Geometric → Fallback)
- Strict output format compliance (96×64, 1 channel grayscale, 32 frames)
- Graceful handling of detection failures
- Comprehensive logging and manifest tracking

Output Requirements:
- Resolution: 96×64 pixels (exact)
- Channels: 1 (true grayscale)
- Frame count: 32 frames (exact)
- Format: MP4 with mouth visibility validation
"""

import cv2
import numpy as np
import logging
import time
import json
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Any
import pandas as pd

try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
except ImportError:
    MEDIAPIPE_AVAILABLE = False
    print("⚠️  MediaPipe not available - using fallback methods only")


class VideoClassifier:
    """Classifies videos as original or already-cropped based on characteristics."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Classification thresholds
        self.SMALL_RESOLUTION_THRESHOLD = 200  # Videos smaller than 200x200 are likely processed
        self.TARGET_FRAME_COUNT = 32  # Exactly 32 frames indicates processing
        self.ASPECT_RATIO_PROCESSED_MIN = 1.2  # Processed videos often have specific aspect ratios
        self.ASPECT_RATIO_PROCESSED_MAX = 2.0
        
    def classify_video(self, video_path: Path) -> Dict[str, Any]:
        """
        Classify video as original or already-cropped.
        
        Returns:
            Dictionary with classification results
        """
        try:
            cap = cv2.VideoCapture(str(video_path))
            if not cap.isOpened():
                return {
                    'classification': 'unknown',
                    'confidence': 0.0,
                    'reason': 'Cannot open video',
                    'properties': {}
                }
            
            # Get video properties
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            # Read first frame for additional analysis
            ret, first_frame = cap.read()
            cap.release()
            
            properties = {
                'width': width,
                'height': height,
                'frame_count': frame_count,
                'fps': fps,
                'aspect_ratio': width / height if height > 0 else 0
            }
            
            # Classification logic
            confidence_scores = []
            reasons = []
            
            # Check 1: Small resolution indicates processing
            if width <= self.SMALL_RESOLUTION_THRESHOLD and height <= self.SMALL_RESOLUTION_THRESHOLD:
                confidence_scores.append(0.9)
                reasons.append(f'Small resolution ({width}×{height})')
            
            # Check 2: Exact target frame count indicates processing
            if frame_count == self.TARGET_FRAME_COUNT:
                confidence_scores.append(0.8)
                reasons.append(f'Target frame count ({frame_count})')
            
            # Check 3: Specific target resolutions
            if (width == 96 and height == 64) or (width == 96 and height == 96):
                confidence_scores.append(0.95)
                reasons.append(f'Target resolution ({width}×{height})')
            
            # Check 4: Aspect ratio analysis
            aspect_ratio = width / height if height > 0 else 0
            if self.ASPECT_RATIO_PROCESSED_MIN <= aspect_ratio <= self.ASPECT_RATIO_PROCESSED_MAX:
                if width < 400:  # Small + specific aspect ratio
                    confidence_scores.append(0.6)
                    reasons.append(f'Processed aspect ratio ({aspect_ratio:.2f})')
            
            # Determine final classification
            if confidence_scores:
                max_confidence = max(confidence_scores)
                if max_confidence >= 0.7:
                    classification = 'already_cropped'
                else:
                    classification = 'original'
            else:
                # Default: larger videos are likely original
                if width >= 400 or height >= 200:
                    classification = 'original'
                    max_confidence = 0.6
                    reasons.append('Large resolution suggests original')
                else:
                    classification = 'unknown'
                    max_confidence = 0.3
                    reasons.append('Ambiguous characteristics')
            
            return {
                'classification': classification,
                'confidence': max_confidence,
                'reason': '; '.join(reasons),
                'properties': properties
            }
            
        except Exception as e:
            self.logger.error(f"Error classifying video {video_path}: {e}")
            return {
                'classification': 'unknown',
                'confidence': 0.0,
                'reason': f'Error: {str(e)}',
                'properties': {}
            }


class LipDetector:
    """Multi-level lip detection using MediaPipe, geometric analysis, and fallbacks."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Initialize MediaPipe if available
        if MEDIAPIPE_AVAILABLE:
            self.mp_face_mesh = mp.solutions.face_mesh
            self.face_mesh = self.mp_face_mesh.FaceMesh(
                static_image_mode=False,
                max_num_faces=1,
                refine_landmarks=True,
                min_detection_confidence=0.3,
                min_tracking_confidence=0.3
            )
            
            # Lip landmark indices for MediaPipe Face Mesh
            self.lip_landmarks = [
                61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318,
                78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308
            ]
        else:
            self.face_mesh = None
    
    def detect_lips_mediapipe(self, frame: np.ndarray) -> Optional[Dict[str, Any]]:
        """Detect lips using MediaPipe Face Mesh."""
        if not self.face_mesh:
            return None
        
        try:
            # Convert BGR to RGB
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.face_mesh.process(rgb_frame)
            
            if not results.multi_face_landmarks:
                return None
            
            # Extract lip landmarks
            face_landmarks = results.multi_face_landmarks[0]
            h, w = frame.shape[:2]
            
            lip_points = []
            for idx in self.lip_landmarks:
                landmark = face_landmarks.landmark[idx]
                x = int(landmark.x * w)
                y = int(landmark.y * h)
                lip_points.append((x, y))
            
            if not lip_points:
                return None
            
            # Calculate bounding box
            xs = [p[0] for p in lip_points]
            ys = [p[1] for p in lip_points]
            
            x_min, x_max = min(xs), max(xs)
            y_min, y_max = min(ys), max(ys)
            
            # Add padding
            padding_x = int((x_max - x_min) * 0.3)
            padding_y = int((y_max - y_min) * 0.4)
            
            x_min = max(0, x_min - padding_x)
            x_max = min(w, x_max + padding_x)
            y_min = max(0, y_min - padding_y)
            y_max = min(h, y_max + padding_y)
            
            return {
                'bbox': (x_min, y_min, x_max, y_max),
                'confidence': 0.8,
                'method': 'mediapipe',
                'landmarks': lip_points
            }
            
        except Exception as e:
            self.logger.warning(f"MediaPipe detection failed: {e}")
            return None
    
    def detect_lips_geometric(self, frame: np.ndarray, video_classification: str) -> Dict[str, Any]:
        """Geometric lip detection based on video classification."""
        h, w = frame.shape[:2]
        
        if video_classification == 'already_cropped':
            # For already cropped videos, assume lips are in center-lower region
            center_x, center_y = w // 2, int(h * 0.6)
            lip_width, lip_height = int(w * 0.6), int(h * 0.4)
        else:
            # For original videos, assume lips are in lower face region
            center_x, center_y = w // 2, int(h * 0.75)
            lip_width, lip_height = int(w * 0.2), int(h * 0.15)
        
        x_min = max(0, center_x - lip_width // 2)
        x_max = min(w, center_x + lip_width // 2)
        y_min = max(0, center_y - lip_height // 2)
        y_max = min(h, center_y + lip_height // 2)
        
        return {
            'bbox': (x_min, y_min, x_max, y_max),
            'confidence': 0.5,
            'method': 'geometric',
            'classification_based': video_classification
        }
    
    def detect_lips_fallback(self, frame: np.ndarray) -> Dict[str, Any]:
        """Fallback detection using center crop."""
        h, w = frame.shape[:2]
        
        # Use center 60% of frame
        crop_w, crop_h = int(w * 0.6), int(h * 0.6)
        x_min = (w - crop_w) // 2
        y_min = (h - crop_h) // 2
        x_max = x_min + crop_w
        y_max = y_min + crop_h
        
        return {
            'bbox': (x_min, y_min, x_max, y_max),
            'confidence': 0.3,
            'method': 'fallback_center'
        }
    
    def detect_best_lip_region(self, frame: np.ndarray, video_classification: str) -> Dict[str, Any]:
        """
        Detect best lip region using hierarchical approach.
        
        Priority:
        1. MediaPipe (if available and confident)
        2. Geometric (based on video classification)
        3. Fallback (center crop)
        """
        # Try MediaPipe first
        mp_result = self.detect_lips_mediapipe(frame)
        if mp_result and mp_result['confidence'] >= 0.6:
            return mp_result
        
        # Try geometric detection
        geo_result = self.detect_lips_geometric(frame, video_classification)
        if geo_result['confidence'] >= 0.4:
            return geo_result
        
        # Fallback to center crop
        return self.detect_lips_fallback(frame)


class IntelligentLipDetectionPipeline:
    """Main pipeline class combining classification and detection."""
    
    def __init__(self, output_dir: Path, manifest_path: Path):
        self.output_dir = Path(output_dir)
        self.manifest_path = Path(manifest_path)
        
        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize components
        self.classifier = VideoClassifier()
        self.detector = LipDetector()
        
        # Output specifications (strict requirements)
        self.OUTPUT_WIDTH = 96
        self.OUTPUT_HEIGHT = 64
        self.TARGET_FRAMES = 32
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
        # Processing statistics
        self.stats = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'skipped': 0,
            'original_videos': 0,
            'already_cropped_videos': 0,
            'mediapipe_detections': 0,
            'geometric_detections': 0,
            'fallback_detections': 0
        }
    
    def is_already_processed(self, video_path: Path) -> bool:
        """Check if video is already processed."""
        output_filename = f"intelligent_lip_{video_path.stem}.mp4"
        output_path = self.output_dir / output_filename
        return output_path.exists()
    
    def validate_mouth_visibility(self, frame: np.ndarray) -> bool:
        """Validate that mouth region has sufficient contrast."""
        if frame is None or frame.size == 0:
            return False
        
        # Check central region for contrast
        h, w = frame.shape[:2]
        center_region = frame[h//4:3*h//4, w//4:3*w//4]
        
        if center_region.size == 0:
            return False
        
        # Calculate contrast (standard deviation)
        if len(center_region.shape) == 3:
            gray_region = cv2.cvtColor(center_region, cv2.COLOR_BGR2GRAY)
        else:
            gray_region = center_region
        
        contrast = np.std(gray_region)
        return contrast > 5.0  # Minimum contrast threshold

    def process_single_video(self, video_path: Path) -> Dict[str, Any]:
        """
        Process a single video with intelligent lip detection.

        Returns:
            Processing result dictionary
        """
        start_time = time.time()
        self.stats['total_processed'] += 1

        # Check if already processed
        if self.is_already_processed(video_path):
            self.logger.info(f"⏭️  Skipping already processed: {video_path.name}")
            self.stats['skipped'] += 1
            return {
                'source_path': str(video_path),
                'source_filename': video_path.name,
                'processing_status': 'skipped_already_processed',
                'processing_time': time.time() - start_time
            }

        try:
            # Step 1: Classify video
            self.logger.info(f"🎬 Processing: {video_path.name}")
            classification_result = self.classifier.classify_video(video_path)
            video_classification = classification_result['classification']

            self.logger.info(f"🔍 Classification: {video_classification} "
                           f"(confidence: {classification_result['confidence']:.2f})")

            # Update statistics
            if video_classification == 'original':
                self.stats['original_videos'] += 1
            elif video_classification == 'already_cropped':
                self.stats['already_cropped_videos'] += 1

            # Step 2: Load video
            cap = cv2.VideoCapture(str(video_path))
            if not cap.isOpened():
                raise Exception("Cannot open video")

            # Get video properties
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            original_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            original_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

            # Step 3: Process based on classification
            if video_classification == 'already_cropped':
                result = self._process_already_cropped_video(
                    cap, video_path, classification_result, start_time
                )
            else:  # original or unknown
                result = self._process_original_video(
                    cap, video_path, classification_result, start_time
                )

            cap.release()

            if result['processing_status'] == 'success':
                self.stats['successful'] += 1
            else:
                self.stats['failed'] += 1

            return result

        except Exception as e:
            self.logger.error(f"❌ Error processing {video_path.name}: {e}")
            self.stats['failed'] += 1
            return {
                'source_path': str(video_path),
                'source_filename': video_path.name,
                'processing_status': 'failed',
                'error': str(e),
                'processing_time': time.time() - start_time
            }

    def _process_already_cropped_video(self, cap, video_path: Path,
                                     classification_result: Dict, start_time: float) -> Dict[str, Any]:
        """Process video that's already cropped around lips."""
        self.logger.info("📐 Processing already-cropped video with minimal intervention")

        # Read all frames
        frames = []
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            frames.append(frame)

        if not frames:
            raise Exception("No frames could be read")

        # Sample to exactly 32 frames
        sampled_frames = self._sample_frames_to_target(frames, self.TARGET_FRAMES)

        # Convert to grayscale and resize to exact specifications
        processed_frames = []
        for frame in sampled_frames:
            # Convert to true grayscale (single channel)
            if len(frame.shape) == 3:
                gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            else:
                gray_frame = frame

            # Resize to exact target resolution
            resized_frame = cv2.resize(gray_frame, (self.OUTPUT_WIDTH, self.OUTPUT_HEIGHT))
            processed_frames.append(resized_frame)

        # Validate mouth visibility
        if not self.validate_mouth_visibility(processed_frames[0]):
            self.logger.warning("⚠️  Low mouth visibility detected")

        # Save output video
        output_filename = f"intelligent_lip_{video_path.stem}.mp4"
        output_path = self.output_dir / output_filename

        success = self._save_video(processed_frames, output_path, fps=30.0)

        if success:
            return {
                'source_path': str(video_path),
                'source_filename': video_path.name,
                'output_path': str(output_path),
                'output_filename': output_filename,
                'processing_status': 'success',
                'processing_method': 'minimal_intervention_already_cropped',
                'classification': classification_result,
                'original_frames': len(frames),
                'output_frames': len(processed_frames),
                'output_resolution': f'{self.OUTPUT_WIDTH}x{self.OUTPUT_HEIGHT}',
                'output_channels': 1,
                'processing_time': time.time() - start_time
            }
        else:
            raise Exception("Failed to save output video")

    def _process_original_video(self, cap, video_path: Path,
                              classification_result: Dict, start_time: float) -> Dict[str, Any]:
        """Process original video with full lip detection."""
        self.logger.info("🎯 Processing original video with lip detection")

        # Read all frames
        frames = []
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            frames.append(frame)

        if not frames:
            raise Exception("No frames could be read")

        # Sample to target frame count for processing
        sample_frames = self._sample_frames_to_target(frames, min(len(frames), 50))

        # Detect lip region in sample frames
        detection_results = []
        for frame in sample_frames[:10]:  # Use first 10 frames for detection
            detection = self.detector.detect_best_lip_region(
                frame, classification_result['classification']
            )
            detection_results.append(detection)

        # Choose best detection
        best_detection = max(detection_results, key=lambda x: x['confidence'])
        self.logger.info(f"🎯 Best detection: {best_detection['method']} "
                        f"(confidence: {best_detection['confidence']:.2f})")

        # Update method statistics
        method = best_detection['method']
        if method == 'mediapipe':
            self.stats['mediapipe_detections'] += 1
        elif method == 'geometric':
            self.stats['geometric_detections'] += 1
        else:
            self.stats['fallback_detections'] += 1

        # Apply detection to all frames
        processed_frames = []
        bbox = best_detection['bbox']

        # Sample to exactly 32 frames
        sampled_frames = self._sample_frames_to_target(frames, self.TARGET_FRAMES)

        for frame in sampled_frames:
            # Crop to detected region
            x_min, y_min, x_max, y_max = bbox
            cropped = frame[y_min:y_max, x_min:x_max]

            if cropped.size == 0:
                # Fallback to center crop if detection failed
                h, w = frame.shape[:2]
                center_crop_size = min(h, w) // 2
                y_center, x_center = h // 2, w // 2
                y_min = max(0, y_center - center_crop_size // 2)
                y_max = min(h, y_center + center_crop_size // 2)
                x_min = max(0, x_center - center_crop_size // 2)
                x_max = min(w, x_center + center_crop_size // 2)
                cropped = frame[y_min:y_max, x_min:x_max]

            # Convert to true grayscale
            if len(cropped.shape) == 3:
                gray_cropped = cv2.cvtColor(cropped, cv2.COLOR_BGR2GRAY)
            else:
                gray_cropped = cropped

            # Resize to exact target resolution
            resized = cv2.resize(gray_cropped, (self.OUTPUT_WIDTH, self.OUTPUT_HEIGHT))
            processed_frames.append(resized)

        # Validate mouth visibility
        if not self.validate_mouth_visibility(processed_frames[0]):
            self.logger.warning("⚠️  Low mouth visibility detected")

        # Save output video
        output_filename = f"intelligent_lip_{video_path.stem}.mp4"
        output_path = self.output_dir / output_filename

        success = self._save_video(processed_frames, output_path, fps=30.0)

        if success:
            return {
                'source_path': str(video_path),
                'source_filename': video_path.name,
                'output_path': str(output_path),
                'output_filename': output_filename,
                'processing_status': 'success',
                'processing_method': f'full_lip_detection_{best_detection["method"]}',
                'classification': classification_result,
                'detection_result': best_detection,
                'original_frames': len(frames),
                'output_frames': len(processed_frames),
                'output_resolution': f'{self.OUTPUT_WIDTH}x{self.OUTPUT_HEIGHT}',
                'output_channels': 1,
                'processing_time': time.time() - start_time
            }
        else:
            raise Exception("Failed to save output video")

    def _sample_frames_to_target(self, frames: List[np.ndarray], target_count: int) -> List[np.ndarray]:
        """Sample frames to exact target count using uniform sampling."""
        if len(frames) <= target_count:
            return frames

        # Use numpy linspace for uniform sampling
        indices = np.linspace(0, len(frames) - 1, target_count, dtype=int)
        return [frames[i] for i in indices]

    def _save_video(self, frames: List[np.ndarray], output_path: Path, fps: float = 30.0) -> bool:
        """
        Save frames as MP4 video with exact specifications.

        Requirements:
        - True single-channel grayscale (not 3-channel grayscale)
        - Exact resolution and frame count
        """
        try:
            if not frames:
                return False

            # Verify all frames have correct dimensions
            for i, frame in enumerate(frames):
                if frame.shape != (self.OUTPUT_HEIGHT, self.OUTPUT_WIDTH):
                    self.logger.error(f"Frame {i} has wrong dimensions: {frame.shape}")
                    return False

            # Setup video writer for true grayscale
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            writer = cv2.VideoWriter(
                str(output_path),
                fourcc,
                fps,
                (self.OUTPUT_WIDTH, self.OUTPUT_HEIGHT),
                isColor=False  # This creates true single-channel output
            )

            if not writer.isOpened():
                self.logger.error("Failed to open video writer")
                return False

            # Write frames
            for frame in frames:
                # Ensure frame is single channel
                if len(frame.shape) == 3:
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

                writer.write(frame)

            writer.release()

            # Verify output
            if output_path.exists() and output_path.stat().st_size > 0:
                # Quick verification
                cap = cv2.VideoCapture(str(output_path))
                if cap.isOpened():
                    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                    cap.release()

                    if width == self.OUTPUT_WIDTH and height == self.OUTPUT_HEIGHT and frame_count == len(frames):
                        return True
                    else:
                        self.logger.error(f"Output verification failed: {width}x{height}, {frame_count} frames")
                        return False
                else:
                    return False
            else:
                return False

        except Exception as e:
            self.logger.error(f"Error saving video: {e}")
            return False

    def save_manifest(self, results: List[Dict[str, Any]]) -> None:
        """Save processing results to manifest CSV."""
        try:
            df = pd.DataFrame(results)
            df.to_csv(self.manifest_path, index=False)
            self.logger.info(f"📄 Manifest saved: {self.manifest_path}")
        except Exception as e:
            self.logger.error(f"Error saving manifest: {e}")

    def print_statistics(self) -> None:
        """Print processing statistics."""
        print("\n" + "="*60)
        print("🎯 INTELLIGENT LIP DETECTION PIPELINE STATISTICS")
        print("="*60)
        print(f"📊 Total videos processed: {self.stats['total_processed']}")
        print(f"✅ Successful: {self.stats['successful']}")
        print(f"❌ Failed: {self.stats['failed']}")
        print(f"⏭️  Skipped: {self.stats['skipped']}")
        print()
        print("📋 Video Classification:")
        print(f"   🎬 Original videos: {self.stats['original_videos']}")
        print(f"   ✂️  Already cropped: {self.stats['already_cropped_videos']}")
        print()
        print("🎯 Detection Methods Used:")
        print(f"   🤖 MediaPipe: {self.stats['mediapipe_detections']}")
        print(f"   📐 Geometric: {self.stats['geometric_detections']}")
        print(f"   🔄 Fallback: {self.stats['fallback_detections']}")
        print()

        if self.stats['total_processed'] > 0:
            success_rate = (self.stats['successful'] / self.stats['total_processed']) * 100
            print(f"🎉 Overall Success Rate: {success_rate:.1f}%")
        print("="*60)


def main():
    """Main function for testing the pipeline."""
    import argparse

    parser = argparse.ArgumentParser(description='Intelligent Lip Detection Pipeline')
    parser.add_argument('input_dir', help='Input directory containing videos')
    parser.add_argument('output_dir', help='Output directory for processed videos')
    parser.add_argument('--manifest', default='intelligent_lip_manifest.csv',
                       help='Manifest CSV file path')
    parser.add_argument('--max_videos', type=int, default=20,
                       help='Maximum number of videos to process (for testing)')

    args = parser.parse_args()

    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    # Initialize pipeline
    pipeline = IntelligentLipDetectionPipeline(
        output_dir=Path(args.output_dir),
        manifest_path=Path(args.manifest)
    )

    # Find videos
    input_dir = Path(args.input_dir)
    video_files = []
    for ext in ['.mp4', '.avi', '.mov']:
        video_files.extend(input_dir.rglob(f'*{ext}'))

    if not video_files:
        print(f"❌ No videos found in {input_dir}")
        return

    # Limit for testing
    if args.max_videos:
        video_files = video_files[:args.max_videos]

    print(f"🎬 Found {len(video_files)} videos to process")

    # Process videos
    results = []
    for video_path in video_files:
        result = pipeline.process_single_video(video_path)
        results.append(result)

    # Save results
    pipeline.save_manifest(results)
    pipeline.print_statistics()


if __name__ == '__main__':
    main()
