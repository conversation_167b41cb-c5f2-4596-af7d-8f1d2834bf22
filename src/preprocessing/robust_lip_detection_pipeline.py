#!/usr/bin/env python3
"""
Robust Lip Detection Pipeline - Final Version
=============================================

Ultimate lip detection pipeline that prioritizes finding actual lips over everything else.
Uses multiple detection strategies, visual validation, and adaptive cropping.

Key Features:
- Multi-method lip detection with validation
- Adaptive region sizing based on detection confidence
- Visual quality scoring for lip visibility
- Fallback strategies that still find lips
- Detailed logging and quality metrics
"""

import cv2
import numpy as np
import logging
import time
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Any
import pandas as pd

class RobustLipDetector:
    """Ultimate lip detector with comprehensive detection strategies."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def detect_lips_comprehensive_color(self, frame: np.ndarray) -> Optional[Dict[str, Any]]:
        """Comprehensive color-based lip detection using multiple color spaces."""
        try:
            h, w = frame.shape[:2]
            
            # Convert to multiple color spaces
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
            lab = cv2.cvtColor(frame, cv2.COLOR_BGR2LAB)
            yuv = cv2.cvtColor(frame, cv2.COLOR_BGR2YUV)
            
            # Focus on lower 2/3 of frame where lips are likely
            roi_y_start = h // 4
            roi_frame = frame[roi_y_start:, :]
            roi_hsv = hsv[roi_y_start:, :]
            roi_lab = lab[roi_y_start:, :]
            
            # Multiple lip color detection strategies
            masks = []
            
            # Strategy 1: HSV red/pink detection
            lower_red1 = np.array([0, 40, 40])
            upper_red1 = np.array([10, 255, 255])
            lower_red2 = np.array([160, 40, 40])
            upper_red2 = np.array([180, 255, 255])
            
            mask_red1 = cv2.inRange(roi_hsv, lower_red1, upper_red1)
            mask_red2 = cv2.inRange(roi_hsv, lower_red2, upper_red2)
            mask_hsv = cv2.bitwise_or(mask_red1, mask_red2)
            masks.append(mask_hsv)
            
            # Strategy 2: LAB color space (better for skin tones)
            # Focus on A channel (green-red axis) for lip detection
            a_channel = roi_lab[:, :, 1]
            _, mask_lab = cv2.threshold(a_channel, 135, 255, cv2.THRESH_BINARY)
            masks.append(mask_lab)
            
            # Strategy 3: Saturation-based detection
            s_channel = roi_hsv[:, :, 1]
            _, mask_sat = cv2.threshold(s_channel, 60, 255, cv2.THRESH_BINARY)
            masks.append(mask_sat)
            
            # Combine all masks
            combined_mask = np.zeros_like(masks[0])
            for mask in masks:
                combined_mask = cv2.bitwise_or(combined_mask, mask)
            
            # Clean up mask
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 3))
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
            
            # Find contours
            contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return None
            
            # Filter and score contours
            valid_contours = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if area < 30:  # Too small
                    continue
                
                x, y, w_box, h_box = cv2.boundingRect(contour)
                aspect_ratio = w_box / h_box if h_box > 0 else 0
                
                # Prefer horizontal, mouth-like shapes
                if 0.8 < aspect_ratio < 8.0:  # Wide range for different mouth shapes
                    valid_contours.append((contour, area, aspect_ratio))
            
            if not valid_contours:
                return None
            
            # Choose best contour (balance area and aspect ratio)
            best_contour = max(valid_contours, key=lambda x: x[1] * (2.0 if 1.5 < x[2] < 4.0 else 1.0))
            contour, area, aspect_ratio = best_contour
            
            # Get bounding box
            x, y, w_box, h_box = cv2.boundingRect(contour)
            
            # Adjust coordinates back to full frame
            y += roi_y_start
            
            # Add adaptive padding based on detection confidence
            confidence_factor = min(area / 100.0, 2.0)  # Higher area = more confidence
            padding_x = max(int(w_box * 0.4 * confidence_factor), 15)
            padding_y = max(int(h_box * 0.6 * confidence_factor), 10)
            
            x_min = max(0, x - padding_x)
            x_max = min(w, x + w_box + padding_x)
            y_min = max(0, y - padding_y)
            y_max = min(h, y + h_box + padding_y)
            
            return {
                'bbox': (x_min, y_min, x_max, y_max),
                'confidence': min(0.8, 0.5 + confidence_factor * 0.1),
                'method': 'comprehensive_color',
                'contour_area': area,
                'aspect_ratio': aspect_ratio
            }
            
        except Exception as e:
            self.logger.warning(f"Comprehensive color detection failed: {e}")
            return None
    
    def detect_lips_gradient_analysis(self, frame: np.ndarray) -> Optional[Dict[str, Any]]:
        """Detect lips using gradient analysis to find mouth edges."""
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            h, w = gray.shape
            
            # Focus on mouth region
            roi_y_start = h // 3
            roi_y_end = int(h * 0.85)
            roi_gray = gray[roi_y_start:roi_y_end, :]
            
            # Apply gradient detection
            grad_x = cv2.Sobel(roi_gray, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(roi_gray, cv2.CV_64F, 0, 1, ksize=3)
            
            # Combine gradients
            gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
            gradient_magnitude = np.uint8(gradient_magnitude / gradient_magnitude.max() * 255)
            
            # Focus on horizontal gradients (mouth opening)
            horizontal_emphasis = np.abs(grad_x) > np.abs(grad_y)
            gradient_magnitude[~horizontal_emphasis] = gradient_magnitude[~horizontal_emphasis] * 0.5
            
            # Threshold and find contours
            _, thresh = cv2.threshold(gradient_magnitude, 50, 255, cv2.THRESH_BINARY)
            
            # Morphological operations to connect mouth edges
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (7, 3))
            thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
            
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return None
            
            # Find mouth-like contours
            mouth_candidates = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if area < 40:
                    continue
                
                x, y, w_box, h_box = cv2.boundingRect(contour)
                aspect_ratio = w_box / h_box if h_box > 0 else 0
                
                # Look for horizontal mouth-like shapes
                if 1.2 < aspect_ratio < 6.0 and area > 40:
                    mouth_candidates.append((contour, area, aspect_ratio))
            
            if not mouth_candidates:
                return None
            
            # Choose best candidate
            best_contour = max(mouth_candidates, key=lambda x: x[1])
            contour, area, aspect_ratio = best_contour
            
            x, y, w_box, h_box = cv2.boundingRect(contour)
            
            # Adjust coordinates back to full frame
            y += roi_y_start
            
            # Add generous padding
            padding_x = max(int(w_box * 0.5), 20)
            padding_y = max(int(h_box * 0.8), 15)
            
            x_min = max(0, x - padding_x)
            x_max = min(w, x + w_box + padding_x)
            y_min = max(0, y - padding_y)
            y_max = min(h, y + h_box + padding_y)
            
            return {
                'bbox': (x_min, y_min, x_max, y_max),
                'confidence': 0.65,
                'method': 'gradient_analysis',
                'contour_area': area,
                'aspect_ratio': aspect_ratio
            }
            
        except Exception as e:
            self.logger.warning(f"Gradient analysis detection failed: {e}")
            return None
    
    def detect_lips_adaptive_geometric(self, frame: np.ndarray, video_type: str) -> Dict[str, Any]:
        """Adaptive geometric detection based on video type and frame analysis."""
        h, w = frame.shape[:2]
        
        # Analyze frame to find likely mouth region
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        if video_type == 'already_cropped':
            # For cropped videos, analyze the upper portion where lips typically are
            search_region = gray[:int(h*0.6), :]  # Upper 60%
            
            # Find regions with good contrast (likely mouth area)
            blurred = cv2.GaussianBlur(search_region, (5, 5), 0)
            edges = cv2.Canny(blurred, 30, 80)
            
            # Find center of edge activity
            edge_points = np.where(edges > 0)
            if len(edge_points[0]) > 0:
                center_y = int(np.mean(edge_points[0]))
                center_x = int(np.mean(edge_points[1]))
            else:
                center_y = int(h * 0.35)  # Default to upper-middle
                center_x = w // 2
            
            # Size based on frame dimensions
            lip_width = int(w * 0.7)
            lip_height = int(h * 0.5)
            
        else:
            # For original videos, focus on lower face
            search_region = gray[int(h*0.5):, :]  # Lower 50%
            
            # Find regions with good contrast
            blurred = cv2.GaussianBlur(search_region, (5, 5), 0)
            edges = cv2.Canny(blurred, 30, 80)
            
            # Find center of edge activity
            edge_points = np.where(edges > 0)
            if len(edge_points[0]) > 0:
                center_y = int(np.mean(edge_points[0])) + int(h*0.5)  # Adjust for ROI offset
                center_x = int(np.mean(edge_points[1]))
            else:
                center_y = int(h * 0.7)  # Default to lower face
                center_x = w // 2
            
            # Size based on face proportions
            lip_width = int(w * 0.3)
            lip_height = int(h * 0.15)
        
        # Calculate bounding box
        x_min = max(0, center_x - lip_width // 2)
        x_max = min(w, center_x + lip_width // 2)
        y_min = max(0, center_y - lip_height // 2)
        y_max = min(h, center_y + lip_height // 2)
        
        return {
            'bbox': (x_min, y_min, x_max, y_max),
            'confidence': 0.55,
            'method': 'adaptive_geometric',
            'video_type': video_type,
            'detected_center': (center_x, center_y)
        }
    
    def validate_lip_region_comprehensive(self, frame: np.ndarray, bbox: Tuple[int, int, int, int]) -> float:
        """Comprehensive validation of lip region quality."""
        x_min, y_min, x_max, y_max = bbox
        
        # Extract region
        roi = frame[y_min:y_max, x_min:x_max]
        if roi.size == 0:
            return 0.0
        
        # Convert to grayscale for analysis
        if len(roi.shape) == 3:
            gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
        else:
            gray_roi = roi
        
        scores = []
        
        # 1. Contrast score (lips should have good contrast)
        contrast = np.std(gray_roi)
        contrast_score = min(contrast / 25.0, 1.0)
        scores.append(contrast_score * 0.3)
        
        # 2. Edge density (lips have distinct edges)
        edges = cv2.Canny(gray_roi, 30, 80)
        edge_density = np.sum(edges > 0) / edges.size
        edge_score = min(edge_density * 8, 1.0)
        scores.append(edge_score * 0.25)
        
        # 3. Horizontal structure (lips are horizontal)
        horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (9, 1))
        horizontal_edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, horizontal_kernel)
        horizontal_score = np.sum(horizontal_edges > 0) / edges.size
        horizontal_score = min(horizontal_score * 12, 1.0)
        scores.append(horizontal_score * 0.25)
        
        # 4. Intensity variation (mouth opening creates dark regions)
        intensity_var = np.var(gray_roi.astype(np.float32))
        intensity_score = min(intensity_var / 400.0, 1.0)
        scores.append(intensity_score * 0.2)
        
        return sum(scores)
    
    def find_best_lip_region(self, frame: np.ndarray, video_type: str) -> Dict[str, Any]:
        """Find the best lip region using all detection methods."""
        detections = []
        
        # Try all detection methods
        methods = [
            self.detect_lips_comprehensive_color,
            self.detect_lips_gradient_analysis,
            lambda f: self.detect_lips_adaptive_geometric(f, video_type)
        ]
        
        for method in methods:
            try:
                result = method(frame)
                if result:
                    # Comprehensive validation
                    validation_score = self.validate_lip_region_comprehensive(frame, result['bbox'])
                    result['validation_score'] = validation_score
                    result['combined_confidence'] = result['confidence'] * (0.7 + validation_score * 0.3)
                    detections.append(result)
            except Exception as e:
                self.logger.warning(f"Detection method failed: {e}")
        
        if not detections:
            # Ultimate fallback with better positioning
            h, w = frame.shape[:2]
            if video_type == 'already_cropped':
                center_x, center_y = w // 2, int(h * 0.4)
                crop_w, crop_h = int(w * 0.8), int(h * 0.6)
            else:
                center_x, center_y = w // 2, int(h * 0.7)
                crop_w, crop_h = int(w * 0.3), int(h * 0.2)
            
            x_min = max(0, center_x - crop_w // 2)
            x_max = min(w, center_x + crop_w // 2)
            y_min = max(0, center_y - crop_h // 2)
            y_max = min(h, center_y + crop_h // 2)
            
            return {
                'bbox': (x_min, y_min, x_max, y_max),
                'confidence': 0.3,
                'method': 'ultimate_fallback',
                'validation_score': 0.3,
                'combined_confidence': 0.3
            }
        
        # Return detection with highest combined confidence
        best_detection = max(detections, key=lambda x: x['combined_confidence'])
        return best_detection


class RobustLipDetectionPipeline:
    """Robust pipeline focused on finding actual lips with high quality."""

    def __init__(self, output_dir: Path, manifest_path: Path):
        self.output_dir = Path(output_dir)
        self.manifest_path = Path(manifest_path)

        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Initialize detector
        self.detector = RobustLipDetector()

        # Output specifications
        self.OUTPUT_WIDTH = 96
        self.OUTPUT_HEIGHT = 64
        self.TARGET_FRAMES = 32

        # Setup logging
        self.logger = logging.getLogger(__name__)

        # Statistics
        self.stats = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'excellent_quality': 0,  # >0.8
            'high_quality': 0,       # 0.6-0.8
            'medium_quality': 0,     # 0.4-0.6
            'low_quality': 0,        # <0.4
            'method_counts': {}
        }

    def classify_video(self, video_path: Path) -> str:
        """Classify video type for adaptive processing."""
        filename = video_path.name.lower()
        if '_topmid' in filename or 'cropped' in filename:
            return 'already_cropped'

        try:
            cap = cv2.VideoCapture(str(video_path))
            if cap.isOpened():
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                cap.release()

                if width < 200 and height < 200:
                    return 'already_cropped'
                elif width >= 400 or height >= 200:
                    return 'original'

            return 'unknown'
        except Exception:
            return 'unknown'

    def process_single_video(self, video_path: Path) -> Dict[str, Any]:
        """Process single video with robust lip detection."""
        start_time = time.time()
        self.stats['total_processed'] += 1

        try:
            self.logger.info(f"🎬 Processing: {video_path.name}")

            # Classify video
            video_type = self.classify_video(video_path)
            self.logger.info(f"🔍 Video type: {video_type}")

            # Load video
            cap = cv2.VideoCapture(str(video_path))
            if not cap.isOpened():
                raise Exception("Cannot open video")

            frames = []
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                frames.append(frame)
            cap.release()

            if not frames:
                raise Exception("No frames could be read")

            # Multi-frame analysis for best detection
            sample_count = min(len(frames), 15)  # More samples for better detection
            sample_indices = np.linspace(0, len(frames) - 1, sample_count, dtype=int)

            all_detections = []
            for idx in sample_indices:
                frame = frames[idx]
                detection = self.detector.find_best_lip_region(frame, video_type)
                detection['frame_idx'] = idx
                all_detections.append(detection)

            # Choose best detection across all frames
            best_detection = max(all_detections, key=lambda x: x['combined_confidence'])

            self.logger.info(f"🎯 Best detection: {best_detection['method']} "
                           f"(confidence: {best_detection['combined_confidence']:.3f}, "
                           f"validation: {best_detection['validation_score']:.3f})")

            # Update statistics
            method = best_detection['method']
            self.stats['method_counts'][method] = self.stats['method_counts'].get(method, 0) + 1

            # Quality categorization
            confidence = best_detection['combined_confidence']
            if confidence >= 0.8:
                self.stats['excellent_quality'] += 1
                quality = 'excellent'
            elif confidence >= 0.6:
                self.stats['high_quality'] += 1
                quality = 'high'
            elif confidence >= 0.4:
                self.stats['medium_quality'] += 1
                quality = 'medium'
            else:
                self.stats['low_quality'] += 1
                quality = 'low'

            # Process all frames with best detection
            bbox = best_detection['bbox']
            processed_frames = []

            # Sample to exactly 32 frames
            target_indices = np.linspace(0, len(frames) - 1, self.TARGET_FRAMES, dtype=int)

            for idx in target_indices:
                frame = frames[idx]

                # Apply detection
                x_min, y_min, x_max, y_max = bbox
                h, w = frame.shape[:2]

                # Ensure valid crop
                x_min = max(0, min(x_min, w-1))
                x_max = max(x_min+1, min(x_max, w))
                y_min = max(0, min(y_min, h-1))
                y_max = max(y_min+1, min(y_max, h))

                # Crop
                cropped = frame[y_min:y_max, x_min:x_max]

                if cropped.size == 0:
                    # Emergency fallback
                    crop_size = min(h, w) // 3
                    center_x, center_y = w // 2, h // 2
                    y_start = max(0, center_y - crop_size // 2)
                    y_end = min(h, center_y + crop_size // 2)
                    x_start = max(0, center_x - crop_size // 2)
                    x_end = min(w, center_x + crop_size // 2)
                    cropped = frame[y_start:y_end, x_start:x_end]

                # Convert to grayscale
                if len(cropped.shape) == 3:
                    gray_cropped = cv2.cvtColor(cropped, cv2.COLOR_BGR2GRAY)
                else:
                    gray_cropped = cropped

                # Resize to target
                resized = cv2.resize(gray_cropped, (self.OUTPUT_WIDTH, self.OUTPUT_HEIGHT))
                processed_frames.append(resized)

            # Save video
            output_filename = f"robust_lip_{video_path.stem}.mp4"
            output_path = self.output_dir / output_filename

            success = self._save_video(processed_frames, output_path)

            if success:
                self.stats['successful'] += 1
                return {
                    'source_path': str(video_path),
                    'source_filename': video_path.name,
                    'output_path': str(output_path),
                    'output_filename': output_filename,
                    'processing_status': 'success',
                    'video_type': video_type,
                    'detection_method': best_detection['method'],
                    'detection_confidence': best_detection['combined_confidence'],
                    'validation_score': best_detection['validation_score'],
                    'quality_category': quality,
                    'bbox_used': bbox,
                    'frames_analyzed': sample_count,
                    'original_frames': len(frames),
                    'output_frames': len(processed_frames),
                    'processing_time': time.time() - start_time
                }
            else:
                raise Exception("Failed to save video")

        except Exception as e:
            self.logger.error(f"❌ Error: {e}")
            self.stats['failed'] += 1
            return {
                'source_path': str(video_path),
                'source_filename': video_path.name,
                'processing_status': 'failed',
                'error': str(e),
                'processing_time': time.time() - start_time
            }

    def _save_video(self, frames: List[np.ndarray], output_path: Path) -> bool:
        """Save video with proper format."""
        try:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            writer = cv2.VideoWriter(
                str(output_path), fourcc, 30.0,
                (self.OUTPUT_WIDTH, self.OUTPUT_HEIGHT), isColor=False
            )

            if not writer.isOpened():
                return False

            for frame in frames:
                if len(frame.shape) == 3:
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                writer.write(frame)

            writer.release()
            return output_path.exists() and output_path.stat().st_size > 0

        except Exception:
            return False

    def save_manifest(self, results: List[Dict[str, Any]]) -> None:
        """Save results manifest."""
        try:
            df = pd.DataFrame(results)
            df.to_csv(self.manifest_path, index=False)
            self.logger.info(f"📄 Manifest saved: {self.manifest_path}")
        except Exception as e:
            self.logger.error(f"Error saving manifest: {e}")

    def print_statistics(self) -> None:
        """Print comprehensive statistics."""
        print("\n" + "="*65)
        print("🎯 ROBUST LIP DETECTION PIPELINE STATISTICS")
        print("="*65)
        print(f"📊 Total videos processed: {self.stats['total_processed']}")
        print(f"✅ Successful: {self.stats['successful']}")
        print(f"❌ Failed: {self.stats['failed']}")
        print()
        print("🎯 Quality Distribution:")
        print(f"   🌟 Excellent (≥0.8): {self.stats['excellent_quality']}")
        print(f"   🟢 High (0.6-0.8): {self.stats['high_quality']}")
        print(f"   🟡 Medium (0.4-0.6): {self.stats['medium_quality']}")
        print(f"   🔴 Low (<0.4): {self.stats['low_quality']}")
        print()
        print("🔍 Detection Methods:")
        for method, count in self.stats['method_counts'].items():
            print(f"   📐 {method}: {count}")
        print()

        if self.stats['total_processed'] > 0:
            success_rate = (self.stats['successful'] / self.stats['total_processed']) * 100
            high_quality_rate = ((self.stats['excellent_quality'] + self.stats['high_quality']) /
                               self.stats['total_processed']) * 100
            print(f"🎉 Success Rate: {success_rate:.1f}%")
            print(f"🎯 High+ Quality Rate: {high_quality_rate:.1f}%")
        print("="*65)
