#!/usr/bin/env python3
"""
Enhanced ICU Geometric Cropping Pipeline
========================================

Intelligent preprocessing pipeline that addresses critical quality issues while maintaining
compatibility with both speaker set and GRID corpus videos.

CRITICAL IMPROVEMENTS:
- Over-cropping prevention for already-processed speaker set videos
- Exact format standardization: 96×64 grayscale, 32 frames
- Multi-format robustness with intelligent video analysis
- Comprehensive 5-point validation system
- Context-preserving ROI extraction

PROCESSING STRATEGY:
- Speaker sets (already cropped): Minimal adjustment to preserve mouth context
- Speaker sets (needs cropping): Top 50% height, middle 33% width
- GRID corpus: Face detection + bottom 60% crop
- Unknown formats: Adaptive processing based on detected lip position

Author: Augment Agent
Date: 2025-09-29
Status: Enhanced Multi-Format Solution
"""

import sys
import cv2
import numpy as np
import pandas as pd
import pathlib
import json
import time
from datetime import datetime
from typing import List, Dict, Tuple, Optional, Any
import logging
from tqdm import tqdm

class EnhancedICUGeometricCropper:
    """
    Enhanced geometric cropping tool with intelligent format detection and over-crop prevention.
    
    Ensures strict lip-reading model compatibility while preserving mouth context
    across diverse video formats.
    """
    
    def __init__(self, 
                 source_dir: str,
                 output_dir: str,
                 manifest_path: str = "enhanced_icu_processing_manifest.csv"):
        """
        Initialize enhanced ICU geometric cropper.
        
        Args:
            source_dir: Directory containing input videos
            output_dir: Directory for processed output videos
            manifest_path: Path for output manifest CSV
        """
        self.source_dir = pathlib.Path(source_dir)
        self.output_dir = pathlib.Path(output_dir)
        self.manifest_path = pathlib.Path(manifest_path)
        
        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # EXACT lip-reading model requirements
        self.TARGET_WIDTH = 96
        self.TARGET_HEIGHT = 64
        self.TARGET_FRAMES = 32
        self.TARGET_CHANNELS = 1  # Grayscale only
        
        # Format detection thresholds
        self.ALREADY_CROPPED_HEIGHT_THRESHOLD = 200  # Below this = already cropped
        self.SPEAKER_SET_ASPECT_RATIO_MIN = 1.5      # 2:1 ratio suggests pre-cropped
        self.FACE_DETECTION_THRESHOLD = 0.3          # Low rate = pre-cropped
        self.MOUTH_COVERAGE_OVER_CROP_THRESHOLD = 0.4  # >40% = over-cropped
        self.MOUTH_COVERAGE_UNDER_CROP_THRESHOLD = 0.1 # <10% = needs cropping
        
        # Quality validation thresholds
        self.MIN_MOUTH_CONTRAST = 8.0
        self.CENTRAL_ROI_SIZE = (24, 32)  # Height, Width for mouth visibility check
        
        # Video file extensions
        self.video_extensions = {'.mp4', '.mov', '.avi', '.mkv', '.webm'}
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Initialize face detector for GRID corpus processing
        try:
            self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        except Exception as e:
            self.logger.warning(f"Face detector initialization failed: {e}")
            self.face_cascade = None
    
    def load_video_frames(self, video_path: pathlib.Path) -> List[np.ndarray]:
        """
        Load all frames from video file.
        
        Args:
            video_path: Path to video file
            
        Returns:
            List of frame arrays
        """
        frames = []
        
        try:
            cap = cv2.VideoCapture(str(video_path))
            if not cap.isOpened():
                self.logger.warning(f"Cannot open video: {video_path}")
                return frames
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                frames.append(frame)
            
            cap.release()
            
        except Exception as e:
            self.logger.error(f"Error loading video {video_path}: {e}")
        
        return frames
    
    def detect_video_format_and_crop_status(self, frames: List[np.ndarray]) -> Tuple[str, str, str]:
        """
        Analyze video to determine processing strategy.
        
        Args:
            frames: List of video frames
            
        Returns:
            Tuple of (format_type, crop_status, lip_position)
            - format_type: 'speaker_set' | 'grid_corpus' | 'unknown'
            - crop_status: 'already_cropped' | 'needs_cropping' | 'partial_crop'
            - lip_position: 'top_third' | 'center_third' | 'bottom_third'
        """
        if not frames:
            return 'unknown', 'needs_cropping', 'center_third'
        
        # Sample frames for analysis
        sample_frames = frames[::max(1, len(frames)//10)][:10]
        
        # Analyze frame dimensions
        heights = [frame.shape[0] for frame in sample_frames]
        widths = [frame.shape[1] for frame in sample_frames]
        avg_height = np.mean(heights)
        avg_width = np.mean(widths)
        aspect_ratio = avg_width / avg_height if avg_height > 0 else 1.0
        
        # Detection criteria
        is_low_height = avg_height < self.ALREADY_CROPPED_HEIGHT_THRESHOLD
        is_wide_aspect = aspect_ratio > self.SPEAKER_SET_ASPECT_RATIO_MIN
        
        # Face detection analysis (if available)
        face_detection_rate = 0.0
        if self.face_cascade is not None:
            face_detections = 0
            for frame in sample_frames:
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                faces = self.face_cascade.detectMultiScale(gray, 1.1, 4)
                if len(faces) > 0:
                    face_detections += 1
            face_detection_rate = face_detections / len(sample_frames)
        
        # Determine format type
        if is_low_height or is_wide_aspect or face_detection_rate < self.FACE_DETECTION_THRESHOLD:
            format_type = 'speaker_set'
        elif face_detection_rate > 0.7:
            format_type = 'grid_corpus'
        else:
            format_type = 'unknown'
        
        # Determine crop status
        if is_low_height and is_wide_aspect:
            crop_status = 'already_cropped'
        elif avg_height > 300 and face_detection_rate > 0.5:
            crop_status = 'needs_cropping'
        else:
            crop_status = 'partial_crop'
        
        # Determine lip position (simplified heuristic)
        if format_type == 'speaker_set':
            lip_position = 'top_third'  # Speaker sets have lips at top
        elif format_type == 'grid_corpus':
            lip_position = 'bottom_third'  # GRID corpus has lips at bottom of face
        else:
            lip_position = 'center_third'  # Default for unknown
        
        self.logger.info(f"📊 Format detection: {format_type}, crop: {crop_status}, lips: {lip_position}")
        self.logger.info(f"   Dimensions: {avg_width:.0f}×{avg_height:.0f}, aspect: {aspect_ratio:.2f}, face_rate: {face_detection_rate:.2f}")
        
        return format_type, crop_status, lip_position
    
    def apply_minimal_context_preserving_adjustment(self, frame: np.ndarray) -> np.ndarray:
        """
        Apply minimal geometric adjustment for already-cropped videos.
        Preserves existing mouth context without aggressive re-cropping.
        
        Args:
            frame: Input frame (already cropped)
            
        Returns:
            Minimally adjusted frame
        """
        height, width = frame.shape[:2]
        
        # Apply maximum 10% padding adjustment
        padding_factor = 0.05  # 5% padding on each side
        
        # Calculate minimal crop region
        crop_width = int(width * (1 - 2 * padding_factor))
        crop_height = int(height * (1 - 2 * padding_factor))
        
        # Center the crop
        x_start = (width - crop_width) // 2
        y_start = (height - crop_height) // 2
        x_end = x_start + crop_width
        y_end = y_start + crop_height
        
        # Ensure boundaries
        x_start = max(0, x_start)
        y_start = max(0, y_start)
        x_end = min(width, x_end)
        y_end = min(height, y_end)
        
        return frame[y_start:y_end, x_start:x_end]
    
    def crop_top_50_percent_middle_33_percent(self, frame: np.ndarray) -> np.ndarray:
        """
        Apply original ICU cropping strategy for speaker set videos.
        
        Args:
            frame: Input frame
            
        Returns:
            Cropped frame (top 50% height, middle 33% width)
        """
        height, width = frame.shape[:2]
        
        # Calculate crop dimensions
        crop_height = int(height * 0.50)  # Top 50%
        crop_width = int(width * 0.33)    # Middle 33%
        
        # Calculate starting positions
        x_start = (width - crop_width) // 2   # Center horizontally
        y_start = 0                           # Start from top
        
        # Calculate end positions
        x_end = x_start + crop_width
        y_end = y_start + crop_height
        
        # Ensure boundaries
        x_start = max(0, x_start)
        x_end = min(width, x_end)
        y_start = max(0, y_start)
        y_end = min(height, y_end)
        
        return frame[y_start:y_end, x_start:x_end]
    
    def detect_face_and_crop_bottom_60_percent(self, frame: np.ndarray) -> np.ndarray:
        """
        Detect face and crop bottom 60% for GRID corpus videos.
        
        Args:
            frame: Input frame
            
        Returns:
            Cropped frame (bottom 60% of detected face)
        """
        if self.face_cascade is None:
            # Fallback to center crop if face detection unavailable
            return self.apply_adaptive_cropping_by_lip_position(frame, 'center_third')
        
        height, width = frame.shape[:2]
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # Detect faces
        faces = self.face_cascade.detectMultiScale(gray, 1.1, 4)
        
        if len(faces) > 0:
            # Use the largest face
            face_areas = [w * h for (x, y, w, h) in faces]
            largest_face_idx = np.argmax(face_areas)
            x, y, w, h = faces[largest_face_idx]
            
            # Crop bottom 60% of face
            mouth_y = y + int(h * 0.4)  # Start at 40% down face
            mouth_h = int(h * 0.6)      # Height is 60% of face height
            mouth_x = x
            mouth_w = w
            
            # Ensure boundaries
            mouth_x = max(0, mouth_x)
            mouth_y = max(0, mouth_y)
            mouth_w = min(width - mouth_x, mouth_w)
            mouth_h = min(height - mouth_y, mouth_h)
            
            return frame[mouth_y:mouth_y + mouth_h, mouth_x:mouth_x + mouth_w]
        else:
            # Fallback to adaptive cropping if no face detected
            return self.apply_adaptive_cropping_by_lip_position(frame, 'bottom_third')
    
    def apply_adaptive_cropping_by_lip_position(self, frame: np.ndarray, lip_position: str) -> np.ndarray:
        """
        Apply adaptive cropping based on detected lip position.
        
        Args:
            frame: Input frame
            lip_position: 'top_third' | 'center_third' | 'bottom_third'
            
        Returns:
            Adaptively cropped frame
        """
        height, width = frame.shape[:2]
        
        # Define crop regions based on lip position
        if lip_position == 'top_third':
            # Crop top 50% height, middle 60% width
            crop_height = int(height * 0.5)
            crop_width = int(width * 0.6)
            y_start = 0
            x_start = (width - crop_width) // 2
        elif lip_position == 'bottom_third':
            # Crop bottom 50% height, middle 60% width
            crop_height = int(height * 0.5)
            crop_width = int(width * 0.6)
            y_start = height - crop_height
            x_start = (width - crop_width) // 2
        else:  # center_third
            # Crop center 60% height, middle 60% width
            crop_height = int(height * 0.6)
            crop_width = int(width * 0.6)
            y_start = (height - crop_height) // 2
            x_start = (width - crop_width) // 2
        
        # Calculate end positions
        x_end = x_start + crop_width
        y_end = y_start + crop_height
        
        # Ensure boundaries
        x_start = max(0, x_start)
        y_start = max(0, y_start)
        x_end = min(width, x_end)
        y_end = min(height, y_end)
        
        return frame[y_start:y_end, x_start:x_end]

    def extract_lip_roi_adaptive(self, frame: np.ndarray, format_type: str,
                                crop_status: str, lip_position: str) -> np.ndarray:
        """
        Context-preserving ROI extraction with over-crop prevention.

        Args:
            frame: Input frame
            format_type: 'speaker_set' | 'grid_corpus' | 'unknown'
            crop_status: 'already_cropped' | 'needs_cropping' | 'partial_crop'
            lip_position: 'top_third' | 'center_third' | 'bottom_third'

        Returns:
            Extracted ROI frame
        """
        if crop_status == "already_cropped":
            return self.apply_minimal_context_preserving_adjustment(frame)
        elif format_type == "speaker_set":
            return self.crop_top_50_percent_middle_33_percent(frame)
        elif format_type == "grid_corpus":
            return self.detect_face_and_crop_bottom_60_percent(frame)
        else:
            return self.apply_adaptive_cropping_by_lip_position(frame, lip_position)

    def standardize_to_lipreading_format(self, frames: List[np.ndarray]) -> List[np.ndarray]:
        """
        Enforce EXACT lip-reading model requirements.

        Args:
            frames: List of input frames

        Returns:
            List of standardized frames (96×64 grayscale, exactly 32 frames)
        """
        if not frames:
            return []

        # Step 1: Convert to grayscale
        gray_frames = []
        for frame in frames:
            if len(frame.shape) == 3:
                gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            else:
                gray_frame = frame
            gray_frames.append(gray_frame)

        # Step 2: Resize to exact 96×64 (width, height)
        resized_frames = []
        for frame in gray_frames:
            resized = cv2.resize(frame, (self.TARGET_WIDTH, self.TARGET_HEIGHT),
                               interpolation=cv2.INTER_AREA)
            resized_frames.append(resized)

        # Step 3: Temporal sampling to exactly 32 frames
        if len(resized_frames) >= self.TARGET_FRAMES:
            # Uniform sampling
            indices = np.linspace(0, len(resized_frames) - 1, self.TARGET_FRAMES, dtype=int)
            sampled_frames = [resized_frames[i] for i in indices]
        else:
            # Pad with repeated frames if too few
            sampled_frames = resized_frames[:]
            while len(sampled_frames) < self.TARGET_FRAMES:
                sampled_frames.extend(resized_frames[:self.TARGET_FRAMES - len(sampled_frames)])
            sampled_frames = sampled_frames[:self.TARGET_FRAMES]

        return sampled_frames

    def validate_mouth_contrast(self, frames: List[np.ndarray]) -> bool:
        """
        Validate mouth visibility through contrast analysis.

        Args:
            frames: List of processed frames

        Returns:
            True if mouth visibility is sufficient
        """
        if not frames:
            return False

        # Use center frame for analysis
        center_frame = frames[len(frames) // 2]
        height, width = center_frame.shape

        # Define central ROI for mouth visibility
        roi_h, roi_w = self.CENTRAL_ROI_SIZE
        roi_h = min(roi_h, height // 2)
        roi_w = min(roi_w, width // 2)

        start_y = max(0, height // 2 - roi_h // 2)
        end_y = min(height, start_y + roi_h)
        start_x = max(0, width // 2 - roi_w // 2)
        end_x = min(width, start_x + roi_w)

        central_roi = center_frame[start_y:end_y, start_x:end_x]

        if central_roi.size == 0:
            return False

        contrast = np.std(central_roi)
        return contrast > self.MIN_MOUTH_CONTRAST

    def validate_mp4_integrity(self, output_path: pathlib.Path) -> bool:
        """
        Validate MP4 file integrity.

        Args:
            output_path: Path to output MP4 file

        Returns:
            True if file is valid MP4
        """
        try:
            if not output_path.exists():
                return False

            cap = cv2.VideoCapture(str(output_path))
            if not cap.isOpened():
                cap.release()
                return False

            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            cap.release()

            return frame_count == self.TARGET_FRAMES

        except Exception:
            return False

    def validate_lipreading_compliance(self, frames: List[np.ndarray],
                                     output_path: pathlib.Path) -> Tuple[bool, Dict[str, bool]]:
        """
        Mandatory validation checklist - ALL must pass for success.

        Args:
            frames: List of processed frames
            output_path: Path to output file

        Returns:
            Tuple of (all_passed, validation_results)
        """
        validation_results = {
            'resolution_check': bool(all(frame.shape == (self.TARGET_HEIGHT, self.TARGET_WIDTH) for frame in frames)),
            'channel_check': bool(all(len(frame.shape) == 2 for frame in frames)),
            'frame_count_check': bool(len(frames) == self.TARGET_FRAMES),
            'mouth_visibility_check': bool(self.validate_mouth_contrast(frames)),
            'file_format_check': bool(self.validate_mp4_integrity(output_path))
        }

        all_passed = all(validation_results.values())
        return all_passed, validation_results

    def save_lipreading_compatible_mp4(self, frames: List[np.ndarray], output_path: pathlib.Path):
        """
        Save frames as lip-reading compatible MP4.

        Args:
            frames: List of standardized frames
            output_path: Output file path
        """
        if not frames:
            raise ValueError("No frames to save")

        # Ensure output directory exists
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # Setup video writer for grayscale
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        fps = 30.0  # Standard frame rate

        # Create video writer
        writer = cv2.VideoWriter(
            str(output_path),
            fourcc,
            fps,
            (self.TARGET_WIDTH, self.TARGET_HEIGHT),
            isColor=False  # Grayscale
        )

        if not writer.isOpened():
            raise RuntimeError(f"Cannot create video writer for: {output_path}")

        # Write frames
        for frame in frames:
            writer.write(frame)

        writer.release()

    def create_success_result(self, format_type: str, crop_status: str, lip_position: str,
                            validation_details: Dict[str, bool], video_path: pathlib.Path,
                            output_path: pathlib.Path, processing_time: float,
                            original_frames: int, processed_frames: int) -> Dict[str, Any]:
        """
        Create success result dictionary.
        """
        return {
            'success': True,
            'processing_status': 'success',
            'input_path': str(video_path),
            'output_path': str(output_path),
            'format_type': format_type,
            'crop_status': crop_status,
            'lip_position': lip_position,
            'original_frames': original_frames,
            'processed_frames': processed_frames,
            'processing_time': processing_time,
            'output_resolution': f'{self.TARGET_WIDTH}x{self.TARGET_HEIGHT}',
            'output_format': 'grayscale',
            'target_frames': self.TARGET_FRAMES,
            'validation_results': validation_details,
            'pipeline_version': 'enhanced_icu_v1.0',
            'timestamp': datetime.now().isoformat()
        }

    def create_failure_result(self, format_type: str, crop_status: str, lip_position: str,
                            validation_details: Dict[str, bool], video_path: pathlib.Path,
                            output_path: pathlib.Path, processing_time: float,
                            error_message: str = "") -> Dict[str, Any]:
        """
        Create failure result dictionary.
        """
        return {
            'success': False,
            'processing_status': 'failed',
            'input_path': str(video_path),
            'output_path': str(output_path),
            'format_type': format_type,
            'crop_status': crop_status,
            'lip_position': lip_position,
            'processing_time': processing_time,
            'validation_results': validation_details,
            'error_message': error_message,
            'pipeline_version': 'enhanced_icu_v1.0',
            'timestamp': datetime.now().isoformat()
        }

    def save_processing_debug_info(self, video_path: pathlib.Path, result: Dict[str, Any],
                                 original_frames: List[np.ndarray], roi_frames: List[np.ndarray],
                                 standardized_frames: List[np.ndarray]):
        """
        Save debug information for inspection.

        Args:
            video_path: Original video path
            result: Processing result dictionary
            original_frames: Original video frames
            roi_frames: ROI extracted frames
            standardized_frames: Final standardized frames
        """
        debug_dir = self.output_dir / "debug_info"
        debug_dir.mkdir(exist_ok=True)

        video_name = video_path.stem

        # Save sample frames for inspection
        if original_frames:
            sample_original = original_frames[len(original_frames) // 2]
            cv2.imwrite(str(debug_dir / f"{video_name}_original.jpg"), sample_original)

        if roi_frames:
            sample_roi = roi_frames[len(roi_frames) // 2]
            cv2.imwrite(str(debug_dir / f"{video_name}_roi.jpg"), sample_roi)

        if standardized_frames:
            sample_standardized = standardized_frames[len(standardized_frames) // 2]
            cv2.imwrite(str(debug_dir / f"{video_name}_standardized.jpg"), sample_standardized)

        # Save processing metadata (convert booleans to strings for JSON compatibility)
        def convert_bools_to_strings(obj):
            """Recursively convert boolean values to strings for JSON serialization."""
            if isinstance(obj, dict):
                return {k: convert_bools_to_strings(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_bools_to_strings(item) for item in obj]
            elif isinstance(obj, bool):
                return str(obj)
            else:
                return obj

        debug_info = {
            'video_name': video_name,
            'processing_result': convert_bools_to_strings(result),
            'frame_counts': {
                'original': len(original_frames),
                'roi': len(roi_frames),
                'standardized': len(standardized_frames)
            }
        }

        with open(debug_dir / f"{video_name}_debug.json", 'w') as f:
            json.dump(debug_info, f, indent=2)

    def process_video_enhanced(self, video_path: pathlib.Path, output_path: pathlib.Path,
                             save_debug_info: bool = True) -> Dict[str, Any]:
        """
        Enhanced processing pipeline with intelligent adaptation and strict compliance.

        Args:
            video_path: Path to input video
            output_path: Path for output video
            save_debug_info: Whether to save debug information

        Returns:
            Processing result dictionary
        """
        start_time = time.time()

        try:
            # Step 1: Load and analyze input video
            self.logger.info(f"🎬 Processing: {video_path.name}")
            frames = self.load_video_frames(video_path)

            if not frames:
                return self.create_failure_result(
                    'unknown', 'unknown', 'unknown', {}, video_path, output_path,
                    time.time() - start_time, "Failed to load video frames"
                )

            original_frame_count = len(frames)
            format_type, crop_status, lip_position = self.detect_video_format_and_crop_status(frames)

            # Step 2: Apply context-aware ROI extraction
            self.logger.info(f"🎯 Applying {format_type} strategy: {crop_status} → {lip_position}")
            roi_frames = []
            for frame in frames:
                roi_frame = self.extract_lip_roi_adaptive(frame, format_type, crop_status, lip_position)
                roi_frames.append(roi_frame)

            # Step 3: Standardize to exact lip-reading format
            self.logger.info(f"📐 Standardizing to {self.TARGET_WIDTH}×{self.TARGET_HEIGHT} grayscale, {self.TARGET_FRAMES} frames")
            standardized_frames = self.standardize_to_lipreading_format(roi_frames)

            # Step 4: Save with proper encoding
            self.save_lipreading_compatible_mp4(standardized_frames, output_path)

            # Step 5: Mandatory 5-point validation
            validation_passed, validation_details = self.validate_lipreading_compliance(standardized_frames, output_path)

            processing_time = time.time() - start_time

            if validation_passed:
                self.logger.info(f"✅ SUCCESS: {video_path.name} → {output_path.name} ({processing_time:.2f}s)")
                result = self.create_success_result(
                    format_type, crop_status, lip_position, validation_details,
                    video_path, output_path, processing_time, original_frame_count, len(standardized_frames)
                )
            else:
                self.logger.warning(f"❌ VALIDATION FAILED: {video_path.name}")
                result = self.create_failure_result(
                    format_type, crop_status, lip_position, validation_details,
                    video_path, output_path, processing_time, "Validation failed"
                )

            # Step 6: Save debug information for inspection
            if save_debug_info:
                try:
                    self.save_processing_debug_info(video_path, result, frames, roi_frames, standardized_frames)
                except Exception as debug_error:
                    self.logger.warning(f"Debug info saving failed: {debug_error}")

            return result

        except Exception as e:
            processing_time = time.time() - start_time
            error_message = f"Processing error: {str(e)}"
            self.logger.error(f"❌ ERROR: {video_path.name} - {error_message}")

            return self.create_failure_result(
                'unknown', 'unknown', 'unknown', {}, video_path, output_path,
                processing_time, error_message
            )

    def find_video_files(self, directory: pathlib.Path) -> List[pathlib.Path]:
        """
        Find all video files in directory and subdirectories.

        Args:
            directory: Directory to search

        Returns:
            List of video file paths
        """
        video_files = []

        for ext in self.video_extensions:
            video_files.extend(directory.rglob(f"*{ext}"))

        return sorted(video_files)

    def process_all_videos(self, max_videos: Optional[int] = None) -> Dict[str, Any]:
        """
        Process all videos in source directory.

        Args:
            max_videos: Maximum number of videos to process (None for all)

        Returns:
            Summary of processing results
        """
        start_time = time.time()

        # Find all video files
        video_files = self.find_video_files(self.source_dir)

        if max_videos:
            video_files = video_files[:max_videos]

        self.logger.info(f"🎬 Found {len(video_files)} videos to process")

        # Process videos
        results = []
        successful = 0
        failed = 0

        for video_path in tqdm(video_files, desc="Processing videos"):
            # Generate output filename
            output_filename = f"enhanced_icu_{video_path.stem}.mp4"
            output_path = self.output_dir / output_filename

            # Process video
            result = self.process_video_enhanced(video_path, output_path)
            results.append(result)

            if result['success']:
                successful += 1
            else:
                failed += 1

        # Calculate summary statistics
        total_time = time.time() - start_time
        success_rate = (successful / len(video_files)) * 100 if video_files else 0

        # Create summary
        summary = {
            'total_videos': len(video_files),
            'successful': successful,
            'failed': failed,
            'success_rate': success_rate,
            'total_processing_time': total_time,
            'average_time_per_video': total_time / len(video_files) if video_files else 0,
            'results': results,
            'pipeline_version': 'enhanced_icu_v1.0',
            'timestamp': datetime.now().isoformat()
        }

        # Save manifest
        self.save_manifest(summary)

        self.logger.info(f"🎉 Processing complete: {successful}/{len(video_files)} successful ({success_rate:.1f}%)")

        return summary

    def save_manifest(self, summary: Dict[str, Any]):
        """
        Save processing manifest as CSV.

        Args:
            summary: Processing summary dictionary
        """
        # Convert results to DataFrame
        df_data = []
        for result in summary['results']:
            row = {
                'input_path': result['input_path'],
                'output_path': result.get('output_path', ''),
                'success': result['success'],
                'format_type': result.get('format_type', ''),
                'crop_status': result.get('crop_status', ''),
                'lip_position': result.get('lip_position', ''),
                'original_frames': result.get('original_frames', 0),
                'processed_frames': result.get('processed_frames', 0),
                'processing_time': result.get('processing_time', 0),
                'output_resolution': result.get('output_resolution', ''),
                'output_format': result.get('output_format', ''),
                'validation_resolution': result.get('validation_results', {}).get('resolution_check', False),
                'validation_channels': result.get('validation_results', {}).get('channel_check', False),
                'validation_frames': result.get('validation_results', {}).get('frame_count_check', False),
                'validation_mouth': result.get('validation_results', {}).get('mouth_visibility_check', False),
                'validation_file': result.get('validation_results', {}).get('file_format_check', False),
                'error_message': result.get('error_message', ''),
                'timestamp': result.get('timestamp', '')
            }
            df_data.append(row)

        df = pd.DataFrame(df_data)
        df.to_csv(self.manifest_path, index=False)

        self.logger.info(f"📄 Manifest saved: {self.manifest_path}")


def main():
    """
    Main function for command-line usage.
    """
    import argparse

    parser = argparse.ArgumentParser(description="Enhanced ICU Geometric Cropping Pipeline")
    parser.add_argument("source_dir", help="Source directory containing videos")
    parser.add_argument("output_dir", help="Output directory for processed videos")
    parser.add_argument("--manifest", default="enhanced_icu_processing_manifest.csv",
                       help="Output manifest file path")
    parser.add_argument("--max-videos", type=int, help="Maximum number of videos to process")

    args = parser.parse_args()

    # Create and run processor
    processor = EnhancedICUGeometricCropper(
        source_dir=args.source_dir,
        output_dir=args.output_dir,
        manifest_path=args.manifest
    )

    summary = processor.process_all_videos(max_videos=args.max_videos)

    print(f"\n🎉 Enhanced ICU Processing Complete!")
    print(f"📊 Success Rate: {summary['success_rate']:.1f}% ({summary['successful']}/{summary['total_videos']})")
    print(f"⏱️  Total Time: {summary['total_processing_time']:.1f}s")
    print(f"📄 Manifest: {args.manifest}")


if __name__ == "__main__":
    main()
