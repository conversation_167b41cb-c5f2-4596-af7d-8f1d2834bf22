#!/usr/bin/env python3
"""
Enhanced Lip Detection Pipeline with Robust Lip Finding
=======================================================

Improved pipeline that prioritizes actual lip detection over format compliance.
Uses multiple detection strategies and visual validation to ensure lips are properly located.

Key Improvements:
- Enhanced lip detection using multiple methods
- Visual validation of lip presence
- Adaptive cropping based on detected lip regions
- Fallback strategies that still find lips
- Quality scoring for lip visibility
"""

import cv2
import numpy as np
import logging
import time
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Any
import pandas as pd

try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
except ImportError:
    MEDIAPIPE_AVAILABLE = False


class EnhancedLipDetector:
    """Enhanced lip detector with multiple robust detection methods."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Initialize MediaPipe if available
        if MEDIAPIPE_AVAILABLE:
            self.mp_face_mesh = mp.solutions.face_mesh
            self.face_mesh = self.mp_face_mesh.FaceMesh(
                static_image_mode=False,
                max_num_faces=1,
                refine_landmarks=True,
                min_detection_confidence=0.2,  # Lower threshold for better detection
                min_tracking_confidence=0.2
            )
            
            # Comprehensive lip landmark indices
            self.outer_lip_landmarks = [61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318]
            self.inner_lip_landmarks = [78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308]
            self.all_lip_landmarks = list(set(self.outer_lip_landmarks + self.inner_lip_landmarks))
        else:
            self.face_mesh = None
    
    def detect_lips_mediapipe(self, frame: np.ndarray) -> Optional[Dict[str, Any]]:
        """Enhanced MediaPipe lip detection with better landmark processing."""
        if not self.face_mesh:
            return None
        
        try:
            # Convert BGR to RGB
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.face_mesh.process(rgb_frame)
            
            if not results.multi_face_landmarks:
                return None
            
            # Extract lip landmarks
            face_landmarks = results.multi_face_landmarks[0]
            h, w = frame.shape[:2]
            
            lip_points = []
            for idx in self.all_lip_landmarks:
                if idx < len(face_landmarks.landmark):
                    landmark = face_landmarks.landmark[idx]
                    x = int(landmark.x * w)
                    y = int(landmark.y * h)
                    lip_points.append((x, y))
            
            if len(lip_points) < 5:  # Need minimum points for reliable detection
                return None
            
            # Calculate bounding box with generous padding
            xs = [p[0] for p in lip_points]
            ys = [p[1] for p in lip_points]
            
            x_min, x_max = min(xs), max(xs)
            y_min, y_max = min(ys), max(ys)
            
            # Add substantial padding for context
            lip_width = x_max - x_min
            lip_height = y_max - y_min
            
            padding_x = max(int(lip_width * 0.8), 20)  # At least 80% padding or 20px
            padding_y = max(int(lip_height * 1.0), 15)  # At least 100% padding or 15px
            
            x_min = max(0, x_min - padding_x)
            x_max = min(w, x_max + padding_x)
            y_min = max(0, y_min - padding_y)
            y_max = min(h, y_max + padding_y)
            
            # Validate detection quality
            detection_width = x_max - x_min
            detection_height = y_max - y_min
            
            if detection_width < 20 or detection_height < 15:
                return None
            
            return {
                'bbox': (x_min, y_min, x_max, y_max),
                'confidence': 0.9,
                'method': 'mediapipe_enhanced',
                'landmarks': lip_points,
                'lip_center': (int(np.mean(xs)), int(np.mean(ys)))
            }
            
        except Exception as e:
            self.logger.warning(f"Enhanced MediaPipe detection failed: {e}")
            return None
    
    def detect_lips_color_analysis(self, frame: np.ndarray) -> Optional[Dict[str, Any]]:
        """Detect lips using color analysis (lip color detection)."""
        try:
            # Convert to different color spaces for lip detection
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
            lab = cv2.cvtColor(frame, cv2.COLOR_BGR2LAB)
            
            h, w = frame.shape[:2]
            
            # Focus on lower half of frame where lips are likely to be
            roi_y_start = h // 3
            roi_frame = frame[roi_y_start:, :]
            roi_hsv = hsv[roi_y_start:, :]
            roi_lab = lab[roi_y_start:, :]
            
            # Create mask for lip-like colors (reddish/pinkish regions)
            # HSV ranges for lip colors
            lower_lip1 = np.array([0, 30, 30])
            upper_lip1 = np.array([10, 255, 255])
            lower_lip2 = np.array([160, 30, 30])
            upper_lip2 = np.array([180, 255, 255])
            
            mask1 = cv2.inRange(roi_hsv, lower_lip1, upper_lip1)
            mask2 = cv2.inRange(roi_hsv, lower_lip2, upper_lip2)
            lip_mask = cv2.bitwise_or(mask1, mask2)
            
            # Clean up mask
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            lip_mask = cv2.morphologyEx(lip_mask, cv2.MORPH_CLOSE, kernel)
            lip_mask = cv2.morphologyEx(lip_mask, cv2.MORPH_OPEN, kernel)
            
            # Find contours
            contours, _ = cv2.findContours(lip_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return None
            
            # Find largest contour (likely lip region)
            largest_contour = max(contours, key=cv2.contourArea)
            
            if cv2.contourArea(largest_contour) < 50:  # Minimum area threshold
                return None
            
            # Get bounding box
            x, y, w_box, h_box = cv2.boundingRect(largest_contour)
            
            # Adjust coordinates back to full frame
            y += roi_y_start
            
            # Add padding
            padding_x = max(int(w_box * 0.5), 15)
            padding_y = max(int(h_box * 0.5), 10)
            
            x_min = max(0, x - padding_x)
            x_max = min(w, x + w_box + padding_x)
            y_min = max(0, y - padding_y)
            y_max = min(h, y + h_box + padding_y)
            
            return {
                'bbox': (x_min, y_min, x_max, y_max),
                'confidence': 0.7,
                'method': 'color_analysis',
                'contour_area': cv2.contourArea(largest_contour)
            }
            
        except Exception as e:
            self.logger.warning(f"Color analysis detection failed: {e}")
            return None
    
    def detect_lips_edge_analysis(self, frame: np.ndarray) -> Optional[Dict[str, Any]]:
        """Detect lips using edge analysis to find mouth opening."""
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            h, w = gray.shape
            
            # Focus on lower 2/3 of frame
            roi_y_start = h // 3
            roi_gray = gray[roi_y_start:, :]
            
            # Apply Gaussian blur and edge detection
            blurred = cv2.GaussianBlur(roi_gray, (5, 5), 0)
            edges = cv2.Canny(blurred, 30, 80)
            
            # Find horizontal edges (mouth opening)
            horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (15, 3))
            horizontal_edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, horizontal_kernel)
            
            # Find contours
            contours, _ = cv2.findContours(horizontal_edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return None
            
            # Filter contours by aspect ratio (mouth-like shapes)
            mouth_contours = []
            for contour in contours:
                x, y, w_box, h_box = cv2.boundingRect(contour)
                aspect_ratio = w_box / h_box if h_box > 0 else 0
                area = cv2.contourArea(contour)
                
                # Look for horizontal, mouth-like shapes
                if 1.5 < aspect_ratio < 6.0 and area > 30:
                    mouth_contours.append(contour)
            
            if not mouth_contours:
                return None
            
            # Choose best contour (largest area)
            best_contour = max(mouth_contours, key=cv2.contourArea)
            x, y, w_box, h_box = cv2.boundingRect(best_contour)
            
            # Adjust coordinates back to full frame
            y += roi_y_start
            
            # Add generous padding
            padding_x = max(int(w_box * 0.6), 20)
            padding_y = max(int(h_box * 1.2), 15)
            
            x_min = max(0, x - padding_x)
            x_max = min(w, x + w_box + padding_x)
            y_min = max(0, y - padding_y)
            y_max = min(h, y + h_box + padding_y)
            
            return {
                'bbox': (x_min, y_min, x_max, y_max),
                'confidence': 0.6,
                'method': 'edge_analysis',
                'contour_area': cv2.contourArea(best_contour)
            }
            
        except Exception as e:
            self.logger.warning(f"Edge analysis detection failed: {e}")
            return None
    
    def detect_lips_smart_geometric(self, frame: np.ndarray, video_classification: str) -> Dict[str, Any]:
        """Smart geometric detection with better positioning."""
        h, w = frame.shape[:2]
        
        if video_classification == 'already_cropped':
            # For already cropped videos, lips are typically in the center-upper region
            # These videos show lower half of face, so lips are in upper portion
            center_x = w // 2
            center_y = int(h * 0.4)  # Upper 40% of cropped face
            
            # Use larger region to ensure we capture lips
            lip_width = int(w * 0.8)  # 80% of width
            lip_height = int(h * 0.6)  # 60% of height
            
        else:
            # For original videos, lips are in lower face region
            center_x = w // 2
            center_y = int(h * 0.75)  # Lower face
            
            # Smaller region relative to full face
            lip_width = int(w * 0.25)
            lip_height = int(h * 0.2)
        
        x_min = max(0, center_x - lip_width // 2)
        x_max = min(w, center_x + lip_width // 2)
        y_min = max(0, center_y - lip_height // 2)
        y_max = min(h, center_y + lip_height // 2)
        
        return {
            'bbox': (x_min, y_min, x_max, y_max),
            'confidence': 0.5,
            'method': 'smart_geometric',
            'classification_based': video_classification,
            'center_point': (center_x, center_y)
        }
    
    def validate_lip_detection(self, frame: np.ndarray, bbox: Tuple[int, int, int, int]) -> float:
        """Validate if the detected region actually contains lips."""
        x_min, y_min, x_max, y_max = bbox
        
        # Extract region
        roi = frame[y_min:y_max, x_min:x_max]
        if roi.size == 0:
            return 0.0
        
        # Convert to grayscale for analysis
        if len(roi.shape) == 3:
            gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
        else:
            gray_roi = roi
        
        # Calculate various quality metrics
        scores = []
        
        # 1. Contrast score (lips should have good contrast)
        contrast = np.std(gray_roi)
        contrast_score = min(contrast / 30.0, 1.0)  # Normalize to 0-1
        scores.append(contrast_score)
        
        # 2. Edge density (lips have distinct edges)
        edges = cv2.Canny(gray_roi, 30, 80)
        edge_density = np.sum(edges > 0) / edges.size
        edge_score = min(edge_density * 10, 1.0)  # Normalize to 0-1
        scores.append(edge_score)
        
        # 3. Horizontal structure (lips are horizontal)
        horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (7, 1))
        horizontal_edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, horizontal_kernel)
        horizontal_score = np.sum(horizontal_edges > 0) / edges.size
        horizontal_score = min(horizontal_score * 15, 1.0)
        scores.append(horizontal_score)
        
        # Return weighted average
        weights = [0.4, 0.3, 0.3]  # Contrast is most important
        final_score = sum(s * w for s, w in zip(scores, weights))
        
        return final_score
    
    def find_best_lip_region(self, frame: np.ndarray, video_classification: str) -> Dict[str, Any]:
        """Find the best lip region using all available methods."""
        detections = []
        
        # Try all detection methods
        methods = [
            self.detect_lips_mediapipe,
            self.detect_lips_color_analysis,
            self.detect_lips_edge_analysis,
            lambda f: self.detect_lips_smart_geometric(f, video_classification)
        ]
        
        for method in methods:
            try:
                result = method(frame)
                if result:
                    # Validate the detection
                    validation_score = self.validate_lip_detection(frame, result['bbox'])
                    result['validation_score'] = validation_score
                    result['combined_confidence'] = result['confidence'] * validation_score
                    detections.append(result)
            except Exception as e:
                self.logger.warning(f"Detection method failed: {e}")
        
        if not detections:
            # Ultimate fallback - center crop
            h, w = frame.shape[:2]
            crop_size = min(h, w) // 2
            center_x, center_y = w // 2, h // 2
            x_min = max(0, center_x - crop_size // 2)
            x_max = min(w, center_x + crop_size // 2)
            y_min = max(0, center_y - crop_size // 2)
            y_max = min(h, center_y + crop_size // 2)
            
            return {
                'bbox': (x_min, y_min, x_max, y_max),
                'confidence': 0.2,
                'method': 'ultimate_fallback',
                'validation_score': 0.2,
                'combined_confidence': 0.04
            }
        
        # Return detection with highest combined confidence
        best_detection = max(detections, key=lambda x: x['combined_confidence'])
        return best_detection


class EnhancedLipDetectionPipeline:
    """Enhanced pipeline with focus on actual lip detection quality."""

    def __init__(self, output_dir: Path, manifest_path: Path):
        self.output_dir = Path(output_dir)
        self.manifest_path = Path(manifest_path)

        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Initialize components
        self.detector = EnhancedLipDetector()

        # Output specifications
        self.OUTPUT_WIDTH = 96
        self.OUTPUT_HEIGHT = 64
        self.TARGET_FRAMES = 32

        # Setup logging
        self.logger = logging.getLogger(__name__)

        # Processing statistics
        self.stats = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'high_quality_detections': 0,
            'medium_quality_detections': 0,
            'low_quality_detections': 0,
            'method_counts': {}
        }

    def classify_video_simple(self, video_path: Path) -> str:
        """Simple video classification based on filename and basic properties."""
        try:
            # Check filename patterns
            filename = video_path.name.lower()
            if '_topmid' in filename or 'cropped' in filename:
                return 'already_cropped'

            # Check video properties
            cap = cv2.VideoCapture(str(video_path))
            if cap.isOpened():
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                cap.release()

                # Small resolution suggests already processed
                if width < 200 and height < 200:
                    return 'already_cropped'
                elif width >= 400 or height >= 200:
                    return 'original'

            return 'unknown'

        except Exception:
            return 'unknown'

    def process_single_video(self, video_path: Path) -> Dict[str, Any]:
        """Process single video with enhanced lip detection."""
        start_time = time.time()
        self.stats['total_processed'] += 1

        try:
            self.logger.info(f"🎬 Processing: {video_path.name}")

            # Classify video
            video_classification = self.classify_video_simple(video_path)
            self.logger.info(f"🔍 Classification: {video_classification}")

            # Load video
            cap = cv2.VideoCapture(str(video_path))
            if not cap.isOpened():
                raise Exception("Cannot open video")

            # Read all frames
            frames = []
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                frames.append(frame)
            cap.release()

            if not frames:
                raise Exception("No frames could be read")

            # Sample frames for detection analysis
            sample_count = min(len(frames), 10)
            sample_indices = np.linspace(0, len(frames) - 1, sample_count, dtype=int)
            sample_frames = [frames[i] for i in sample_indices]

            # Find best lip detection across sample frames
            best_detection = None
            best_score = 0

            for frame in sample_frames:
                detection = self.detector.find_best_lip_region(frame, video_classification)
                if detection['combined_confidence'] > best_score:
                    best_detection = detection
                    best_score = detection['combined_confidence']

            if not best_detection:
                raise Exception("No lip detection found")

            self.logger.info(f"🎯 Best detection: {best_detection['method']} "
                           f"(confidence: {best_detection['combined_confidence']:.3f})")

            # Update method statistics
            method = best_detection['method']
            self.stats['method_counts'][method] = self.stats['method_counts'].get(method, 0) + 1

            # Categorize detection quality
            if best_detection['combined_confidence'] >= 0.7:
                self.stats['high_quality_detections'] += 1
                quality = 'high'
            elif best_detection['combined_confidence'] >= 0.4:
                self.stats['medium_quality_detections'] += 1
                quality = 'medium'
            else:
                self.stats['low_quality_detections'] += 1
                quality = 'low'

            # Process all frames with the best detection
            bbox = best_detection['bbox']
            processed_frames = []

            # Sample to exactly 32 frames
            target_indices = np.linspace(0, len(frames) - 1, self.TARGET_FRAMES, dtype=int)

            for idx in target_indices:
                frame = frames[idx]

                # Apply detection bbox
                x_min, y_min, x_max, y_max = bbox

                # Ensure valid crop region
                h, w = frame.shape[:2]
                x_min = max(0, min(x_min, w-1))
                x_max = max(x_min+1, min(x_max, w))
                y_min = max(0, min(y_min, h-1))
                y_max = max(y_min+1, min(y_max, h))

                # Crop to detected region
                cropped = frame[y_min:y_max, x_min:x_max]

                if cropped.size == 0:
                    # Fallback to center crop
                    center_size = min(h, w) // 2
                    center_x, center_y = w // 2, h // 2
                    y_start = max(0, center_y - center_size // 2)
                    y_end = min(h, center_y + center_size // 2)
                    x_start = max(0, center_x - center_size // 2)
                    x_end = min(w, center_x + center_size // 2)
                    cropped = frame[y_start:y_end, x_start:x_end]

                # Convert to grayscale
                if len(cropped.shape) == 3:
                    gray_cropped = cv2.cvtColor(cropped, cv2.COLOR_BGR2GRAY)
                else:
                    gray_cropped = cropped

                # Resize to target resolution
                resized = cv2.resize(gray_cropped, (self.OUTPUT_WIDTH, self.OUTPUT_HEIGHT))
                processed_frames.append(resized)

            # Save output video
            output_filename = f"enhanced_lip_{video_path.stem}.mp4"
            output_path = self.output_dir / output_filename

            success = self._save_video(processed_frames, output_path)

            if success:
                self.stats['successful'] += 1
                return {
                    'source_path': str(video_path),
                    'source_filename': video_path.name,
                    'output_path': str(output_path),
                    'output_filename': output_filename,
                    'processing_status': 'success',
                    'video_classification': video_classification,
                    'detection_method': best_detection['method'],
                    'detection_confidence': best_detection['combined_confidence'],
                    'detection_quality': quality,
                    'validation_score': best_detection.get('validation_score', 0),
                    'original_frames': len(frames),
                    'output_frames': len(processed_frames),
                    'output_resolution': f'{self.OUTPUT_WIDTH}x{self.OUTPUT_HEIGHT}',
                    'processing_time': time.time() - start_time
                }
            else:
                raise Exception("Failed to save output video")

        except Exception as e:
            self.logger.error(f"❌ Error processing {video_path.name}: {e}")
            self.stats['failed'] += 1
            return {
                'source_path': str(video_path),
                'source_filename': video_path.name,
                'processing_status': 'failed',
                'error': str(e),
                'processing_time': time.time() - start_time
            }

    def _save_video(self, frames: List[np.ndarray], output_path: Path) -> bool:
        """Save frames as MP4 video."""
        try:
            if not frames:
                return False

            # Setup video writer
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            writer = cv2.VideoWriter(
                str(output_path),
                fourcc,
                30.0,
                (self.OUTPUT_WIDTH, self.OUTPUT_HEIGHT),
                isColor=False
            )

            if not writer.isOpened():
                return False

            # Write frames
            for frame in frames:
                if len(frame.shape) == 3:
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                writer.write(frame)

            writer.release()
            return output_path.exists() and output_path.stat().st_size > 0

        except Exception as e:
            self.logger.error(f"Error saving video: {e}")
            return False

    def save_manifest(self, results: List[Dict[str, Any]]) -> None:
        """Save processing results to manifest."""
        try:
            df = pd.DataFrame(results)
            df.to_csv(self.manifest_path, index=False)
            self.logger.info(f"📄 Manifest saved: {self.manifest_path}")
        except Exception as e:
            self.logger.error(f"Error saving manifest: {e}")

    def print_statistics(self) -> None:
        """Print detailed processing statistics."""
        print("\n" + "="*60)
        print("🎯 ENHANCED LIP DETECTION PIPELINE STATISTICS")
        print("="*60)
        print(f"📊 Total videos processed: {self.stats['total_processed']}")
        print(f"✅ Successful: {self.stats['successful']}")
        print(f"❌ Failed: {self.stats['failed']}")
        print()
        print("🎯 Detection Quality Distribution:")
        print(f"   🟢 High Quality (≥0.7): {self.stats['high_quality_detections']}")
        print(f"   🟡 Medium Quality (0.4-0.7): {self.stats['medium_quality_detections']}")
        print(f"   🔴 Low Quality (<0.4): {self.stats['low_quality_detections']}")
        print()
        print("🔍 Detection Methods Used:")
        for method, count in self.stats['method_counts'].items():
            print(f"   📐 {method}: {count}")
        print()

        if self.stats['total_processed'] > 0:
            success_rate = (self.stats['successful'] / self.stats['total_processed']) * 100
            high_quality_rate = (self.stats['high_quality_detections'] / self.stats['total_processed']) * 100
            print(f"🎉 Success Rate: {success_rate:.1f}%")
            print(f"🎯 High Quality Detection Rate: {high_quality_rate:.1f}%")
        print("="*60)
