<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Lip Reading Demo - Mobile</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px;
            color: white;
        }

        .container {
            max-width: 400px;
            width: 100%;
            text-align: center;
        }

        h1 {
            font-size: 24px;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .camera-container {
            position: relative;
            width: 300px;
            height: 200px;
            margin: 20px auto;
            border-radius: 20px;
            overflow: hidden;
            background: rgba(0,0,0,0.3);
            border: 3px solid rgba(255,255,255,0.3);
        }

        #videoElement {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transform: scaleX(-1); /* Mirror effect */
        }

        .lip-guide {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 40px;
            border: 2px solid #00ff88;
            border-radius: 50px;
            background: rgba(0,255,136,0.1);
            box-shadow: 0 0 20px rgba(0,255,136,0.5);
        }

        .lip-guide::after {
            content: "👄 Position lips here";
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            color: #00ff88;
            white-space: nowrap;
        }

        .controls {
            margin: 20px 0;
        }

        button {
            background: linear-gradient(45deg, #00ff88, #00cc6a);
            border: none;
            color: white;
            padding: 15px 30px;
            font-size: 16px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px;
            box-shadow: 0 4px 15px rgba(0,255,136,0.3);
            transition: all 0.3s ease;
            font-weight: bold;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,255,136,0.4);
        }

        button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .status {
            margin: 15px 0;
            padding: 10px;
            border-radius: 10px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
        }

        .prediction-result {
            margin: 20px 0;
            padding: 15px;
            background: rgba(255,255,255,0.15);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .prediction-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 8px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
        }

        .confidence-bar {
            width: 100px;
            height: 8px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            overflow: hidden;
            margin-left: 10px;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ff88, #00cc6a);
            transition: width 0.3s ease;
        }

        .error {
            color: #ff6b6b;
            background: rgba(255,107,107,0.2);
            padding: 10px;
            border-radius: 10px;
            margin: 10px 0;
        }

        .instructions {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: left;
            font-size: 14px;
        }

        .instructions h3 {
            margin-bottom: 10px;
            color: #00ff88;
        }

        .instructions li {
            margin: 5px 0;
            list-style-position: inside;
        }

        @media (max-width: 480px) {
            .camera-container {
                width: 280px;
                height: 180px;
            }
            
            h1 {
                font-size: 20px;
            }
            
            button {
                padding: 12px 24px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Lip Reading Demo</h1>
        
        <div class="instructions">
            <h3>📱 Mobile Setup:</h3>
            <ol>
                <li>Tap "Start Camera" below</li>
                <li>Allow camera permissions when prompted</li>
                <li>Position your lips in the green guide</li>
                <li>Tap "Record & Predict" to test</li>
            </ol>
        </div>

        <div class="camera-container">
            <video id="videoElement" autoplay muted playsinline></video>
            <div class="lip-guide"></div>
        </div>

        <div class="controls">
            <button id="startCameraBtn" onclick="requestCameraAccess()">📹 Start Camera</button>
            <button id="recordBtn" onclick="toggleRecording()" disabled>🎤 Record & Predict</button>
        </div>

        <div id="status" class="status">
            Ready to start camera
        </div>

        <div id="predictionResult" class="prediction-result" style="display: none;">
            <h3>🎯 Prediction Results:</h3>
            <div id="predictions"></div>
        </div>

        <div id="errorMessage" class="error" style="display: none;"></div>
    </div>

    <script>
        const API_URL = 'http://*************:5000';
        let stream = null;
        let mediaRecorder = null;
        let recordedChunks = [];
        let isRecording = false;

        const videoElement = document.getElementById('videoElement');
        const startCameraBtn = document.getElementById('startCameraBtn');
        const recordBtn = document.getElementById('recordBtn');
        const status = document.getElementById('status');
        const predictionResult = document.getElementById('predictionResult');
        const predictions = document.getElementById('predictions');
        const errorMessage = document.getElementById('errorMessage');

        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            setTimeout(() => {
                errorMessage.style.display = 'none';
            }, 5000);
        }

        function updateStatus(message) {
            status.textContent = message;
        }

        async function requestCameraAccess() {
            try {
                updateStatus('Requesting camera access...');

                // Check for getUserMedia support with fallbacks
                let getUserMedia = null;

                if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                    // Modern browsers
                    getUserMedia = navigator.mediaDevices.getUserMedia.bind(navigator.mediaDevices);
                } else if (navigator.getUserMedia) {
                    // Older browsers
                    getUserMedia = navigator.getUserMedia.bind(navigator);
                } else if (navigator.webkitGetUserMedia) {
                    // Webkit browsers
                    getUserMedia = navigator.webkitGetUserMedia.bind(navigator);
                } else if (navigator.mozGetUserMedia) {
                    // Firefox
                    getUserMedia = navigator.mozGetUserMedia.bind(navigator);
                } else {
                    throw new Error('getUserMedia is not supported in this browser');
                }

                // Request camera with mobile-optimized constraints
                const constraints = {
                    video: {
                        width: { ideal: 640, max: 1280 },
                        height: { ideal: 480, max: 720 },
                        facingMode: 'user',
                        frameRate: { ideal: 30, max: 30 }
                    },
                    audio: true
                };

                // Handle both Promise and callback-based APIs
                if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                    stream = await getUserMedia(constraints);
                } else {
                    // Use callback-based API for older browsers
                    stream = await new Promise((resolve, reject) => {
                        getUserMedia(constraints, resolve, reject);
                    });
                }

                videoElement.srcObject = stream;
                
                // Wait for video to be ready
                await new Promise((resolve) => {
                    videoElement.onloadedmetadata = resolve;
                });

                startCameraBtn.disabled = true;
                startCameraBtn.textContent = '✅ Camera Active';
                recordBtn.disabled = false;
                updateStatus('Camera ready! Position your lips in the green guide.');

            } catch (error) {
                console.error('Camera access error:', error);
                
                let errorMsg = 'Camera access denied. ';
                if (error.name === 'NotAllowedError') {
                    errorMsg += 'Please enable camera permissions in your browser settings.';
                } else if (error.name === 'NotFoundError') {
                    errorMsg += 'No camera found on this device.';
                } else if (error.name === 'NotSupportedError') {
                    errorMsg += 'Camera not supported on this browser.';
                } else {
                    errorMsg += error.message;
                }
                
                showError(errorMsg);
                updateStatus('Camera access failed');
            }
        }

        async function toggleRecording() {
            if (!isRecording) {
                startRecording();
            } else {
                stopRecording();
            }
        }

        function startRecording() {
            try {
                recordedChunks = [];
                
                mediaRecorder = new MediaRecorder(stream, {
                    mimeType: 'video/webm;codecs=vp8,opus'
                });

                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        recordedChunks.push(event.data);
                    }
                };

                mediaRecorder.onstop = () => {
                    processRecording();
                };

                mediaRecorder.start();
                isRecording = true;
                
                recordBtn.textContent = '⏹️ Stop Recording';
                recordBtn.style.background = 'linear-gradient(45deg, #ff6b6b, #ee5a52)';
                updateStatus('Recording... Say your word clearly!');

                // Auto-stop after 3 seconds
                setTimeout(() => {
                    if (isRecording) {
                        stopRecording();
                    }
                }, 3000);

            } catch (error) {
                console.error('Recording error:', error);
                showError('Failed to start recording: ' + error.message);
            }
        }

        function stopRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                isRecording = false;
                
                recordBtn.textContent = '🎤 Record & Predict';
                recordBtn.style.background = 'linear-gradient(45deg, #00ff88, #00cc6a)';
                updateStatus('Processing recording...');
            }
        }

        async function processRecording() {
            try {
                const blob = new Blob(recordedChunks, { type: 'video/webm' });
                
                updateStatus('Sending to AI model...');

                const formData = new FormData();
                formData.append('video', blob, 'recording.webm');

                const response = await fetch(`${API_URL}/predict`, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`Server error: ${response.status}`);
                }

                const result = await response.json();
                displayPredictions(result);
                updateStatus('Prediction complete!');

            } catch (error) {
                console.error('Processing error:', error);
                showError('Failed to process recording: ' + error.message);
                updateStatus('Processing failed');
            }
        }

        function displayPredictions(result) {
            const predictionList = result.predictions || result.top2 || [];
            
            predictions.innerHTML = '';
            
            predictionList.forEach((pred, index) => {
                const confidence = pred.confidence > 1 ? pred.confidence / 100 : pred.confidence;
                const percentage = Math.round(confidence * 100);
                
                const predItem = document.createElement('div');
                predItem.className = 'prediction-item';
                
                predItem.innerHTML = `
                    <span>${index + 1}. ${pred.class}</span>
                    <div style="display: flex; align-items: center;">
                        <span>${percentage}%</span>
                        <div class="confidence-bar">
                            <div class="confidence-fill" style="width: ${percentage}%"></div>
                        </div>
                    </div>
                `;
                
                predictions.appendChild(predItem);
            });
            
            predictionResult.style.display = 'block';
        }

        // Browser and protocol detection
        function detectBrowserIssues() {
            const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
            const isAndroid = /Android/.test(navigator.userAgent);
            const isChrome = /Chrome/.test(navigator.userAgent);
            const isSafari = /Safari/.test(navigator.userAgent) && !isChrome;
            const isFirefox = /Firefox/.test(navigator.userAgent);

            let warnings = [];

            // Protocol check
            if (location.protocol !== 'https:' && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
                warnings.push('⚠️ Camera requires HTTPS on mobile devices');
            }

            // Browser-specific warnings
            if (isIOS && !isSafari) {
                warnings.push('📱 iOS: Safari works best for camera access');
            }

            if (isAndroid && isChrome) {
                warnings.push('🔧 Chrome Android: Enable "Insecure origins" in chrome://flags');
            }

            // getUserMedia support check
            if (!navigator.mediaDevices && !navigator.getUserMedia && !navigator.webkitGetUserMedia && !navigator.mozGetUserMedia) {
                warnings.push('❌ Your browser does not support camera access');
            }

            if (warnings.length > 0) {
                showError(warnings.join('\n'));
            }
        }

        // Run detection on page load
        detectBrowserIssues();
    </script>
</body>
</html>
