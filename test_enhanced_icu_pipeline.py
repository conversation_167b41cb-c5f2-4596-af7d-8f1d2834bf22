#!/usr/bin/env python3
"""
Enhanced ICU Pipeline Comprehensive Validation Test
==================================================

Tests the enhanced ICU geometric cropping pipeline on both speaker set and GRID corpus videos
to validate multi-format robustness and strict lip-reading model compliance.

VALIDATION OBJECTIVES:
- Test 35 speaker set videos (5 per class) + 10 GRID corpus videos = 45 total
- Verify 100% format compliance: 96×64 grayscale, exactly 32 frames
- Validate context preservation for pre-cropped videos
- Compare with original pipeline performance
- Generate comprehensive visual inspection interface

EXPECTED OUTCOMES:
- Universal format compliance: ALL videos output exactly 96×64 grayscale with 32 frames
- Context preservation: Pre-cropped speaker sets maintain lip context without over-cropping
- Multi-format robustness: Handle both speaker sets and GRID corpus appropriately
- 100% pass rate on 5-point validation checklist

Author: Augment Agent
Date: 2025-09-29
Status: Comprehensive Multi-Format Validation
"""

import sys
import os
import random
import shutil
import json
import time
import webbrowser
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

# Add src to path for imports
sys.path.append(str(Path(__file__).parent / "src"))

from preprocessing.icu_geometric_crop_enhanced import EnhancedICUGeometricCropper

class EnhancedPipelineValidator:
    """
    Comprehensive validator for enhanced ICU pipeline across multiple video formats.
    """
    
    def __init__(self):
        """Initialize validator with test configuration."""
        self.base_dir = Path.cwd()
        self.validation_dir = self.base_dir / "enhanced_pipeline_validation"
        self.temp_input_dir = self.validation_dir / "temp_input"
        self.processed_dir = self.validation_dir / "processed"
        self.debug_dir = self.validation_dir / "debug_info"
        
        # Create directories
        for dir_path in [self.validation_dir, self.temp_input_dir, self.processed_dir, self.debug_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # Test configuration
        self.speaker_set_classes = [
            "doctor", "glasses", "i_need_to_move", "my_back_hurts", 
            "my_mouth_is_dry", "phone", "pillow"
        ]
        self.videos_per_class = 5
        self.grid_videos_count = 10
        
        # Results storage
        self.test_results = {
            'speaker_set_results': [],
            'grid_corpus_results': [],
            'summary_stats': {},
            'validation_timestamp': datetime.now().isoformat()
        }
    
    def select_speaker_set_videos(self) -> List[Path]:
        """
        Select exactly 5 videos from each speaker set class.
        
        Returns:
            List of selected speaker set video paths
        """
        selected_videos = []
        speaker_sets_dir = self.base_dir / "speaker_sets" / "full_speaker_sets_top7"
        
        print(f"🎯 Selecting speaker set videos from: {speaker_sets_dir}")
        
        for class_name in self.speaker_set_classes:
            class_videos = []
            
            # Search all speaker directories for this class
            for speaker_dir in speaker_sets_dir.iterdir():
                if speaker_dir.is_dir():
                    class_dir = speaker_dir / class_name
                    if class_dir.exists():
                        # Find video files in this class directory
                        for video_file in class_dir.glob("*.mp4"):
                            class_videos.append(video_file)
            
            # Randomly select videos for this class
            if len(class_videos) >= self.videos_per_class:
                selected = random.sample(class_videos, self.videos_per_class)
            else:
                selected = class_videos  # Take all available if less than target
            
            selected_videos.extend(selected)
            print(f"   📁 {class_name}: {len(selected)} videos selected")
        
        print(f"✅ Total speaker set videos selected: {len(selected_videos)}")
        return selected_videos
    
    def select_grid_corpus_videos(self) -> List[Path]:
        """
        Select random videos from GRID corpus (s1 and s4 speakers).
        
        Returns:
            List of selected GRID corpus video paths
        """
        selected_videos = []
        grid_base_dir = self.base_dir / "GRID_talker_sets"
        
        print(f"🎯 Selecting GRID corpus videos from: {grid_base_dir}")
        
        # Look for speaker directories
        speaker_dirs = []
        for potential_speaker in ["s1", "s4", "talker 1", "talker 4"]:
            speaker_path = grid_base_dir / potential_speaker
            if speaker_path.exists():
                speaker_dirs.append(speaker_path)
        
        # Collect all video files from speaker directories
        all_grid_videos = []
        for speaker_dir in speaker_dirs:
            for video_file in speaker_dir.rglob("*.mp4"):
                all_grid_videos.append(video_file)
        
        # Randomly select videos
        if len(all_grid_videos) >= self.grid_videos_count:
            selected_videos = random.sample(all_grid_videos, self.grid_videos_count)
        else:
            selected_videos = all_grid_videos  # Take all available
        
        print(f"✅ Total GRID corpus videos selected: {len(selected_videos)}")
        return selected_videos
    
    def copy_videos_to_temp(self, video_paths: List[Path], prefix: str) -> List[Path]:
        """
        Copy selected videos to temporary input directory.
        
        Args:
            video_paths: List of video paths to copy
            prefix: Prefix for copied files (e.g., 'speaker_set', 'grid_corpus')
            
        Returns:
            List of copied video paths
        """
        copied_paths = []
        
        for i, video_path in enumerate(video_paths):
            # Create unique filename
            new_filename = f"{prefix}_{i+1:02d}_{video_path.name}"
            dest_path = self.temp_input_dir / new_filename
            
            # Copy file
            shutil.copy2(video_path, dest_path)
            copied_paths.append(dest_path)
        
        return copied_paths
    
    def run_enhanced_pipeline_test(self) -> Dict[str, Any]:
        """
        Run comprehensive enhanced pipeline test.
        
        Returns:
            Complete test results
        """
        print("🚀 Starting Enhanced ICU Pipeline Comprehensive Validation")
        print("=" * 70)
        
        start_time = time.time()
        
        # Step 1: Select test videos
        print("\n📋 STEP 1: Selecting Test Videos")
        speaker_set_videos = self.select_speaker_set_videos()
        grid_corpus_videos = self.select_grid_corpus_videos()
        
        # Step 2: Copy videos to temp directory
        print("\n📁 STEP 2: Preparing Test Environment")
        speaker_set_temp = self.copy_videos_to_temp(speaker_set_videos, "speaker_set")
        grid_corpus_temp = self.copy_videos_to_temp(grid_corpus_videos, "grid_corpus")
        
        all_test_videos = speaker_set_temp + grid_corpus_temp
        print(f"✅ Prepared {len(all_test_videos)} videos for testing")
        
        # Step 3: Initialize enhanced pipeline
        print("\n🔧 STEP 3: Initializing Enhanced ICU Pipeline")
        enhanced_processor = EnhancedICUGeometricCropper(
            source_dir=str(self.temp_input_dir),
            output_dir=str(self.processed_dir),
            manifest_path=str(self.validation_dir / "enhanced_pipeline_manifest.csv")
        )
        
        # Step 4: Process all videos
        print("\n⚙️  STEP 4: Processing Videos with Enhanced Pipeline")
        processing_results = enhanced_processor.process_all_videos()
        
        # Step 5: Analyze results by format
        print("\n📊 STEP 5: Analyzing Results by Format")
        self.analyze_results_by_format(processing_results, speaker_set_temp, grid_corpus_temp)
        
        # Step 6: Generate comprehensive visual inspection
        print("\n🎨 STEP 6: Generating Visual Inspection Interface")
        self.generate_enhanced_visual_inspection()
        
        # Calculate total time
        total_time = time.time() - start_time
        
        # Step 7: Create final summary
        print("\n📋 STEP 7: Creating Final Summary")
        final_summary = self.create_final_summary(processing_results, total_time)
        
        print(f"\n🎉 Enhanced Pipeline Validation Complete!")
        print(f"⏱️  Total Time: {total_time:.1f}s")
        print(f"📊 Overall Success Rate: {final_summary['overall_success_rate']:.1f}%")
        
        return final_summary
    
    def analyze_results_by_format(self, processing_results: Dict[str, Any], 
                                speaker_set_videos: List[Path], grid_corpus_videos: List[Path]):
        """
        Analyze processing results by video format.
        
        Args:
            processing_results: Results from enhanced pipeline
            speaker_set_videos: List of speaker set video paths
            grid_corpus_videos: List of GRID corpus video paths
        """
        # Separate results by format
        speaker_set_names = {v.name for v in speaker_set_videos}
        grid_corpus_names = {v.name for v in grid_corpus_videos}
        
        for result in processing_results['results']:
            input_name = Path(result['input_path']).name
            
            if any(name in input_name for name in speaker_set_names):
                self.test_results['speaker_set_results'].append(result)
            elif any(name in input_name for name in grid_corpus_names):
                self.test_results['grid_corpus_results'].append(result)
        
        # Calculate format-specific statistics
        speaker_set_success = sum(1 for r in self.test_results['speaker_set_results'] if r['success'])
        grid_corpus_success = sum(1 for r in self.test_results['grid_corpus_results'] if r['success'])
        
        self.test_results['summary_stats'] = {
            'speaker_set': {
                'total': len(self.test_results['speaker_set_results']),
                'successful': speaker_set_success,
                'success_rate': (speaker_set_success / len(self.test_results['speaker_set_results']) * 100) 
                               if self.test_results['speaker_set_results'] else 0
            },
            'grid_corpus': {
                'total': len(self.test_results['grid_corpus_results']),
                'successful': grid_corpus_success,
                'success_rate': (grid_corpus_success / len(self.test_results['grid_corpus_results']) * 100) 
                               if self.test_results['grid_corpus_results'] else 0
            }
        }
        
        print(f"📊 Speaker Set Results: {speaker_set_success}/{len(self.test_results['speaker_set_results'])} "
              f"({self.test_results['summary_stats']['speaker_set']['success_rate']:.1f}%)")
        print(f"📊 GRID Corpus Results: {grid_corpus_success}/{len(self.test_results['grid_corpus_results'])} "
              f"({self.test_results['summary_stats']['grid_corpus']['success_rate']:.1f}%)")
    
    def create_final_summary(self, processing_results: Dict[str, Any], total_time: float) -> Dict[str, Any]:
        """
        Create comprehensive final summary.
        
        Args:
            processing_results: Processing results from enhanced pipeline
            total_time: Total processing time
            
        Returns:
            Final summary dictionary
        """
        total_videos = len(processing_results['results'])
        total_successful = sum(1 for r in processing_results['results'] if r['success'])
        overall_success_rate = (total_successful / total_videos * 100) if total_videos > 0 else 0
        
        # Validation compliance analysis
        validation_compliance = {
            'resolution_compliance': 0,
            'channel_compliance': 0,
            'frame_count_compliance': 0,
            'mouth_visibility_compliance': 0,
            'file_format_compliance': 0
        }
        
        for result in processing_results['results']:
            if result['success'] and 'validation_results' in result:
                val_results = result['validation_results']
                if val_results.get('resolution_check', False):
                    validation_compliance['resolution_compliance'] += 1
                if val_results.get('channel_check', False):
                    validation_compliance['channel_compliance'] += 1
                if val_results.get('frame_count_check', False):
                    validation_compliance['frame_count_compliance'] += 1
                if val_results.get('mouth_visibility_check', False):
                    validation_compliance['mouth_visibility_compliance'] += 1
                if val_results.get('file_format_check', False):
                    validation_compliance['file_format_compliance'] += 1
        
        final_summary = {
            'test_type': 'enhanced_icu_pipeline_comprehensive_validation',
            'total_videos_tested': total_videos,
            'total_successful': total_successful,
            'total_failed': total_videos - total_successful,
            'overall_success_rate': overall_success_rate,
            'processing_time': total_time,
            'format_specific_results': self.test_results['summary_stats'],
            'validation_compliance': validation_compliance,
            'pipeline_version': 'enhanced_icu_v1.0',
            'test_timestamp': datetime.now().isoformat(),
            'detailed_results': self.test_results
        }
        
        # Save final summary
        with open(self.validation_dir / "enhanced_pipeline_final_summary.json", 'w') as f:
            json.dump(final_summary, f, indent=2)
        
        return final_summary

    def generate_enhanced_visual_inspection(self):
        """
        Generate comprehensive visual inspection interface with format comparisons.
        """
        # Calculate summary statistics
        speaker_set_success_rate = self.test_results['summary_stats'].get('speaker_set', {}).get('success_rate', 0)
        grid_corpus_success_rate = self.test_results['summary_stats'].get('grid_corpus', {}).get('success_rate', 0)
        total_videos = len(self.test_results['speaker_set_results']) + len(self.test_results['grid_corpus_results'])
        total_successful = len([r for r in self.test_results['speaker_set_results'] + self.test_results['grid_corpus_results'] if r['success']])

        html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced ICU Pipeline Validation Results</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }}
        .header p {{
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }}
        .stat-card {{
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            text-align: center;
            border-left: 5px solid #4CAF50;
        }}
        .stat-number {{
            font-size: 2.5em;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 10px;
        }}
        .stat-label {{
            font-size: 1.1em;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}
        .section {{
            padding: 30px;
            border-bottom: 1px solid #eee;
        }}
        .section h2 {{
            color: #333;
            border-bottom: 3px solid #4CAF50;
            padding-bottom: 10px;
            margin-bottom: 25px;
        }}
        .video-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 25px;
            margin-top: 20px;
        }}
        .video-card {{
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }}
        .video-card.success {{
            border-left: 5px solid #4CAF50;
        }}
        .video-card.failed {{
            border-left: 5px solid #f44336;
        }}
        .video-title {{
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }}
        .video-details {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 0.9em;
        }}
        .detail-item {{
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #ddd;
        }}
        .validation-grid {{
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 10px;
            margin-top: 15px;
        }}
        .validation-item {{
            text-align: center;
            padding: 10px;
            border-radius: 5px;
            font-size: 0.8em;
        }}
        .validation-item.pass {{
            background: #d4edda;
            color: #155724;
        }}
        .validation-item.fail {{
            background: #f8d7da;
            color: #721c24;
        }}
        .format-badge {{
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
            margin-bottom: 10px;
        }}
        .format-badge.speaker-set {{
            background: #e3f2fd;
            color: #1976d2;
        }}
        .format-badge.grid-corpus {{
            background: #f3e5f5;
            color: #7b1fa2;
        }}
        .timestamp {{
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 20px;
            background: #f8f9fa;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Enhanced ICU Pipeline Validation</h1>
            <p>Comprehensive Multi-Format Testing Results</p>
            <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{speaker_set_success_rate:.1f}%</div>
                <div class="stat-label">Speaker Set Success</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{grid_corpus_success_rate:.1f}%</div>
                <div class="stat-label">GRID Corpus Success</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{total_videos}</div>
                <div class="stat-label">Total Videos Tested</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{total_successful}</div>
                <div class="stat-label">Successful Processes</div>
            </div>
        </div>

        <div class="section">
            <h2>📱 Speaker Set Videos Results</h2>
            <div class="video-grid">
"""

        # Add speaker set video cards
        for result in self.test_results['speaker_set_results']:
            status_class = "success" if result['success'] else "failed"
            validation_results = result.get('validation_results', {})

            html_content += f"""
                <div class="video-card {status_class}">
                    <div class="format-badge speaker-set">Speaker Set</div>
                    <div class="video-title">{Path(result['input_path']).name}</div>
                    <div class="video-details">
                        <div class="detail-item">
                            <span>Format Type:</span>
                            <span>{result.get('format_type', 'unknown')}</span>
                        </div>
                        <div class="detail-item">
                            <span>Crop Status:</span>
                            <span>{result.get('crop_status', 'unknown')}</span>
                        </div>
                        <div class="detail-item">
                            <span>Lip Position:</span>
                            <span>{result.get('lip_position', 'unknown')}</span>
                        </div>
                        <div class="detail-item">
                            <span>Output Resolution:</span>
                            <span>{result.get('output_resolution', 'N/A')}</span>
                        </div>
                        <div class="detail-item">
                            <span>Frames:</span>
                            <span>{result.get('processed_frames', 0)}</span>
                        </div>
                        <div class="detail-item">
                            <span>Processing Time:</span>
                            <span>{result.get('processing_time', 0):.2f}s</span>
                        </div>
                    </div>
                    <div class="validation-grid">
                        <div class="validation-item {'pass' if validation_results.get('resolution_check', False) else 'fail'}">
                            Resolution<br>{'✓' if validation_results.get('resolution_check', False) else '✗'}
                        </div>
                        <div class="validation-item {'pass' if validation_results.get('channel_check', False) else 'fail'}">
                            Channels<br>{'✓' if validation_results.get('channel_check', False) else '✗'}
                        </div>
                        <div class="validation-item {'pass' if validation_results.get('frame_count_check', False) else 'fail'}">
                            Frame Count<br>{'✓' if validation_results.get('frame_count_check', False) else '✗'}
                        </div>
                        <div class="validation-item {'pass' if validation_results.get('mouth_visibility_check', False) else 'fail'}">
                            Mouth Visibility<br>{'✓' if validation_results.get('mouth_visibility_check', False) else '✗'}
                        </div>
                        <div class="validation-item {'pass' if validation_results.get('file_format_check', False) else 'fail'}">
                            File Format<br>{'✓' if validation_results.get('file_format_check', False) else '✗'}
                        </div>
                    </div>
                </div>
            """

        html_content += """
            </div>
        </div>

        <div class="section">
            <h2>🎬 GRID Corpus Videos Results</h2>
            <div class="video-grid">
        """

        # Add GRID corpus video cards
        for result in self.test_results['grid_corpus_results']:
            status_class = "success" if result['success'] else "failed"
            validation_results = result.get('validation_results', {})

            html_content += f"""
                <div class="video-card {status_class}">
                    <div class="format-badge grid-corpus">GRID Corpus</div>
                    <div class="video-title">{Path(result['input_path']).name}</div>
                    <div class="video-details">
                        <div class="detail-item">
                            <span>Format Type:</span>
                            <span>{result.get('format_type', 'unknown')}</span>
                        </div>
                        <div class="detail-item">
                            <span>Crop Status:</span>
                            <span>{result.get('crop_status', 'unknown')}</span>
                        </div>
                        <div class="detail-item">
                            <span>Lip Position:</span>
                            <span>{result.get('lip_position', 'unknown')}</span>
                        </div>
                        <div class="detail-item">
                            <span>Output Resolution:</span>
                            <span>{result.get('output_resolution', 'N/A')}</span>
                        </div>
                        <div class="detail-item">
                            <span>Frames:</span>
                            <span>{result.get('processed_frames', 0)}</span>
                        </div>
                        <div class="detail-item">
                            <span>Processing Time:</span>
                            <span>{result.get('processing_time', 0):.2f}s</span>
                        </div>
                    </div>
                    <div class="validation-grid">
                        <div class="validation-item {'pass' if validation_results.get('resolution_check', False) else 'fail'}">
                            Resolution<br>{'✓' if validation_results.get('resolution_check', False) else '✗'}
                        </div>
                        <div class="validation-item {'pass' if validation_results.get('channel_check', False) else 'fail'}">
                            Channels<br>{'✓' if validation_results.get('channel_check', False) else '✗'}
                        </div>
                        <div class="validation-item {'pass' if validation_results.get('frame_count_check', False) else 'fail'}">
                            Frame Count<br>{'✓' if validation_results.get('frame_count_check', False) else '✗'}
                        </div>
                        <div class="validation-item {'pass' if validation_results.get('mouth_visibility_check', False) else 'fail'}">
                            Mouth Visibility<br>{'✓' if validation_results.get('mouth_visibility_check', False) else '✗'}
                        </div>
                        <div class="validation-item {'pass' if validation_results.get('file_format_check', False) else 'fail'}">
                            File Format<br>{'✓' if validation_results.get('file_format_check', False) else '✗'}
                        </div>
                    </div>
                </div>
            """

        html_content += f"""
            </div>
        </div>

        <div class="timestamp">
            Enhanced ICU Pipeline Validation completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}<br>
            Pipeline Version: enhanced_icu_v1.0 | Total Test Duration: Multiple formats validated
        </div>
    </div>
</body>
</html>
        """

        # Save HTML file
        html_file = self.validation_dir / "enhanced_pipeline_visual_inspection.html"
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"📄 Visual inspection saved: {html_file}")

        # Open in browser
        try:
            webbrowser.open(f"file://{html_file.absolute()}")
            print("🌐 Opening visual inspection in browser...")
        except Exception as e:
            print(f"⚠️  Could not open browser automatically: {e}")
            print(f"📂 Please open manually: {html_file}")


def main():
    """
    Main function to run enhanced pipeline validation.
    """
    print("🚀 Enhanced ICU Pipeline Comprehensive Validation")
    print("=" * 60)
    print("Testing enhanced pipeline on speaker set + GRID corpus videos")
    print("Target: 100% format compliance (96×64 grayscale, 32 frames)")
    print()

    # Set random seed for reproducible results
    random.seed(42)

    # Create and run validator
    validator = EnhancedPipelineValidator()

    try:
        final_results = validator.run_enhanced_pipeline_test()

        print("\n" + "=" * 60)
        print("🎉 ENHANCED PIPELINE VALIDATION COMPLETE!")
        print("=" * 60)
        print(f"📊 Overall Success Rate: {final_results['overall_success_rate']:.1f}%")
        print(f"📱 Speaker Set Success: {final_results['format_specific_results']['speaker_set']['success_rate']:.1f}%")
        print(f"🎬 GRID Corpus Success: {final_results['format_specific_results']['grid_corpus']['success_rate']:.1f}%")
        print(f"⏱️  Total Processing Time: {final_results['processing_time']:.1f}s")
        print()
        print("📄 Results saved in: enhanced_pipeline_validation/")
        print("🌐 Visual inspection interface opened in browser")

        return final_results

    except Exception as e:
        print(f"❌ Validation failed with error: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    main()
