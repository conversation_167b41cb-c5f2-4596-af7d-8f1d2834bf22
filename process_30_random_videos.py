#!/usr/bin/env python3
"""
BATCH PROCESS 30 RANDOM VIDEOS - Direct Lip Crop
=================================================

Process 30 random videos from full_speaker_sets_top7 using the proven direct_lip_crop.py approach.
Uses the same geometric cropping method that achieved 100% success rate.
"""

import cv2
import numpy as np
from pathlib import Path
import logging
import random
import glob

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def direct_lip_crop_video(input_path: Path, output_path: Path):
    """
    Directly crop the lip region without any detection - just geometric targeting.
    Same proven method from direct_lip_crop.py
    """
    
    logger.info(f"🎬 Direct lip cropping: {input_path.name}")
    
    # Load video
    cap = cv2.VideoCapture(str(input_path))
    if not cap.isOpened():
        logger.error(f"Cannot open video: {input_path}")
        return False
    
    # Read all frames
    frames = []
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        frames.append(frame)
    cap.release()
    
    if not frames:
        logger.error("No frames found")
        return False
    
    logger.info(f"📹 Loaded {len(frames)} frames, {frames[0].shape[1]}×{frames[0].shape[0]} pixels")
    
    # DIRECT GEOMETRIC CROP - NO DETECTION (proven coordinates)
    h, w = frames[0].shape[:2]
    
    # Define EXACT crop coordinates for lip region
    crop_x1 = int(w * 0.25)  # 25% from left (100 pixels for 400px width)
    crop_x2 = int(w * 0.75)  # 75% from left (300 pixels for 400px width) 
    crop_y1 = int(h * 0.05)  # 5% from top (10 pixels for 200px height)
    crop_y2 = int(h * 0.45)  # 45% from top (90 pixels for 200px height)
    
    # Process frames with direct cropping
    processed_frames = []
    
    for frame in frames:
        # DIRECT CROP - no detection, just extract the region
        cropped = frame[crop_y1:crop_y2, crop_x1:crop_x2]
        
        # Convert to grayscale
        gray = cv2.cvtColor(cropped, cv2.COLOR_BGR2GRAY)
        
        # Resize to 96×64
        resized = cv2.resize(gray, (96, 64), interpolation=cv2.INTER_LINEAR)
        
        processed_frames.append(resized)
    
    # Sample to exactly 32 frames (uniform sampling)
    if len(processed_frames) >= 32:
        indices = np.linspace(0, len(processed_frames) - 1, 32, dtype=int)
        sampled_frames = [processed_frames[i] for i in indices]
    else:
        # Pad with repeated frames if too few
        sampled_frames = processed_frames[:]
        while len(sampled_frames) < 32:
            sampled_frames.extend(processed_frames[:32 - len(sampled_frames)])
        sampled_frames = sampled_frames[:32]
    
    # Create output directory
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Save as MP4 at 15 FPS
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(output_path), fourcc, 15.0, (96, 64), isColor=False)
    
    for frame in sampled_frames:
        out.write(frame)
    
    out.release()
    
    logger.info(f"💾 Saved directly cropped video: {output_path}")
    return True

def main():
    """Process 30 random videos from full_speaker_sets_top7."""
    
    # Input and output directories
    input_dir = Path("/Users/<USER>/Desktop/LRP classifier 11.9.25/speaker_sets/full_speaker_sets_top7")
    output_dir = Path("/Users/<USER>/Desktop/LRP classifier 11.9.25/speaker_sets")
    
    print("🎯 BATCH PROCESS 30 RANDOM VIDEOS - DIRECT LIP CROP")
    print("="*65)
    print("Strategy:")
    print("• Use proven direct_lip_crop.py method")
    print("• Skip ALL detection algorithms")
    print("• Use FIXED geometric coordinates (x=100-300, y=10-90)")
    print("• Process 30 random videos from all speakers/classes")
    print("• Direct crop → grayscale → resize → sample")
    print("• Save as MP4 at 15 FPS")
    print("="*65)
    
    # Find all MP4 videos recursively
    all_videos = []
    for video_path in input_dir.rglob("*.mp4"):
        all_videos.append(video_path)
    
    if len(all_videos) < 30:
        print(f"❌ Error: Only {len(all_videos)} videos found, need at least 30")
        return
    
    print(f"📁 Found {len(all_videos)} total videos across all speakers/classes")
    
    # Randomly select 30 videos
    random.seed(42)  # For reproducible results
    selected_videos = random.sample(all_videos, 30)
    
    print(f"🎲 Randomly selected 30 videos for processing")
    
    success_count = 0
    
    for i, input_video in enumerate(selected_videos, 1):
        print(f"\n🎬 Processing video {i}/30: {input_video.name}")
        print(f"📂 From: {input_video.parent.name}")
        
        # Create output filename with speaker and class info
        speaker_name = input_video.parent.parent.name  # e.g., "speaker_1 "
        class_name = input_video.parent.name           # e.g., "doctor"
        
        output_name = f"processed_{speaker_name.strip()}_{class_name}_{input_video.stem}.mp4"
        output_video = output_dir / output_name
        
        # Process the video
        success = direct_lip_crop_video(input_video, output_video)
        
        if success:
            print(f"✅ Video {i} completed: {output_name}")
            success_count += 1
        else:
            print(f"❌ Video {i} failed")
    
    print(f"\n🎉 BATCH PROCESSING COMPLETE!")
    print(f"📊 Successfully processed: {success_count}/30 videos")
    print(f"📁 Output location: {output_dir}")
    print("📏 All videos: 32 frames, 96×64 pixels, grayscale, 15 FPS")
    print("🎯 Used proven direct geometric cropping - no detection needed!")

if __name__ == "__main__":
    main()
