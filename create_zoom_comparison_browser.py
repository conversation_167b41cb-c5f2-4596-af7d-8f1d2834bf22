#!/usr/bin/env python3
"""
Create visual comparison browser showing original vs 15% zoomed out cropping
"""

import cv2
import base64
import numpy as np
from pathlib import Path

def create_comparison_browser():
    # Find processed videos from both versions
    original_dir = Path('test_corrected_grayscale_output')
    zoomed_dir = Path('test_zoomed_out_15pct_output')
    
    original_videos = list(original_dir.glob('*.mp4'))
    zoomed_videos = list(zoomed_dir.glob('*.mp4'))
    
    print(f'Creating comparison browser...')
    print(f'Original cropping: {len(original_videos)} videos')
    print(f'Zoomed out 15%: {len(zoomed_videos)} videos')
    
    html_content = '''<!DOCTYPE html>
<html>
<head>
    <title>🔍 ICU Pipeline: Original vs 15% Zoomed Out Comparison</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .header { background: #4299e1; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .comparison-info { background: #e6fffa; border: 2px solid #38b2ac; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .video-comparison { background: white; margin-bottom: 30px; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .comparison-title { font-size: 18px; font-weight: bold; color: #2d3748; margin-bottom: 15px; }
        .crop-comparison { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px; }
        .crop-section { border: 2px solid #e2e8f0; border-radius: 8px; padding: 15px; }
        .crop-section.original { border-color: #f6ad55; background: #fef5e7; }
        .crop-section.zoomed { border-color: #68d391; background: #f0fff4; }
        .crop-title { font-size: 14px; font-weight: bold; margin-bottom: 10px; text-align: center; }
        .crop-title.original { color: #c05621; }
        .crop-title.zoomed { color: #2f855a; }
        .frames-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 8px; }
        .frame-item { text-align: center; }
        .frame-item img { width: 100%; border: 1px solid #e2e8f0; border-radius: 4px; }
        .frame-label { font-size: 10px; color: #a0aec0; margin-top: 5px; }
        .specs { font-size: 12px; color: #718096; text-align: center; margin-top: 10px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 ICU Pipeline Cropping Comparison</h1>
        <p><strong>Comparison:</strong> Original Cropping vs 15% Zoomed Out</p>
        <p>Visual inspection to determine optimal crop parameters</p>
    </div>
    
    <div class="comparison-info">
        <h3>📊 Cropping Parameter Comparison</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
                <h4 style="color: #c05621;">🔸 Original Cropping</h4>
                <p><strong>Height:</strong> 70% of frame (top portion)</p>
                <p><strong>Width:</strong> 50% of frame (middle portion)</p>
                <p><strong>Result:</strong> Tighter crop, more focused on mouth</p>
            </div>
            <div>
                <h4 style="color: #2f855a;">🔹 15% Zoomed Out</h4>
                <p><strong>Height:</strong> 85% of frame (top portion)</p>
                <p><strong>Width:</strong> 65% of frame (middle portion)</p>
                <p><strong>Result:</strong> More context around mouth region</p>
            </div>
        </div>
    </div>
'''
    
    # Find matching videos between both sets
    original_names = {v.name.replace('icu_cropped_', '') for v in original_videos}
    zoomed_names = {v.name.replace('icu_cropped_', '') for v in zoomed_videos}
    common_names = original_names.intersection(zoomed_names)
    
    comparison_count = 0
    for base_name in sorted(common_names):
        # Find matching videos
        original_video = None
        zoomed_video = None
        
        for v in original_videos:
            if v.name.replace('icu_cropped_', '') == base_name:
                original_video = v
                break
                
        for v in zoomed_videos:
            if v.name.replace('icu_cropped_', '') == base_name:
                zoomed_video = v
                break
        
        if original_video and zoomed_video:
            comparison_count += 1
            print(f'Creating comparison {comparison_count}: {base_name}')
            
            html_content += f'''
    <div class="video-comparison">
        <div class="comparison-title">{comparison_count}. {base_name}</div>
        <div class="crop-comparison">
'''
            
            # Process original video
            cap_orig = cv2.VideoCapture(str(original_video))
            if cap_orig.isOpened():
                frame_count = int(cap_orig.get(cv2.CAP_PROP_FRAME_COUNT))
                
                html_content += '''
            <div class="crop-section original">
                <div class="crop-title original">🔸 Original Cropping (70% × 50%)</div>
                <div class="frames-grid">
'''
                
                # Extract 4 frames from original
                for j in range(4):
                    frame_idx = int(j * frame_count / 4) if frame_count > 4 else j
                    cap_orig.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                    ret, frame = cap_orig.read()
                    
                    if ret:
                        _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
                        frame_b64 = base64.b64encode(buffer).decode('utf-8')
                        
                        html_content += f'''
                    <div class="frame-item">
                        <img src="data:image/jpeg;base64,{frame_b64}" alt="Original Frame {j+1}">
                        <div class="frame-label">Frame {frame_idx+1}</div>
                    </div>
'''
                
                html_content += '''
                </div>
                <div class="specs">Tighter crop, focused view</div>
            </div>
'''
                cap_orig.release()
            
            # Process zoomed out video
            cap_zoom = cv2.VideoCapture(str(zoomed_video))
            if cap_zoom.isOpened():
                frame_count = int(cap_zoom.get(cv2.CAP_PROP_FRAME_COUNT))
                
                html_content += '''
            <div class="crop-section zoomed">
                <div class="crop-title zoomed">🔹 15% Zoomed Out (85% × 65%)</div>
                <div class="frames-grid">
'''
                
                # Extract 4 frames from zoomed
                for j in range(4):
                    frame_idx = int(j * frame_count / 4) if frame_count > 4 else j
                    cap_zoom.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                    ret, frame = cap_zoom.read()
                    
                    if ret:
                        _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
                        frame_b64 = base64.b64encode(buffer).decode('utf-8')
                        
                        html_content += f'''
                    <div class="frame-item">
                        <img src="data:image/jpeg;base64,{frame_b64}" alt="Zoomed Frame {j+1}">
                        <div class="frame-label">Frame {frame_idx+1}</div>
                    </div>
'''
                
                html_content += '''
                </div>
                <div class="specs">More context, wider view</div>
            </div>
'''
                cap_zoom.release()
            
            html_content += '''
        </div>
    </div>
'''
    
    html_content += f'''
    <div class="comparison-info">
        <h3>🎯 Visual Comparison Summary</h3>
        <p><strong>Total Comparisons:</strong> {comparison_count} video pairs</p>
        <p><strong>What to Look For:</strong></p>
        <ul>
            <li><strong>Mouth Visibility:</strong> Both versions should show clear lip movements</li>
            <li><strong>Context Amount:</strong> Zoomed out version should show more surrounding facial area</li>
            <li><strong>Cropping Preference:</strong> Which provides better balance of focus vs context?</li>
            <li><strong>Quality:</strong> Both should maintain good contrast and clarity</li>
        </ul>
        <p><strong>Decision Point:</strong> Choose the cropping level that provides optimal lip-reading context without being too tight or too loose.</p>
    </div>
</body>
</html>
'''
    
    # Save HTML file
    html_file = Path('icu_cropping_comparison.html')
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f'✅ Comparison browser created: {html_file.absolute()}')
    print(f'📊 Created {comparison_count} side-by-side comparisons')
    return html_file

if __name__ == '__main__':
    create_comparison_browser()
