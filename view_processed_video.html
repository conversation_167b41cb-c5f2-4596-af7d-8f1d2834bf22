<!DOCTYPE html>
<html>
<head>
    <title>✅ Processed Video Result</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f0f9ff; 
            text-align: center;
        }
        .header { 
            background: linear-gradient(135deg, #10b981 0%, #059669 100%); 
            color: white; 
            padding: 25px; 
            border-radius: 12px; 
            margin-bottom: 25px; 
        }
        .video-container { 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 4px 6px rgba(0,0,0,0.1); 
            margin-bottom: 25px;
        }
        .video-player { 
            border: 3px solid #10b981; 
            border-radius: 8px; 
            margin: 20px auto;
        }
        .specs { 
            background: #f0f9ff; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 20px 0;
            display: inline-block;
            text-align: left;
        }
        .spec-item { 
            margin: 8px 0; 
            font-family: monospace;
        }
        .success { 
            color: #10b981; 
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>✅ Video Processing Complete</h1>
        <p><strong>Comparison: Basic Processing vs Lip-Cropped Processing</strong></p>
    </div>

    <div class="video-container">
        <h2>📹 Video Processing Comparison</h2>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin: 20px 0;">
            <div style="text-align: center;">
                <h3 style="color: #ef4444;">❌ Basic Processing (No Lip Cropping)</h3>
                <video class="video-player" width="288" height="192" controls loop>
                    <source src="test clips/processed_my_back_hurts.mp4" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
                <p style="color: #ef4444; font-weight: bold;">Shows full face region - not suitable for lip-reading</p>
            </div>

            <div style="text-align: center;">
                <h3 style="color: #10b981;">✅ Lip-Cropped Processing (Fixed)</h3>
                <video class="video-player" width="288" height="192" controls loop>
                    <source src="test clips/lip_cropped_my_back_hurts.mp4" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
                <p style="color: #10b981; font-weight: bold;">Tight crop around mouth/lip region - perfect for lip-reading!</p>
            </div>
        </div>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin: 20px 0;">
            <div class="specs">
                <h3>📊 Basic Processing Specs</h3>
                <div class="spec-item"><strong>Resolution:</strong> <span class="success">96×64 pixels ✅</span></div>
                <div class="spec-item"><strong>Frame Count:</strong> <span class="success">32 frames ✅</span></div>
                <div class="spec-item"><strong>Color:</strong> <span class="success">Grayscale ✅</span></div>
                <div class="spec-item"><strong>Format:</strong> <span class="success">MP4 ✅</span></div>
                <div class="spec-item"><strong>Frame Rate:</strong> 30 FPS</div>
                <div class="spec-item" style="color: #ef4444;"><strong>Lip Cropping:</strong> ❌ None</div>
            </div>

            <div class="specs">
                <h3>📊 Lip-Cropped Processing Specs</h3>
                <div class="spec-item"><strong>Resolution:</strong> <span class="success">96×64 pixels ✅</span></div>
                <div class="spec-item"><strong>Frame Count:</strong> <span class="success">32 frames ✅</span></div>
                <div class="spec-item"><strong>Color:</strong> <span class="success">Grayscale ✅</span></div>
                <div class="spec-item"><strong>Format:</strong> <span class="success">MP4 ✅</span></div>
                <div class="spec-item"><strong>Frame Rate:</strong> <span class="success">15 FPS ✅</span></div>
                <div class="spec-item" style="color: #10b981;"><strong>Lip Cropping:</strong> ✅ Tight crop around mouth</div>
            </div>
        </div>
        
        <div style="background: #ecfdf5; padding: 20px; border-radius: 8px; margin: 20px 0; border: 2px solid #10b981;">
            <h3>🎯 Problem Analysis & Solution</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h4 style="color: #ef4444;">❌ Problem Identified:</h4>
                    <p>The basic processing pipeline lacked proper lip detection/cropping:</p>
                    <ul>
                        <li>❌ No lip region detection</li>
                        <li>❌ Full face shown instead of mouth</li>
                        <li>❌ Not suitable for lip-reading training</li>
                        <li>❌ 30 FPS (too high for lip-reading)</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: #10b981;">✅ Solution Implemented:</h4>
                    <p>Added OpenCV-based lip detection and cropping:</p>
                    <ul>
                        <li>✅ Face detection + geometric lip estimation</li>
                        <li>✅ Edge detection for mouth boundary refinement</li>
                        <li>✅ Tight cropping around lip/mouth region</li>
                        <li>✅ 15 FPS (optimal for lip-reading)</li>
                        <li>✅ EMA smoothing for stable crops</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div style="background: #f9fafb; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h4>📁 File Locations</h4>
            <p><strong>Basic Processing:</strong><br><code>/Users/<USER>/Desktop/LRP classifier 11.9.25/test clips/processed_my_back_hurts.mp4</code></p>
            <p><strong>Lip-Cropped (Corrected):</strong><br><code>/Users/<USER>/Desktop/LRP classifier 11.9.25/test clips/lip_cropped_my_back_hurts.mp4</code></p>
        </div>
    </div>
    
    <div style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
        <h3>🎉 Processing Summary</h3>
        <p><strong>Original Video:</strong> my_back_hurts__useruser01__18to39__male__not_specified__20250722T014409.mp4</p>
        <p><strong>Original Specs:</strong> 69 frames, 400×200 pixels, color</p>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0;">
            <div style="background: #fee2e2; padding: 15px; border-radius: 8px; border: 2px solid #ef4444;">
                <h4 style="color: #ef4444;">❌ Basic Processing Result</h4>
                <p><strong>File:</strong> processed_my_back_hurts.mp4</p>
                <p><strong>Specs:</strong> 32 frames, 96×64 pixels, grayscale, 30 FPS</p>
                <p><strong>Issue:</strong> Shows full face, not suitable for lip-reading</p>
            </div>

            <div style="background: #d1fae5; padding: 15px; border-radius: 8px; border: 2px solid #10b981;">
                <h4 style="color: #10b981;">✅ Lip-Cropped Result (CORRECTED)</h4>
                <p><strong>File:</strong> lip_cropped_my_back_hurts.mp4</p>
                <p><strong>Specs:</strong> 32 frames, 96×64 pixels, grayscale, 15 FPS</p>
                <p><strong>Success:</strong> Tight crop around mouth - perfect for lip-reading!</p>
            </div>
        </div>

        <div style="background: #ecfdf5; padding: 15px; border-radius: 8px; margin: 15px 0; border: 2px solid #10b981;">
            <h4 style="color: #065f46;">🎯 Final Result</h4>
            <p><strong>Problem solved!</strong> The lip detection and cropping pipeline now produces videos with tight crops around the mouth/lip region, making them suitable for lip-reading model training. All technical specifications maintained: 32 frames, 96×64 pixels, grayscale, MP4 format, with the added benefit of 15 FPS (optimal for lip-reading) and proper lip region focus.</p>
        </div>
    </div>
</body>
</html>
