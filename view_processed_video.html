<!DOCTYPE html>
<html>
<head>
    <title>✅ Processed Video Result</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f0f9ff; 
            text-align: center;
        }
        .header { 
            background: linear-gradient(135deg, #10b981 0%, #059669 100%); 
            color: white; 
            padding: 25px; 
            border-radius: 12px; 
            margin-bottom: 25px; 
        }
        .video-container { 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 4px 6px rgba(0,0,0,0.1); 
            margin-bottom: 25px;
        }
        .video-player { 
            border: 3px solid #10b981; 
            border-radius: 8px; 
            margin: 20px auto;
        }
        .specs { 
            background: #f0f9ff; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 20px 0;
            display: inline-block;
            text-align: left;
        }
        .spec-item { 
            margin: 8px 0; 
            font-family: monospace;
        }
        .success { 
            color: #10b981; 
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>✅ Video Processing Complete</h1>
        <p><strong>Successfully processed using scripts directory</strong></p>
    </div>
    
    <div class="video-container">
        <h2>📹 Processed Video Result</h2>
        <video class="video-player" width="288" height="192" controls loop>
            <source src="test clips/processed_my_back_hurts.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
        
        <div class="specs">
            <h3>📊 Video Specifications</h3>
            <div class="spec-item"><strong>Resolution:</strong> <span class="success">96×64 pixels ✅</span></div>
            <div class="spec-item"><strong>Frame Count:</strong> <span class="success">32 frames ✅</span></div>
            <div class="spec-item"><strong>Color:</strong> <span class="success">Grayscale ✅</span></div>
            <div class="spec-item"><strong>Format:</strong> <span class="success">MP4 ✅</span></div>
            <div class="spec-item"><strong>Duration:</strong> 1.07 seconds</div>
            <div class="spec-item"><strong>Frame Rate:</strong> 30 FPS</div>
        </div>
        
        <div style="background: #ecfdf5; padding: 20px; border-radius: 8px; margin: 20px 0; border: 2px solid #10b981;">
            <h3>🎯 Transformations Applied</h3>
            <p><strong>✅ Convert → grayscale</strong> (from color)</p>
            <p><strong>✅ Resize → 96×64 pixels</strong> (from 400×200)</p>
            <p><strong>✅ Sample to exactly 32 frames</strong> (from 69 frames, uniform sampling)</p>
            <p><strong>✅ Save as .mp4</strong> (MPEG-4 format)</p>
        </div>
        
        <div style="background: #f9fafb; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h4>📁 File Location</h4>
            <p><code>/Users/<USER>/Desktop/LRP classifier 11.9.25/test clips/processed_my_back_hurts.mp4</code></p>
        </div>
    </div>
    
    <div style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
        <h3>🎉 Processing Summary</h3>
        <p><strong>Original Video:</strong> my_back_hurts__useruser01__18to39__male__not_specified__20250722T014409.mp4</p>
        <p><strong>Original Specs:</strong> 69 frames, 400×200 pixels, color</p>
        <p><strong>Processed Video:</strong> processed_my_back_hurts.mp4</p>
        <p><strong>Final Specs:</strong> 32 frames, 96×64 pixels, grayscale</p>
        <p><strong>All transformations completed successfully! ✅</strong></p>
    </div>
</body>
</html>
