
# Speaker Set Video Processing Diagnostic Report

## 🎯 Test Results Summary
- **Total Videos Tested**: 14
- **Success Rate**: 28.6%
- **Main Issue**: Mouth visibility failures

## 🔍 Key Findings

### 1. Contrast Analysis
- **Successful Videos Average Contrast**: 20.80
- **Failed Videos Average Contrast**: 14.92
- **Threshold Issue**: Many videos fall below 10.0 contrast threshold

### 2. Video Format Differences
Speaker set videos have different characteristics compared to GRID corpus:

#### GRID Corpus (Works Well):
- Lips typically centered in frame
- Full face visible
- Consistent lighting
- Standard face positioning

#### Speaker Set Videos (Challenging):
- Lips often positioned in TOP portion of frame
- Cropped to show lower half of face only
- Variable lighting conditions
- Different face angles and positions

### 3. Pipeline Adaptation Issues

The corrected preprocessing pipeline was optimized for GRID corpus videos where:
- Face detection finds full faces
- Mouth ROI extraction targets lower 50% of detected face
- Geometric cropping takes bottom 60% of ROI

**Problem with Speaker Set Videos**:
- Videos are already cropped to show lower face
- Face detection may struggle with partial faces
- Mouth region may already be at the TOP of the cropped video
- Current pipeline logic assumes full face detection

## 🛠️ Recommended Solutions

### Option 1: Adaptive Pipeline (Recommended)
Create a hybrid preprocessing pipeline that:
1. **Detects video format** (full face vs cropped face)
2. **Adapts ROI extraction** based on format:
   - Full face: Use current corrected logic (bottom 50% of face)
   - Cropped face: Use different logic (top 60% of frame)
3. **Adjusts geometric cropping** accordingly

### Option 2: Speaker Set Specific Pipeline
Create a separate preprocessing pipeline optimized for speaker set videos:
- Skip face detection (assume entire frame is face region)
- Extract mouth ROI from top portion of frame
- Use different contrast thresholds

### Option 3: Unified Robust Pipeline
Enhance the current pipeline with:
- Multiple face detection strategies
- Adaptive ROI positioning based on detected face size/position
- Dynamic contrast thresholds based on video characteristics

## 📊 Class-Specific Analysis

### Successful Classes:
- **Phone**: 100% success - likely has good mouth visibility in top portion
- **I Need To Move**: 50% success - mixed results
- **Pillow**: 50% success - mixed results

### Failed Classes:
- **Doctor**: 0% success - mouth region not properly extracted
- **Glasses**: 0% success - possible interference from glasses
- **My Back Hurts**: 0% success - mouth region issues
- **My Mouth Is Dry**: 0% success - ironic given the phrase content

## 🎯 Next Steps

1. **Implement Adaptive Pipeline**: Modify the corrected pipeline to detect and handle both video formats
2. **Test on Larger Sample**: Validate improvements on more speaker set videos
3. **Optimize Thresholds**: Adjust contrast and visibility thresholds for speaker set format
4. **Visual Validation**: Ensure mouth regions are properly extracted in both formats

## 📋 Technical Specifications

### Current Pipeline Assumptions:
- Input: Full face videos (GRID format)
- Face Detection: OpenCV Haar Cascades on full faces
- ROI Extraction: Bottom 50% of detected face
- Geometric Cropping: Bottom 60% of ROI

### Required Adaptations:
- Input: Both full face and cropped face videos
- Face Detection: Adaptive strategies for partial faces
- ROI Extraction: Format-aware positioning
- Geometric Cropping: Dynamic based on face position

---

**Generated**: Unknown
**Status**: Diagnostic Complete - Solutions Identified
