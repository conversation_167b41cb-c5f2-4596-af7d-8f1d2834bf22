#!/usr/bin/env python3
"""
PROCESS ALL VIDEOS - Smart Adaptive Processing with Directory Organization
=========================================================================

Process ALL 804 videos from full_speaker_sets_top7 using smart format detection
and organize them into proper speaker/class directory structure.

Features:
- Smart format detection (400×200 vs 132×100)
- Maintains original directory structure
- Creates organized output folders automatically
- Processes all videos, not just random samples
"""

import cv2
import numpy as np
from pathlib import Path
import logging
import time
from typing import Dict, List

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def smart_process_video(input_path: Path, output_path: Path) -> bool:
    """
    Intelligently process video based on its dimensions.
    Same proven method from smart_process_30_videos.py
    """
    
    try:
        # Load video
        cap = cv2.VideoCapture(str(input_path))
        if not cap.isOpened():
            logger.error(f"Cannot open video: {input_path}")
            return False
        
        # Read all frames
        frames = []
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            frames.append(frame)
        cap.release()
        
        if not frames:
            logger.error("No frames found")
            return False
        
        h, w = frames[0].shape[:2]
        
        # SMART PROCESSING BASED ON VIDEO DIMENSIONS
        processed_frames = []
        
        if w == 400 and h == 200:
            # FULL-SIZE VIDEO: Apply direct lip cropping
            crop_x1 = int(w * 0.25)  # 25% from left (100 pixels)
            crop_x2 = int(w * 0.75)  # 75% from left (300 pixels) 
            crop_y1 = int(h * 0.05)  # 5% from top (10 pixels)
            crop_y2 = int(h * 0.45)  # 45% from top (90 pixels)
            
            for frame in frames:
                # DIRECT CROP - extract lip region
                cropped = frame[crop_y1:crop_y2, crop_x1:crop_x2]
                # Convert to grayscale
                gray = cv2.cvtColor(cropped, cv2.COLOR_BGR2GRAY)
                # Resize to 96×64
                resized = cv2.resize(gray, (96, 64), interpolation=cv2.INTER_LINEAR)
                processed_frames.append(resized)
                
        elif w == 132 and h == 100:
            # ALREADY-CROPPED VIDEO: Skip cropping, just process
            for frame in frames:
                # Convert to grayscale (no cropping needed)
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                # Resize to 96×64
                resized = cv2.resize(gray, (96, 64), interpolation=cv2.INTER_LINEAR)
                processed_frames.append(resized)
                
        else:
            # OTHER FORMAT: Apply proportional cropping
            crop_x1 = int(w * 0.25)  # 25% from left
            crop_x2 = int(w * 0.75)  # 75% from left
            crop_y1 = int(h * 0.05)  # 5% from top
            crop_y2 = int(h * 0.45)  # 45% from top
            
            for frame in frames:
                # Proportional crop
                cropped = frame[crop_y1:crop_y2, crop_x1:crop_x2]
                # Convert to grayscale
                gray = cv2.cvtColor(cropped, cv2.COLOR_BGR2GRAY)
                # Resize to 96×64
                resized = cv2.resize(gray, (96, 64), interpolation=cv2.INTER_LINEAR)
                processed_frames.append(resized)
        
        # Sample to exactly 32 frames (uniform sampling)
        if len(processed_frames) >= 32:
            indices = np.linspace(0, len(processed_frames) - 1, 32, dtype=int)
            sampled_frames = [processed_frames[i] for i in indices]
        else:
            # Pad with repeated frames if too few
            sampled_frames = processed_frames[:]
            while len(sampled_frames) < 32:
                sampled_frames.extend(processed_frames[:32 - len(sampled_frames)])
            sampled_frames = sampled_frames[:32]
        
        # Create output directory
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Save as MP4 at 15 FPS
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(output_path), fourcc, 15.0, (96, 64), isColor=False)
        
        for frame in sampled_frames:
            out.write(frame)
        
        out.release()
        return True
        
    except Exception as e:
        logger.error(f"Error processing {input_path}: {e}")
        return False

def main():
    """Process ALL videos with organized directory structure."""
    
    # Input and output directories
    input_dir = Path("/Users/<USER>/Desktop/LRP classifier 11.9.25/speaker_sets/full_speaker_sets_top7")
    output_dir = Path("/Users/<USER>/Desktop/LRP classifier 11.9.25/speaker_sets/full_speaker_sets_top7_8pm")
    
    print("🎯 PROCESS ALL VIDEOS - SMART ADAPTIVE PROCESSING")
    print("="*65)
    print("Strategy:")
    print("• Process ALL 804 videos (not random sample)")
    print("• Smart format detection: 400×200 vs 132×100")
    print("• Maintain original directory structure")
    print("• Output: 32 frames, 96×64 pixels, grayscale, 15 FPS")
    print("• Organized into speaker/class folders")
    print("="*65)
    
    # Find all MP4 videos recursively with their structure
    all_videos = []
    for video_path in input_dir.rglob("*.mp4"):
        # Extract speaker and class from path
        speaker_name = video_path.parent.parent.name  # e.g., "speaker_1 "
        class_name = video_path.parent.name           # e.g., "doctor"
        all_videos.append((video_path, speaker_name.strip(), class_name))
    
    print(f"📁 Found {len(all_videos)} total videos across all speakers/classes")
    
    # Group by speaker and class for statistics
    speaker_stats = {}
    class_stats = {}
    format_stats = {"400x200": 0, "132x100": 0, "other": 0}
    
    success_count = 0
    start_time = time.time()
    
    for i, (input_video, speaker_name, class_name) in enumerate(all_videos, 1):
        
        # Progress indicator
        if i % 50 == 0 or i == 1:
            elapsed = time.time() - start_time
            rate = i / elapsed if elapsed > 0 else 0
            eta = (len(all_videos) - i) / rate if rate > 0 else 0
            print(f"\n📊 Progress: {i}/{len(all_videos)} ({i/len(all_videos)*100:.1f}%) - {rate:.1f} videos/sec - ETA: {eta/60:.1f}min")
        
        print(f"🎬 Processing {i}/{len(all_videos)}: {input_video.name}")
        print(f"📂 Speaker: {speaker_name} | Class: {class_name}")
        
        # Create organized output path
        output_speaker_dir = output_dir / speaker_name / class_name
        output_filename = f"processed_{input_video.name}"
        output_video = output_speaker_dir / output_filename
        
        # Process the video
        success = smart_process_video(input_video, output_video)
        
        if success:
            success_count += 1
            
            # Update statistics
            speaker_stats[speaker_name] = speaker_stats.get(speaker_name, 0) + 1
            class_stats[class_name] = class_stats.get(class_name, 0) + 1
            
            # Track format statistics
            try:
                cap = cv2.VideoCapture(str(input_video))
                ret, frame = cap.read()
                if ret:
                    h, w = frame.shape[:2]
                    if w == 400 and h == 200:
                        format_stats["400x200"] += 1
                    elif w == 132 and h == 100:
                        format_stats["132x100"] += 1
                    else:
                        format_stats["other"] += 1
                cap.release()
            except:
                pass
            
            if i % 50 == 0:
                print(f"✅ Batch completed: {success_count}/{i} successful")
        else:
            print(f"❌ Failed: {input_video.name}")
    
    # Final results
    total_time = time.time() - start_time
    
    print(f"\n🎉 COMPLETE PROCESSING FINISHED!")
    print(f"📊 Successfully processed: {success_count}/{len(all_videos)} videos ({success_count/len(all_videos)*100:.1f}%)")
    print(f"⏱️ Total time: {total_time/60:.1f} minutes ({total_time/success_count:.2f}s per video)")
    print(f"📁 Output location: {output_dir}")
    
    print(f"\n📈 SPEAKER DISTRIBUTION:")
    for speaker, count in sorted(speaker_stats.items()):
        print(f"• {speaker}: {count} videos")
    
    print(f"\n📝 CLASS DISTRIBUTION:")
    for class_name, count in sorted(class_stats.items()):
        print(f"• {class_name}: {count} videos")
    
    print(f"\n🎬 FORMAT DISTRIBUTION:")
    print(f"• Full-size (400×200): {format_stats['400x200']} videos")
    print(f"• Already-cropped (132×100): {format_stats['132x100']} videos") 
    print(f"• Other formats: {format_stats['other']} videos")
    
    print("🎯 All videos processed with smart adaptive method!")
    print("📏 Output specs: 32 frames, 96×64 pixels, grayscale, 15 FPS")
    print("📁 Organized directory structure maintained!")

if __name__ == "__main__":
    main()
