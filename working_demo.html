<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Lip-Reading Demo - Working Version</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .status-bar {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
        }
        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .status-good { color: #4CAF50; font-weight: bold; }
        .status-warning { color: #FF9800; font-weight: bold; }
        .status-error { color: #f44336; font-weight: bold; }
        
        .video-container {
            position: relative;
            margin: 20px 0;
            text-align: center;
        }
        video {
            width: 100%;
            max-width: 640px;
            height: auto;
            border: 3px solid #374151;
            border-radius: 15px;
            background: #000;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            padding: 15px 30px;
            margin: 10px;
            font-size: 16px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-primary { background: #2196F3; color: white; }
        .btn-success { background: #4CAF50; color: white; }
        .btn-warning { background: #FF9800; color: white; }
        .btn-danger { background: #f44336; color: white; }
        
        button:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
        button:disabled { opacity: 0.5; cursor: not-allowed; transform: none; }
        
        .results {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            display: none;
        }
        
        .log {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Lip-Reading Demo - Enhanced 81.65% Model</h1>
        
        <div class="status-bar">
            <div class="status-item">
                <span>🤖 Model:</span>
                <span id="modelStatus" class="status-good">81.65% Accuracy (721K params)</span>
            </div>
            <div class="status-item">
                <span>🌐 Backend:</span>
                <span id="backendStatus" class="status-warning">Checking...</span>
            </div>
            <div class="status-item">
                <span>📹 Camera:</span>
                <span id="cameraStatus" class="status-warning">Ready</span>
            </div>
        </div>
        
        <div class="video-container">
            <video id="videoElement" autoplay muted playsinline>
                Your browser does not support the video element.
            </video>
        </div>
        
        <div class="controls">
            <button id="testBackendBtn" class="btn-primary">🧪 Test Backend</button>
            <button id="startCameraBtn" class="btn-success" disabled>📹 Start Camera</button>
            <button id="recordBtn" class="btn-warning" disabled>🎬 Record (3s)</button>
            <button id="stopCameraBtn" class="btn-danger" disabled>🛑 Stop Camera</button>
        </div>
        
        <div id="results" class="results">
            <h3>🎯 AI Predictions</h3>
            <div id="predictions"></div>
        </div>
        
        <div class="log" id="logOutput">
            <strong>Activity Log:</strong><br>
            Initializing demo...<br>
        </div>
    </div>

    <script>
        // Configuration
        const API_URL = 'http://localhost:5000';
        
        // DOM elements
        const videoElement = document.getElementById('videoElement');
        const testBackendBtn = document.getElementById('testBackendBtn');
        const startCameraBtn = document.getElementById('startCameraBtn');
        const recordBtn = document.getElementById('recordBtn');
        const stopCameraBtn = document.getElementById('stopCameraBtn');
        const backendStatus = document.getElementById('backendStatus');
        const cameraStatus = document.getElementById('cameraStatus');
        const results = document.getElementById('results');
        const predictions = document.getElementById('predictions');
        const logOutput = document.getElementById('logOutput');
        
        // State
        let stream = null;
        let mediaRecorder = null;
        let recordedChunks = [];
        
        // Utility functions
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logOutput.innerHTML += `[${timestamp}] ${message}<br>`;
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(element, text, className) {
            element.textContent = text;
            element.className = `status-${className}`;
        }
        
        // Backend functions
        async function testBackend() {
            log('🧪 Testing backend connection...');
            
            try {
                const response = await fetch(`${API_URL}/health`);
                const data = await response.json();
                
                if (data.status === 'healthy') {
                    log('✅ Backend test successful!');
                    log(`📊 Server: ${data.server_info}`);
                    log(`🤖 Model: ${data.model_parameters?.toLocaleString()} parameters`);
                    updateStatus(backendStatus, '✅ Connected', 'good');
                    
                    // Enable camera button if camera is available
                    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                        startCameraBtn.disabled = false;
                        log('📹 Camera button enabled');
                    }
                    
                    alert('✅ Backend connection successful!\n\nModel: ' + data.server_info);
                } else {
                    log('❌ Backend test failed');
                    updateStatus(backendStatus, '❌ Error', 'error');
                    alert('❌ Backend test failed');
                }
            } catch (error) {
                log(`❌ Backend connection failed: ${error.message}`);
                updateStatus(backendStatus, '❌ Offline', 'error');
                alert('❌ Backend connection failed: ' + error.message);
            }
        }
        
        // Camera functions
        async function startCamera() {
            log('📹 Starting camera...');
            
            try {
                stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1280 },
                        height: { ideal: 720 },
                        facingMode: 'user'
                    },
                    audio: false
                });
                
                videoElement.srcObject = stream;
                updateStatus(cameraStatus, '✅ Active', 'good');
                
                startCameraBtn.disabled = true;
                recordBtn.disabled = false;
                stopCameraBtn.disabled = false;
                
                log('✅ Camera started successfully');
                
            } catch (error) {
                log(`❌ Camera failed: ${error.message}`);
                updateStatus(cameraStatus, '❌ Failed', 'error');
                alert('❌ Camera access failed: ' + error.message);
            }
        }
        
        function stopCamera() {
            log('🛑 Stopping camera...');
            
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
            }
            
            videoElement.srcObject = null;
            updateStatus(cameraStatus, '⏹️ Stopped', 'warning');
            
            startCameraBtn.disabled = false;
            recordBtn.disabled = true;
            stopCameraBtn.disabled = true;
            
            log('✅ Camera stopped');
        }
        
        // Recording functions
        async function recordVideo() {
            log('🎬 Starting 3-second recording...');
            recordBtn.disabled = true;
            recordBtn.textContent = 'Recording...';
            
            recordedChunks = [];
            
            mediaRecorder = new MediaRecorder(stream, {
                mimeType: 'video/webm;codecs=vp9'
            });
            
            mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    recordedChunks.push(event.data);
                }
            };
            
            mediaRecorder.onstop = async () => {
                const blob = new Blob(recordedChunks, { type: 'video/webm' });
                log(`📹 Recording complete: ${(blob.size / 1024).toFixed(1)} KB`);
                await uploadVideo(blob);
                
                recordBtn.disabled = false;
                recordBtn.textContent = '🎬 Record (3s)';
            };
            
            mediaRecorder.start();
            
            // Stop after 3 seconds
            setTimeout(() => {
                if (mediaRecorder && mediaRecorder.state === 'recording') {
                    mediaRecorder.stop();
                }
            }, 3000);
        }
        
        async function uploadVideo(blob) {
            log('📤 Uploading video for analysis...');
            
            const formData = new FormData();
            formData.append('video', blob, 'recording.webm');
            
            try {
                const response = await fetch(`${API_URL}/predict`, {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    log('✅ Prediction successful!');
                    displayResults(data.predictions);
                } else {
                    log(`❌ Prediction failed: ${data.error}`);
                    alert('❌ Prediction failed: ' + data.error);
                }
            } catch (error) {
                log(`❌ Upload failed: ${error.message}`);
                alert('❌ Upload failed: ' + error.message);
            }
        }
        
        function displayResults(preds) {
            predictions.innerHTML = '';
            
            preds.forEach((pred, index) => {
                const div = document.createElement('div');
                div.style.cssText = 'margin: 10px 0; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 5px;';
                
                const confidence = pred.confidence > 1 ? pred.confidence / 100 : pred.confidence;
                const percentage = (confidence * 100).toFixed(1);
                
                div.innerHTML = `
                    <strong>${index + 1}. ${pred.class}</strong>
                    <span style="float: right; color: #4CAF50;">${percentage}%</span>
                `;
                
                predictions.appendChild(div);
            });
            
            results.style.display = 'block';
        }
        
        // Event listeners
        testBackendBtn.addEventListener('click', () => {
            log('🧪 Test Backend button clicked');
            testBackend();
        });
        
        startCameraBtn.addEventListener('click', () => {
            log('📹 Start Camera button clicked');
            startCamera();
        });
        
        recordBtn.addEventListener('click', () => {
            log('🎬 Record button clicked');
            recordVideo();
        });
        
        stopCameraBtn.addEventListener('click', () => {
            log('🛑 Stop Camera button clicked');
            stopCamera();
        });
        
        // Initialize
        window.addEventListener('load', () => {
            log('🎯 Working demo loaded');
            log('🔗 API URL: ' + API_URL);
            log('✅ Event listeners set up');
            
            // Auto-test backend
            setTimeout(testBackend, 1000);
        });
    </script>
</body>
</html>
