#!/usr/bin/env python3
"""
SMART BATCH PROCESS 30 RANDOM VIDEOS - Adaptive Cropping
========================================================

Intelligently processes videos by detecting their format:
- Full-size videos (400×200): Apply direct lip cropping
- Already-cropped videos (132×100): Skip cropping, just resize/process
- Other formats: Handle appropriately
"""

import cv2
import numpy as np
from pathlib import Path
import logging
import random
import glob

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def detect_face_in_crop_region(frame, crop_x1, crop_x2, crop_y1, crop_y2):
    """
    Verify that a face is present in the proposed crop region using OpenCV.
    Returns True if face detected, False otherwise.
    """
    try:
        # Load OpenCV face cascade
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

        # Convert to grayscale for face detection
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # Detect faces
        faces = face_cascade.detectMultiScale(gray, 1.1, 4)

        if len(faces) > 0:
            # Check if any face overlaps with crop region
            for (x, y, w, h) in faces:
                face_center_x = x + w // 2
                face_center_y = y + h // 2

                # Check if face center or bottom part (where lips would be) is in crop region
                face_bottom_y = y + int(h * 0.8)  # Bottom 20% of face (lip area)

                if (crop_x1 <= face_center_x <= crop_x2 and
                    crop_y1 <= face_bottom_y <= crop_y2):
                    logger.info(f"✅ Face detected with lip area in crop region")
                    return True

            logger.warning("⚠️ Face detected but lip area not in crop region")
            return False

        logger.warning("⚠️ No face detected in frame")
        return False

    except Exception as e:
        logger.warning(f"⚠️ Face detection failed: {e}")
        return False

def smart_process_video(input_path: Path, output_path: Path):
    """
    Intelligently process video based on its dimensions.
    """
    
    logger.info(f"🎬 Smart processing: {input_path.name}")
    
    # Load video
    cap = cv2.VideoCapture(str(input_path))
    if not cap.isOpened():
        logger.error(f"Cannot open video: {input_path}")
        return False
    
    # Read all frames
    frames = []
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        frames.append(frame)
    cap.release()
    
    if not frames:
        logger.error("No frames found")
        return False
    
    h, w = frames[0].shape[:2]
    logger.info(f"📹 Loaded {len(frames)} frames, {w}×{h} pixels")
    
    # SMART PROCESSING BASED ON VIDEO DIMENSIONS
    processed_frames = []
    
    if w == 400 and h == 200:
        # FULL-SIZE VIDEO: Apply SPEAKER-SPECIFIC lip cropping coordinates
        logger.info("🎯 Full-size video detected - applying SPEAKER-SPECIFIC lip cropping")

        # Determine speaker-specific coordinates based on video path
        if "speaker 3 thomas" in str(input_path):
            # Speaker 3: Lips CENTERED in frame (400×200)
            crop_x1 = int(w * 0.25)  # 25% from left (100 pixels)
            crop_x2 = int(w * 0.75)  # 75% from left (300 pixels)
            crop_y1 = int(h * 0.25)  # 25% from top (50 pixels) - CENTER
            crop_y2 = int(h * 0.75)  # 75% from top (150 pixels) - CENTER
            logger.info("👤 Speaker 3 thomas: Using CENTERED lip coordinates for 400×200")
        elif "speaker 9 volunteer lady green shirt" in str(input_path):
            # Speaker 9: Lips HIGHER in frame - move crop UP
            crop_x1 = int(w * 0.25)  # 25% from left (100 pixels)
            crop_x2 = int(w * 0.75)  # 75% from left (300 pixels)
            crop_y1 = int(h * 0.00)  # 0% from top (0 pixels) - HIGHER
            crop_y2 = int(h * 0.40)  # 40% from top (80 pixels) - HIGHER
            logger.info("👤 Speaker 9: Using HIGHER lip coordinates")
        else:
            # Default coordinates (other speakers)
            crop_x1 = int(w * 0.25)  # 25% from left (100 pixels)
            crop_x2 = int(w * 0.75)  # 75% from left (300 pixels)
            crop_y1 = int(h * 0.05)  # 5% from top (10 pixels)
            crop_y2 = int(h * 0.45)  # 45% from top (90 pixels)
            logger.info("👤 Default: Using standard lip coordinates")

        logger.info(f"🎯 SPEAKER-SPECIFIC CROP REGION: x={crop_x1}-{crop_x2}, y={crop_y1}-{crop_y2}")
        logger.info(f"📏 Crop size: {crop_x2-crop_x1}×{crop_y2-crop_y1} pixels")

        for frame in frames:
            # SPEAKER-SPECIFIC CROP
            cropped = frame[crop_y1:crop_y2, crop_x1:crop_x2]

            # Convert to grayscale
            gray = cv2.cvtColor(cropped, cv2.COLOR_BGR2GRAY)

            # Resize to 96×64
            resized = cv2.resize(gray, (96, 64), interpolation=cv2.INTER_LINEAR)

            processed_frames.append(resized)
            
    elif w == 132 and h == 100:
        # ALREADY-CROPPED VIDEO: Skip cropping, just process
        logger.info("✂️ Already-cropped video detected - skipping crop, processing directly")
        
        for frame in frames:
            # Convert to grayscale (no cropping needed)
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # Resize to 96×64
            resized = cv2.resize(gray, (96, 64), interpolation=cv2.INTER_LINEAR)
            
            processed_frames.append(resized)
            
    else:
        # OTHER FORMAT: Apply speaker-specific proportional cropping
        logger.info(f"🔧 Custom format ({w}×{h}) detected - applying speaker-specific proportional cropping")

        # Apply speaker-specific proportional cropping
        if "speaker 2 phan" in str(input_path):
            # Speaker 2: MOVE UP to find lips (1280×720) - previous crop was too low
            crop_x1 = int(w * 0.25)  # 25% from left
            crop_x2 = int(w * 0.75)  # 75% from left
            crop_y1 = int(h * 0.40)  # 40% from top - MOVED UP to find lips
            crop_y2 = int(h * 0.75)  # 75% from top - MOVED UP to find lips
            logger.info("👤 Speaker 2 phan: MOVED UP to find lips (40%-75% height) for 1280×720")
        else:
            # Default proportional cropping
            crop_x1 = int(w * 0.25)  # 25% from left
            crop_x2 = int(w * 0.75)  # 75% from left
            crop_y1 = int(h * 0.05)  # 5% from top
            crop_y2 = int(h * 0.45)  # 45% from top
            logger.info("👤 Default: Using standard proportional coordinates")

        for frame in frames:
            # Speaker-specific proportional crop
            cropped = frame[crop_y1:crop_y2, crop_x1:crop_x2]
            
            # Convert to grayscale
            gray = cv2.cvtColor(cropped, cv2.COLOR_BGR2GRAY)
            
            # Resize to 96×64
            resized = cv2.resize(gray, (96, 64), interpolation=cv2.INTER_LINEAR)
            
            processed_frames.append(resized)
    
    # Sample to exactly 32 frames (uniform sampling)
    if len(processed_frames) >= 32:
        indices = np.linspace(0, len(processed_frames) - 1, 32, dtype=int)
        sampled_frames = [processed_frames[i] for i in indices]
    else:
        # Pad with repeated frames if too few
        sampled_frames = processed_frames[:]
        while len(sampled_frames) < 32:
            sampled_frames.extend(processed_frames[:32 - len(sampled_frames)])
        sampled_frames = sampled_frames[:32]
    
    # Create output directory
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Save as MP4 at 15 FPS - FIXED GRAYSCALE WRITING
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(output_path), fourcc, 15.0, (96, 64), isColor=False)

    if not out.isOpened():
        logger.error("❌ Failed to open video writer")
        return False

    for frame in sampled_frames:
        # Ensure frame is truly grayscale (2D array)
        if len(frame.shape) == 3:
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # Double-check frame is the right size and type
        if frame.shape != (64, 96):
            frame = cv2.resize(frame, (96, 64))

        # Write the frame
        out.write(frame)

    out.release()
    
    logger.info(f"💾 Saved smartly processed video: {output_path}")
    return True

def main():
    """Process ALL videos from partial speaker sets to sagemaker structure."""

    # Input and output directories - check both partial and full speaker sets
    partial_input_dir = Path("/Users/<USER>/Desktop/LRP classifier 11.9.25/speaker_sets/partial_speaker_sets_top7")
    full_input_dir = Path("/Users/<USER>/Desktop/LRP classifier 11.9.25/speaker_sets/full_speaker_sets_top7_8pm")
    output_dir = Path("/Users/<USER>/Desktop/LRP classifier 11.9.25/speaker_sets/sagemaker_ready_classifier_speaker_sets/partial_sets")

    # Create output directory
    output_dir.mkdir(parents=True, exist_ok=True)

    print("🧠 SMART PROCESS - PROCESS ALL SPEAKER 3 THOMAS CLASSES!")
    print("="*70)
    print("Strategy:")
    print("• Speaker 2 phan (1280×720): CORRECTED - moved up to find lips")
    print("• Speaker 3 thomas: Process ALL 6 classes (doctor, I need to move, my back hurts, my mouth is dry, phone, pillow)")
    print("• Speaker 9 volunteer lady: CORRECTED - higher crop")
    print("• Process ALL remaining .mov files for speaker 3 thomas!")
    print("• Always output: 32 frames, 96×64 pixels, grayscale, 15 FPS")
    print("• Organize into speaker/class folder structure")
    print("="*70)

    # Find all MP4 videos recursively for specific speakers/classes from both directories
    all_videos = []

    # Search in partial speaker sets (include both MP4 and MOV files)
    for video_path in list(partial_input_dir.rglob("*.mp4")) + list(partial_input_dir.rglob("*.mov")):
        path_str = str(video_path)
        # Include: speaker 2 phan, speaker 3 thomas (ALL classes), speaker 9 my_back_hurts & phone
        if ("speaker 2 phan" in path_str or
            "speaker 3 thomas" in path_str or
            ("speaker 9 volunteer lady green shirt" in path_str and
             ("my_back_hurts" in path_str or "phone" in path_str))):
            all_videos.append(video_path)

    # Search in full speaker sets (include both MP4 and MOV files)
    for video_path in list(full_input_dir.rglob("*.mp4")) + list(full_input_dir.rglob("*.mov")):
        path_str = str(video_path)
        # Include: speaker 2 phan, speaker 3 thomas (ALL classes), speaker 9 my_back_hurts & phone
        if ("speaker 2 phan" in path_str or
            "speaker 3 thomas" in path_str or
            ("speaker 9 volunteer lady green shirt" in path_str and
             ("my_back_hurts" in path_str or "phone" in path_str))):
            all_videos.append(video_path)

    if not all_videos:
        print(f"❌ Error: No videos found for specified speakers/classes in both directories")
        return

    print(f"📁 Found {len(all_videos)} total videos (speaker 2, 3, and speaker 9 specific classes)")

    # Process ALL videos (no random selection)
    selected_videos = all_videos

    print(f"🎯 Processing ALL {len(selected_videos)} videos")

    success_count = 0

    for i, input_video in enumerate(selected_videos, 1):
        print(f"\n🎬 Processing video {i}/{len(selected_videos)}: {input_video.name}")

        # Get relative path for reference
        if str(input_video).startswith(str(partial_input_dir)):
            relative_path = input_video.relative_to(partial_input_dir)
            print(f"📂 From partial: {relative_path}")
        else:
            relative_path = input_video.relative_to(full_input_dir)
            print(f"📂 From full: {relative_path}")

        # Extract speaker and class from path structure
        speaker_folder = relative_path.parts[0]  # e.g., "speaker 7 cam 6 done"
        class_folder = relative_path.parts[1]    # e.g., "doctor"

        # Create output directory structure
        output_speaker_dir = output_dir / speaker_folder / class_folder
        output_speaker_dir.mkdir(parents=True, exist_ok=True)

        # Keep original filename
        output_video = output_speaker_dir / input_video.name

        # Process the video
        success = smart_process_video(input_video, output_video)

        if success:
            print(f"✅ Video {i} completed: {input_video.name}")
            success_count += 1
        else:
            print(f"❌ Video {i} failed: {input_video.name}")

    print(f"\n🎉 PROCESSING COMPLETE!")
    print(f"📊 Successfully processed: {success_count}/{len(selected_videos)} videos")
    print(f"📁 Output location: {output_dir}")
    print("📏 All videos: 32 frames, 96×64 pixels, grayscale, 15 FPS")
    print("� Ready for cropping inspection!")

if __name__ == "__main__":
    main()
