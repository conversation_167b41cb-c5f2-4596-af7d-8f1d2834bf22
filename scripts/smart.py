#!/usr/bin/env python3
"""
SMART BATCH PROCESS 30 RANDOM VIDEOS - Adaptive Cropping
========================================================

Intelligently processes videos by detecting their format:
- Full-size videos (400×200): Apply direct lip cropping
- Already-cropped videos (132×100): Skip cropping, just resize/process
- Other formats: Handle appropriately
"""

import cv2
import numpy as np
from pathlib import Path
import logging
import random
import glob

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def smart_process_video(input_path: Path, output_path: Path):
    """
    Intelligently process video based on its dimensions.
    """
    
    logger.info(f"🎬 Smart processing: {input_path.name}")
    
    # Load video
    cap = cv2.VideoCapture(str(input_path))
    if not cap.isOpened():
        logger.error(f"Cannot open video: {input_path}")
        return False
    
    # Read all frames
    frames = []
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        frames.append(frame)
    cap.release()
    
    if not frames:
        logger.error("No frames found")
        return False
    
    h, w = frames[0].shape[:2]
    logger.info(f"📹 Loaded {len(frames)} frames, {w}×{h} pixels")
    
    # SMART PROCESSING BASED ON VIDEO DIMENSIONS
    processed_frames = []
    
    if w == 400 and h == 200:
        # FULL-SIZE VIDEO: Apply CORRECT lip cropping coordinates
        logger.info("🎯 Full-size video detected - applying CORRECT lip cropping")

        # CORRECT crop coordinates - TOP QUARTER where lips actually are!
        crop_x1 = int(w * 0.20)  # 20% from left (80 pixels)
        crop_x2 = int(w * 0.80)  # 80% from left (320 pixels)
        crop_y1 = int(h * 0.00)  # 0% from top (0 pixels) - TOP of frame
        crop_y2 = int(h * 0.40)  # 40% from top (80 pixels) - TOP QUARTER

        logger.info(f"🎯 CORRECT CROP REGION: x={crop_x1}-{crop_x2}, y={crop_y1}-{crop_y2}")
        logger.info(f"📏 Crop size: {crop_x2-crop_x1}×{crop_y2-crop_y1} pixels")

        for frame in frames:
            # IMPROVED CROP - extract lower-center region for lips
            cropped = frame[crop_y1:crop_y2, crop_x1:crop_x2]

            # Convert to grayscale
            gray = cv2.cvtColor(cropped, cv2.COLOR_BGR2GRAY)

            # Resize to 96×64
            resized = cv2.resize(gray, (96, 64), interpolation=cv2.INTER_LINEAR)

            processed_frames.append(resized)
            
    elif w == 132 and h == 100:
        # ALREADY-CROPPED VIDEO: Skip cropping, just process
        logger.info("✂️ Already-cropped video detected - skipping crop, processing directly")
        
        for frame in frames:
            # Convert to grayscale (no cropping needed)
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # Resize to 96×64
            resized = cv2.resize(gray, (96, 64), interpolation=cv2.INTER_LINEAR)
            
            processed_frames.append(resized)
            
    else:
        # OTHER FORMAT: Apply proportional cropping
        logger.info(f"🔧 Custom format ({w}×{h}) detected - applying proportional cropping")
        
        # Use same proportions as the proven method
        crop_x1 = int(w * 0.25)  # 25% from left
        crop_x2 = int(w * 0.75)  # 75% from left
        crop_y1 = int(h * 0.05)  # 5% from top
        crop_y2 = int(h * 0.45)  # 45% from top
        
        for frame in frames:
            # Proportional crop
            cropped = frame[crop_y1:crop_y2, crop_x1:crop_x2]
            
            # Convert to grayscale
            gray = cv2.cvtColor(cropped, cv2.COLOR_BGR2GRAY)
            
            # Resize to 96×64
            resized = cv2.resize(gray, (96, 64), interpolation=cv2.INTER_LINEAR)
            
            processed_frames.append(resized)
    
    # Sample to exactly 32 frames (uniform sampling)
    if len(processed_frames) >= 32:
        indices = np.linspace(0, len(processed_frames) - 1, 32, dtype=int)
        sampled_frames = [processed_frames[i] for i in indices]
    else:
        # Pad with repeated frames if too few
        sampled_frames = processed_frames[:]
        while len(sampled_frames) < 32:
            sampled_frames.extend(processed_frames[:32 - len(sampled_frames)])
        sampled_frames = sampled_frames[:32]
    
    # Create output directory
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Save as MP4 at 15 FPS - FIXED GRAYSCALE WRITING
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(output_path), fourcc, 15.0, (96, 64), isColor=False)

    if not out.isOpened():
        logger.error("❌ Failed to open video writer")
        return False

    for frame in sampled_frames:
        # Ensure frame is truly grayscale (2D array)
        if len(frame.shape) == 3:
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # Double-check frame is the right size and type
        if frame.shape != (64, 96):
            frame = cv2.resize(frame, (96, 64))

        # Write the frame
        out.write(frame)

    out.release()
    
    logger.info(f"💾 Saved smartly processed video: {output_path}")
    return True

def main():
    """Process 10 random videos from partial speaker sets for testing."""

    # Input and output directories
    input_dir = Path("/Users/<USER>/Desktop/LRP classifier 11.9.25/speaker_sets/partial_speaker_sets_top7")
    output_dir = Path("/Users/<USER>/Desktop/LRP classifier 11.9.25/test clips")

    # Create output directory
    output_dir.mkdir(parents=True, exist_ok=True)

    print("🧠 SMART PROCESS 10 RANDOM VIDEOS - TEST CROPPING")
    print("="*70)
    print("Strategy:")
    print("• Process 10 random videos for testing")
    print("• Full-size (400×200): Apply direct lip cropping")
    print("• Already-cropped (132×100): Skip cropping, just resize")
    print("• Other formats: Apply proportional cropping")
    print("• Always output: 32 frames, 96×64 pixels, grayscale, 15 FPS")
    print("="*70)

    # Find all MP4 videos recursively
    all_videos = []
    for video_path in input_dir.rglob("*.mp4"):
        all_videos.append(video_path)

    if not all_videos:
        print(f"❌ Error: No videos found in {input_dir}")
        return

    print(f"📁 Found {len(all_videos)} total videos")

    # Select 10 random videos
    import random
    random.seed(789)  # For reproducible results
    selected_videos = random.sample(all_videos, min(10, len(all_videos)))

    print(f"🎯 Processing 10 random videos for testing")

    success_count = 0

    for i, input_video in enumerate(selected_videos, 1):
        print(f"\n🎬 Processing video {i}/10: {input_video.name}")

        # Get relative path for reference
        relative_path = input_video.relative_to(input_dir)
        print(f"📂 From: {relative_path}")

        # Simple filename for test clips
        output_video = output_dir / f"test_{i:02d}_{input_video.name}"

        # Process the video
        success = smart_process_video(input_video, output_video)

        if success:
            print(f"✅ Video {i} completed: test_{i:02d}_{input_video.name}")
            success_count += 1
        else:
            print(f"❌ Video {i} failed: test_{i:02d}_{input_video.name}")

    print(f"\n🎉 TEST PROCESSING COMPLETE!")
    print(f"📊 Successfully processed: {success_count}/10 videos")
    print(f"📁 Output location: {output_dir}")
    print("📏 All videos: 32 frames, 96×64 pixels, grayscale, 15 FPS")
    print("� Ready for cropping inspection!")

if __name__ == "__main__":
    main()
