#!/usr/bin/env python3
"""
Simple Video Processor
======================

Process a single video with basic transformations:
- Convert to grayscale
- Resize to 96×64 pixels
- Sample to exactly 32 frames (uniform)
- Save as .mp4
"""

import cv2
import numpy as np
from pathlib import Path
import sys

def process_video(input_path, output_path):
    """
    Process video with basic transformations.
    
    Args:
        input_path: Path to input video
        output_path: Path to save processed video
    """
    print(f"🎬 Processing: {input_path}")
    print(f"📁 Output: {output_path}")
    
    # Load video
    cap = cv2.VideoCapture(str(input_path))
    
    if not cap.isOpened():
        print(f"❌ Error: Cannot open video {input_path}")
        return False
    
    # Read all frames
    frames = []
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        frames.append(frame)
    
    cap.release()
    
    if not frames:
        print("❌ Error: No frames found in video")
        return False
    
    print(f"📹 Original: {len(frames)} frames, {frames[0].shape[1]}×{frames[0].shape[0]} pixels")
    
    # Process frames
    processed_frames = []
    
    for frame in frames:
        # Convert to grayscale
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # Resize to 96×64
        resized = cv2.resize(gray, (96, 64), interpolation=cv2.INTER_LINEAR)
        
        processed_frames.append(resized)
    
    # Sample to exactly 32 frames (uniform sampling)
    if len(processed_frames) >= 32:
        # Uniform sampling
        indices = np.linspace(0, len(processed_frames) - 1, 32, dtype=int)
        sampled_frames = [processed_frames[i] for i in indices]
    else:
        # Pad with repeated frames if too few
        sampled_frames = processed_frames[:]
        while len(sampled_frames) < 32:
            sampled_frames.extend(processed_frames[:32 - len(sampled_frames)])
        sampled_frames = sampled_frames[:32]
    
    print(f"✅ Processed: {len(sampled_frames)} frames, 96×64 pixels, grayscale")
    
    # Create output directory
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Save as MP4
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(output_path), fourcc, 30.0, (96, 64), isColor=False)
    
    for frame in sampled_frames:
        out.write(frame)
    
    out.release()
    
    print(f"💾 Saved: {output_path}")
    return True

def main():
    """Main function."""
    # Input video path
    input_video = Path("speaker_sets/full_speaker_sets_top7/speaker_1 /my_back_hurts/my_back_hurts__useruser01__18to39__male__not_specified__20250722T014409.mp4")
    
    # Output directory and filename
    output_dir = Path("test clips")
    output_video = output_dir / "processed_my_back_hurts.mp4"
    
    if not input_video.exists():
        print(f"❌ Error: Input video not found: {input_video}")
        return
    
    print("🎯 SIMPLE VIDEO PROCESSOR")
    print("="*50)
    print("Transformations:")
    print("• Convert → grayscale")
    print("• Resize → 96×64 pixels")
    print("• Sample to exactly 32 frames (uniform)")
    print("• Save as .mp4")
    print("="*50)
    
    # Process the video
    success = process_video(input_video, output_video)
    
    if success:
        print("\n🎉 SUCCESS!")
        print(f"📄 Processed video: {output_video}")
    else:
        print("\n❌ FAILED!")

if __name__ == "__main__":
    main()
