#!/usr/bin/env python3
"""
GRID 2-Speaker (s1+s4) Expansion Pipeline
========================================

Expands GRID pretraining to include speaker s1 alongside existing s4 data,
creating a robust 2-speaker GRID subset for enhanced multi-speaker pretraining.

Key Features:
- Processes speaker s1 with identical pipeline as s4
- Maintains exact same 15 viseme-matched word classes
- Preserves existing s4 data and processing
- Creates unified 2-speaker manifest and configuration
- Executes enhanced multi-speaker GRID pretraining

Usage:
    python expand_grid_to_s1s4_pretraining.py

Author: Augment Agent
Date: 2025-09-29
"""

import os
import sys
import json
import shutil
import cv2
import numpy as np
from pathlib import Path
from typing import Dict, List, Set, Optional, Tuple
import logging
from collections import defaultdict, Counter
import subprocess
import time
import csv
import yaml

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GRIDTwoSpeakerProcessor:
    """Processes GRID speakers s1 and s4 for enhanced 2-speaker pretraining."""
    
    def __init__(self):
        self.grid_s1_path = Path("GRID_talker_sets/s1")
        self.grid_s4_path = Path("GRID_talker_sets/talker 4/s4")
        self.output_base = Path("sagemaker_training_files/data")
        self.grid_raw_dir = self.output_base / "grid_raw"
        self.grid_subset_dir = self.output_base / "grid_subset_3spk"
        
        # GRID decoding mappings (from build_grid_manifest.py)
        self.command_map = {'b': 'bin', 'l': 'lay', 'p': 'place', 's': 'set'}
        self.color_map = {'b': 'blue', 'g': 'green', 'r': 'red', 'w': 'white'}
        self.prep_map = {'a': 'at', 'b': 'by', 'i': 'in', 'w': 'with'}
        self.digit_map = {'0': 'zero', '1': 'one', '2': 'two', '3': 'three', '4': 'four',
                         '5': 'five', '6': 'six', '7': 'seven', '8': 'eight', '9': 'nine'}
        self.adverb_map = {'a': 'again', 'n': 'now', 'p': 'please', 's': 'soon'}
        
        # Required words (exactly same as s4 - maintain consistency)
        self.required_words = {
            'at', 'bin', 'blue', 'green', 'in', 'lay', 'now', 'one', 
            'place', 'please', 'red', 'set', 'soon', 'white', 'with'
        }
        
    def decode_grid_filename(self, filename: str) -> Optional[List[str]]:
        """
        Decode GRID filename to extract words.
        
        GRID filenames like 'bbae9n.mpg' encode 6-word sentences:
        - 1st char: command (b=bin, l=lay, p=place, s=set)
        - 2nd char: color (b=blue, g=green, r=red, w=white)  
        - 3rd char: preposition (a=at, b=by, i=in, w=with)
        - 4th char: letter (a-z except w)
        - 5th char: digit (0-9)
        - 6th char: adverb (a=again, n=now, p=please, s=soon)
        """
        # Remove extension and get base name
        sentence_id = Path(filename).stem
        
        if len(sentence_id) != 6:
            return None
        
        try:
            words = [
                self.command_map[sentence_id[0]],
                self.color_map[sentence_id[1]], 
                self.prep_map[sentence_id[2]],
                sentence_id[3],  # letter as-is
                self.digit_map[sentence_id[4]],
                self.adverb_map[sentence_id[5]]
            ]
            return words
        except KeyError as e:
            logger.debug(f"Could not decode {filename}: {e}")
            return None
    
    def analyze_grid_s1_data(self) -> Dict[str, List[Path]]:
        """Analyze GRID s1 data and group by required words."""
        
        if not self.grid_s1_path.exists():
            logger.error(f"GRID s1 directory not found: {self.grid_s1_path}")
            return {}
        
        logger.info(f"Analyzing GRID s1 data in: {self.grid_s1_path}")
        
        # Find all video files
        video_files = list(self.grid_s1_path.glob("*.mpg"))
        logger.info(f"Found {len(video_files)} video files")
        
        # Group by words
        word_groups = defaultdict(list)
        word_counts = Counter()
        
        for video_file in video_files:
            words = self.decode_grid_filename(video_file.name)
            if words:
                # Check each word in the sentence
                for word in words:
                    word_counts[word] += 1
                    if word in self.required_words:
                        word_groups[word].append(video_file)
        
        # Log analysis results
        logger.info(f"Word analysis complete:")
        logger.info(f"Total unique words found: {len(word_counts)}")
        logger.info(f"Required words found: {len(word_groups)}")
        
        for word in sorted(self.required_words):
            count = len(word_groups.get(word, []))
            logger.info(f"  {word}: {count} videos")
        
        return dict(word_groups)
    
    def convert_mpg_to_mp4(self, input_path: Path, output_path: Path) -> bool:
        """Convert MPG to MP4 using ffmpeg."""
        try:
            cmd = [
                'ffmpeg', '-i', str(input_path),
                '-c:v', 'libx264', '-c:a', 'aac',
                '-y', str(output_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return True
            else:
                logger.error(f"FFmpeg error converting {input_path}: {result.stderr}")
                return False
                
        except FileNotFoundError:
            logger.error("FFmpeg not found. Please install: brew install ffmpeg")
            return False
        except Exception as e:
            logger.error(f"Error converting {input_path}: {e}")
            return False
    
    def organize_grid_s1_data(self, word_groups: Dict[str, List[Path]]) -> bool:
        """Organize GRID s1 data into required structure (preserving s4)."""
        
        # Create s1 speaker directory
        speaker_s1_dir = self.grid_raw_dir / "speaker_s1"
        speaker_s1_dir.mkdir(parents=True, exist_ok=True)
        
        total_organized = 0
        
        for word, video_files in word_groups.items():
            if not video_files:
                continue
                
            word_dir = speaker_s1_dir / word
            word_dir.mkdir(exist_ok=True)
            
            logger.info(f"Organizing {len(video_files)} videos for word '{word}'")
            
            for i, video_file in enumerate(video_files):
                # Convert MPG to MP4
                output_filename = f"s1_{word}_{i+1:03d}.mp4"
                output_path = word_dir / output_filename
                
                if self.convert_mpg_to_mp4(video_file, output_path):
                    total_organized += 1
                    if i == 0:  # Log first file
                        logger.info(f"  Converted: {video_file.name} -> {output_filename}")
                else:
                    logger.warning(f"Failed to convert: {video_file.name}")
        
        logger.info(f"Total s1 videos organized: {total_organized}")
        return total_organized > 0
    
    def preprocess_s1_videos(self) -> bool:
        """Preprocess s1 videos using existing pipeline (preserving s4)."""
        
        logger.info("Starting s1 video preprocessing...")
        
        # Use existing preprocessing tool
        preprocessing_script = Path("tools/stabilize_mouth_roi.py")
        
        if not preprocessing_script.exists():
            logger.error(f"Preprocessing script not found: {preprocessing_script}")
            return False
        
        try:
            # Run preprocessing on s1 data only
            cmd = [
                sys.executable, str(preprocessing_script),
                "--input-dir", str(self.grid_raw_dir / "speaker_s1"),
                "--output-dir", str(self.grid_subset_dir / "speaker_s1"),
                "--max-videos-per-class", "50"  # Limit for consistency with s4
            ]
            
            logger.info(f"Running s1 preprocessing: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")
            
            if result.returncode == 0:
                logger.info("s1 preprocessing completed successfully")
                logger.info(f"s1 preprocessing output:\n{result.stdout}")
                return True
            else:
                logger.error(f"s1 preprocessing failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error running s1 preprocessing: {e}")
            return False
    
    def create_unified_manifest(self) -> bool:
        """Create unified manifest for both s1 and s4 speakers."""
        
        manifest_path = self.output_base / "grid_manifest.csv"
        
        try:
            manifest_data = []
            
            # Scan preprocessed data for both speakers
            for speaker_dir in self.grid_subset_dir.iterdir():
                if not speaker_dir.is_dir():
                    continue
                    
                speaker_id = speaker_dir.name
                
                for word_dir in speaker_dir.iterdir():
                    if not word_dir.is_dir():
                        continue
                        
                    word = word_dir.name
                    
                    for video_file in word_dir.glob("*.mp4"):
                        relative_path = video_file.relative_to(self.grid_subset_dir)
                        manifest_data.append({
                            'video_path': str(relative_path),
                            'speaker_id': speaker_id,
                            'word': word,
                            'class_label': word,  # For GRID pretraining
                            'split': 'train'
                        })
            
            # Write unified manifest
            with open(manifest_path, 'w', newline='') as f:
                if manifest_data:
                    writer = csv.DictWriter(f, fieldnames=manifest_data[0].keys())
                    writer.writeheader()
                    writer.writerows(manifest_data)
            
            # Log statistics
            speaker_counts = Counter(item['speaker_id'] for item in manifest_data)
            word_counts = Counter(item['word'] for item in manifest_data)
            
            logger.info(f"Created unified manifest with {len(manifest_data)} entries: {manifest_path}")
            logger.info(f"Speaker distribution: {dict(speaker_counts)}")
            logger.info(f"Word distribution: {dict(word_counts)}")
            
            return len(manifest_data) > 0
            
        except Exception as e:
            logger.error(f"Error creating unified manifest: {e}")
            return False
    
    def create_2speaker_config(self) -> bool:
        """Create 2-speaker GRID pretraining configuration."""
        
        base_config_path = Path("sagemaker_training_files/configs/grid_pretrain_s4.yaml")
        
        if not base_config_path.exists():
            logger.error(f"Base GRID config not found: {base_config_path}")
            return False
        
        try:
            with open(base_config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            # Update for 2-speaker training
            config['data_root'] = 'data/grid_subset_3spk'
            config['labels'] = sorted(list(self.required_words))  # Maintain exact same classes
            config['num_classes'] = len(self.required_words)  # Should be 15
            
            # Adjust parameters for 2-speaker training
            config['batch_size'] = 16      # Increased from 8 (more data available)
            config['epochs'] = 40          # Increased from 30 (more complex learning)
            config['val_split'] = 0.25     # Adjusted from 0.3 (maintain good validation size)
            config['early_stop_patience'] = 15  # Increased from 10 (more patience for complex learning)
            
            # Update output directories
            config['output_dir'] = 'checkpoints/grid_pretrain_s1s4'
            config['tensorboard_dir'] = 'runs/grid_pretrain_s1s4'
            
            # Save 2-speaker config
            two_speaker_config = Path("sagemaker_training_files/configs/grid_pretrain_s1s4.yaml")
            with open(two_speaker_config, 'w') as f:
                yaml.dump(config, f, default_flow_style=False)
            
            logger.info(f"Created 2-speaker config: {two_speaker_config}")
            logger.info(f"Classes ({len(self.required_words)}): {sorted(list(self.required_words))}")
            logger.info(f"Batch size: {config['batch_size']}, Epochs: {config['epochs']}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error creating 2-speaker config: {e}")
            return False
    
    def run_2speaker_pretraining(self) -> bool:
        """Execute 2-speaker GRID pretraining."""
        
        logger.info("Starting 2-speaker GRID pretraining...")
        
        # Change to sagemaker directory
        sagemaker_dir = Path("sagemaker_training_files")
        
        if not sagemaker_dir.exists():
            logger.error(f"SageMaker directory not found: {sagemaker_dir}")
            return False
        
        try:
            # Run 2-speaker GRID pretraining
            training_script = sagemaker_dir / "training" / "train_grid_pretrain.py"
            config_file = sagemaker_dir / "configs" / "grid_pretrain_s1s4.yaml"
            
            if not training_script.exists():
                logger.error(f"Training script not found: {training_script}")
                return False
            
            cmd = [
                sys.executable, str(training_script),
                "--config", str(config_file)
            ]
            
            logger.info(f"Running 2-speaker GRID pretraining: {' '.join(cmd)}")
            
            # Run training with real-time output
            process = subprocess.Popen(
                cmd, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                cwd=str(sagemaker_dir)
            )
            
            # Stream output
            for line in process.stdout:
                print(line.rstrip())
            
            process.wait()
            
            if process.returncode == 0:
                logger.info("2-speaker GRID pretraining completed successfully!")
                return True
            else:
                logger.error(f"2-speaker GRID pretraining failed with return code: {process.returncode}")
                return False
                
        except Exception as e:
            logger.error(f"Error running 2-speaker GRID pretraining: {e}")
            return False

def main():
    """Main execution function."""
    
    print("🎬 GRID 2-Speaker (s1+s4) Expansion Pipeline")
    print("=" * 60)
    
    processor = GRIDTwoSpeakerProcessor()
    
    # Step 1: Analyze GRID s1 data
    print("\n📊 Step 1: Analyzing GRID s1 data...")
    word_groups = processor.analyze_grid_s1_data()
    
    if not word_groups:
        print("❌ No matching words found in GRID s1 data")
        return False
    
    # Step 2: Organize s1 data (preserving s4)
    print("\n📁 Step 2: Organizing GRID s1 data...")
    if not processor.organize_grid_s1_data(word_groups):
        print("❌ Failed to organize GRID s1 data")
        return False
    
    # Step 3: Preprocess s1 videos
    print("\n🔧 Step 3: Preprocessing s1 videos...")
    if not processor.preprocess_s1_videos():
        print("❌ Failed to preprocess s1 videos")
        return False
    
    # Step 4: Create unified manifest
    print("\n📋 Step 4: Creating unified s1+s4 manifest...")
    if not processor.create_unified_manifest():
        print("❌ Failed to create unified manifest")
        return False
    
    # Step 5: Create 2-speaker config
    print("\n⚙️  Step 5: Creating 2-speaker training config...")
    if not processor.create_2speaker_config():
        print("❌ Failed to create 2-speaker config")
        return False
    
    # Step 6: Run 2-speaker pretraining
    print("\n🚂 Step 6: Running 2-speaker GRID pretraining...")
    if not processor.run_2speaker_pretraining():
        print("❌ 2-speaker GRID pretraining failed")
        return False
    
    print("\n🎉 GRID 2-Speaker (s1+s4) Expansion Complete!")
    print("✅ Enhanced multi-speaker encoder ready for ICU fine-tuning")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
