#!/usr/bin/env python3
"""
Create simple comparison browser showing original vs zoomed out results
"""

import cv2
import base64
from pathlib import Path

def create_simple_comparison():
    # Find processed videos from both versions
    original_dir = Path('test_corrected_grayscale_output')
    zoomed_dir = Path('test_zoomed_out_15pct_output')
    
    original_videos = list(original_dir.glob('*.mp4'))
    zoomed_videos = list(zoomed_dir.glob('*.mp4'))
    
    print(f'Creating simple comparison browser...')
    print(f'Original cropping: {len(original_videos)} videos')
    print(f'Zoomed out 15%: {len(zoomed_videos)} videos')
    
    html_content = '''<!DOCTYPE html>
<html>
<head>
    <title>🔍 ICU Pipeline: Cropping Comparison</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .header { background: #4299e1; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .comparison-section { background: white; margin-bottom: 30px; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .section-title { font-size: 20px; font-weight: bold; margin-bottom: 15px; text-align: center; }
        .original { border-left: 5px solid #f6ad55; }
        .original .section-title { color: #c05621; }
        .zoomed { border-left: 5px solid #68d391; }
        .zoomed .section-title { color: #2f855a; }
        .video-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .video-item { border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; }
        .video-title { font-size: 14px; font-weight: bold; margin-bottom: 10px; }
        .frames-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 5px; }
        .frame-item img { width: 100%; border: 1px solid #e2e8f0; border-radius: 4px; }
        .specs { font-size: 12px; color: #718096; margin-top: 10px; text-align: center; }
        .comparison-info { background: #e6fffa; border: 2px solid #38b2ac; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 ICU Pipeline Cropping Comparison</h1>
        <p><strong>Comparison:</strong> Original vs 15% Zoomed Out Cropping</p>
        <p>Visual inspection to determine optimal crop parameters</p>
    </div>
    
    <div class="comparison-info">
        <h3>📊 Cropping Parameters</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
                <h4 style="color: #c05621;">🔸 Original Cropping</h4>
                <p><strong>Height:</strong> 70% of frame</p>
                <p><strong>Width:</strong> 50% of frame</p>
                <p><strong>Focus:</strong> Tighter crop on mouth region</p>
            </div>
            <div>
                <h4 style="color: #2f855a;">🔹 15% Zoomed Out</h4>
                <p><strong>Height:</strong> 85% of frame</p>
                <p><strong>Width:</strong> 65% of frame</p>
                <p><strong>Focus:</strong> More context around mouth</p>
            </div>
        </div>
    </div>
'''
    
    # Original cropping section
    if original_videos:
        html_content += '''
    <div class="comparison-section original">
        <div class="section-title">🔸 Original Cropping (70% × 50%)</div>
        <div class="video-grid">
'''
        
        for i, video_path in enumerate(original_videos, 1):
            print(f'Processing original video {i}: {video_path.name}')
            
            cap = cv2.VideoCapture(str(video_path))
            if cap.isOpened():
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                
                html_content += f'''
            <div class="video-item">
                <div class="video-title">{i}. {video_path.name.replace('icu_cropped_', '')}</div>
                <div class="frames-grid">
'''
                
                # Extract 4 frames
                for j in range(4):
                    frame_idx = int(j * frame_count / 4) if frame_count > 4 else j
                    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                    ret, frame = cap.read()
                    
                    if ret:
                        _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
                        frame_b64 = base64.b64encode(buffer).decode('utf-8')
                        
                        html_content += f'''
                    <div class="frame-item">
                        <img src="data:image/jpeg;base64,{frame_b64}" alt="Frame {j+1}">
                    </div>
'''
                
                html_content += '''
                </div>
                <div class="specs">Tighter crop, focused view</div>
            </div>
'''
                cap.release()
        
        html_content += '''
        </div>
    </div>
'''
    
    # Zoomed out section
    if zoomed_videos:
        html_content += '''
    <div class="comparison-section zoomed">
        <div class="section-title">🔹 15% Zoomed Out (85% × 65%)</div>
        <div class="video-grid">
'''
        
        for i, video_path in enumerate(zoomed_videos, 1):
            print(f'Processing zoomed video {i}: {video_path.name}')
            
            cap = cv2.VideoCapture(str(video_path))
            if cap.isOpened():
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                
                html_content += f'''
            <div class="video-item">
                <div class="video-title">{i}. {video_path.name.replace('icu_cropped_', '')}</div>
                <div class="frames-grid">
'''
                
                # Extract 4 frames
                for j in range(4):
                    frame_idx = int(j * frame_count / 4) if frame_count > 4 else j
                    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                    ret, frame = cap.read()
                    
                    if ret:
                        _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
                        frame_b64 = base64.b64encode(buffer).decode('utf-8')
                        
                        html_content += f'''
                    <div class="frame-item">
                        <img src="data:image/jpeg;base64,{frame_b64}" alt="Frame {j+1}">
                    </div>
'''
                
                html_content += '''
                </div>
                <div class="specs">More context, wider view</div>
            </div>
'''
                cap.release()
        
        html_content += '''
        </div>
    </div>
'''
    
    html_content += '''
    <div class="comparison-info">
        <h3>🎯 Visual Assessment Guide</h3>
        <p><strong>Compare the two cropping approaches:</strong></p>
        <ul>
            <li><strong>Mouth Visibility:</strong> Both should show clear lip movements</li>
            <li><strong>Context Amount:</strong> Zoomed out provides more surrounding facial area</li>
            <li><strong>Focus vs Context:</strong> Original is tighter, zoomed out has more context</li>
            <li><strong>Preference:</strong> Which provides better balance for lip-reading?</li>
        </ul>
        <p><strong>Decision:</strong> Choose the cropping level that works best for your use case.</p>
    </div>
</body>
</html>
'''
    
    # Save HTML file
    html_file = Path('icu_cropping_comparison.html')
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f'✅ Comparison browser created: {html_file.absolute()}')
    return html_file

if __name__ == '__main__':
    create_simple_comparison()
