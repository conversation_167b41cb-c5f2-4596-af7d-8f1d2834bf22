#!/usr/bin/env python3
"""
Speaker Set Processing with Proven GRID Pipeline
===============================================

Uses the EXACT preprocessing script that was successfully working before checkpoint 8.
Processes speaker set videos using the proven tools/grid_preprocessing_pipeline_corrected.py approach.

Features:
- Uses proven GRID preprocessing pipeline (100% success rate)
- Processes 5 videos from each class (doctor, glasses, i_need_to_move, my_back_hurts, my_mouth_is_dry, phone, pillow)
- Skips already processed videos to avoid duplicates
- Creates visual browser interface to inspect results
- Maintains exact 96×64 grayscale, 32 frames output format

Author: Augment Agent
Date: 2025-09-29
Status: REVERT TO PROVEN PIPELINE
"""

import os
import sys
import cv2
import numpy as np
import random
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import logging
import json
import time
from datetime import datetime
import base64
import pandas as pd

# Import the proven preprocessing pipeline
sys.path.append('tools')
from grid_preprocessing_pipeline_corrected import GRIDPreprocessingPipelineCorrected

class SpeakerSetProcessor:
    """Process speaker set videos using proven GRID pipeline."""
    
    def __init__(self, output_dir: str = "speaker_sets_proven_processing"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Create subdirectories
        self.processed_dir = self.output_dir / "processed"
        self.processed_dir.mkdir(exist_ok=True)
        
        # Initialize proven GRID pipeline
        self.grid_pipeline = GRIDPreprocessingPipelineCorrected()
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Target classes
        self.target_classes = [
            'doctor', 'glasses', 'i_need_to_move', 
            'my_back_hurts', 'my_mouth_is_dry', 'phone', 'pillow'
        ]
        
        # Processing results
        self.results = []
    
    def select_videos_from_speaker_sets(self, base_dir: str, videos_per_class: int = 5) -> Dict[str, List[Path]]:
        """Select random videos from each class across speaker sets 1-6."""
        base_path = Path(base_dir)
        selected_videos = {class_name: [] for class_name in self.target_classes}
        
        self.logger.info(f"🎯 Selecting {videos_per_class} videos per class from speaker sets 1-6")
        
        # Collect all videos from each class across speaker sets 1-6
        for class_name in self.target_classes:
            all_videos = []
            
            # Search in speaker sets 1-6
            for speaker_num in range(1, 7):  # speaker_1 to speaker_6
                speaker_dir = base_path / f"speaker_{speaker_num}" / class_name
                if speaker_dir.exists():
                    videos = list(speaker_dir.glob("*.mp4"))
                    all_videos.extend(videos)
            
            # Randomly select videos_per_class videos
            if len(all_videos) >= videos_per_class:
                selected = random.sample(all_videos, videos_per_class)
                selected_videos[class_name] = selected
                self.logger.info(f"   📁 {class_name}: {len(selected)} videos selected from {len(all_videos)} available")
            else:
                selected_videos[class_name] = all_videos
                self.logger.info(f"   📁 {class_name}: {len(all_videos)} videos selected (all available)")
        
        return selected_videos
    
    def is_already_processed(self, input_path: Path) -> bool:
        """Check if video has already been processed."""
        output_name = f"proven_{input_path.stem}.mp4"
        output_path = self.processed_dir / output_name
        return output_path.exists()
    
    def process_video_with_proven_pipeline(self, input_path: Path) -> Dict[str, Any]:
        """Process single video using proven GRID pipeline."""
        start_time = time.time()
        
        try:
            # Create output path
            output_name = f"proven_{input_path.stem}.mp4"
            output_path = self.processed_dir / output_name
            
            # Use proven GRID pipeline to process video
            result = self.grid_pipeline.process_video(
                Path(input_path),
                Path(output_path)
            )
            
            processing_time = time.time() - start_time
            
            # Extract key information from result
            success = result.get('success', False)
            error_msg = result.get('error', '')
            
            # Get video info if successful
            original_frames = 0
            processed_frames = 0
            if success and output_path.exists():
                # Count frames in processed video
                cap = cv2.VideoCapture(str(output_path))
                processed_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                cap.release()
                
                # Count frames in original video
                cap = cv2.VideoCapture(str(input_path))
                original_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                cap.release()
            
            return {
                'input_path': str(input_path),
                'output_path': str(output_path),
                'success': success,
                'original_frames': original_frames,
                'processed_frames': processed_frames,
                'processing_time': processing_time,
                'error_message': error_msg,
                'class_name': input_path.parent.name,
                'speaker_set': input_path.parts[-3] if len(input_path.parts) >= 3 else 'unknown',
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            processing_time = time.time() - start_time
            return {
                'input_path': str(input_path),
                'output_path': '',
                'success': False,
                'original_frames': 0,
                'processed_frames': 0,
                'processing_time': processing_time,
                'error_message': str(e),
                'class_name': input_path.parent.name,
                'speaker_set': input_path.parts[-3] if len(input_path.parts) >= 3 else 'unknown',
                'timestamp': datetime.now().isoformat()
            }
    
    def process_all_selected_videos(self, selected_videos: Dict[str, List[Path]]) -> List[Dict[str, Any]]:
        """Process all selected videos using proven pipeline."""
        all_results = []
        total_videos = sum(len(videos) for videos in selected_videos.values())
        processed_count = 0
        skipped_count = 0
        
        self.logger.info(f"🔧 Processing {total_videos} selected videos with proven GRID pipeline")
        
        for class_name, videos in selected_videos.items():
            self.logger.info(f"📁 Processing {class_name} class ({len(videos)} videos)")
            
            for video_path in videos:
                # Check if already processed
                if self.is_already_processed(video_path):
                    self.logger.info(f"   ⏭️  SKIPPED (already processed): {video_path.name}")
                    skipped_count += 1
                    continue
                
                self.logger.info(f"   🎬 Processing: {video_path.name}")
                
                # Process with proven pipeline
                result = self.process_video_with_proven_pipeline(video_path)
                all_results.append(result)
                
                if result['success']:
                    self.logger.info(f"   ✅ SUCCESS: {result['processing_time']:.2f}s")
                    processed_count += 1
                else:
                    self.logger.error(f"   ❌ FAILED: {result['error_message']}")
        
        self.logger.info(f"🎉 Processing complete: {processed_count} successful, {skipped_count} skipped")
        return all_results
    
    def save_manifest(self, results: List[Dict[str, Any]]) -> Path:
        """Save processing results to CSV manifest."""
        manifest_path = self.output_dir / "proven_pipeline_manifest.csv"
        
        if results:
            df = pd.DataFrame(results)
            df.to_csv(manifest_path, index=False)
            self.logger.info(f"📄 Manifest saved: {manifest_path}")
        
        return manifest_path

def main():
    """Main processing function."""
    print("🚀 Speaker Set Processing with Proven GRID Pipeline")
    print("=" * 60)
    
    # Initialize processor
    processor = SpeakerSetProcessor()
    
    # Select videos from speaker sets
    base_dir = "speaker_sets/full_speaker_sets_top7"
    selected_videos = processor.select_videos_from_speaker_sets(base_dir, videos_per_class=5)
    
    # Process selected videos
    results = processor.process_all_selected_videos(selected_videos)
    
    # Save manifest
    manifest_path = processor.save_manifest(results)
    
    # Calculate success rate
    if results:
        successful = sum(1 for r in results if r['success'])
        total = len(results)
        success_rate = successful / total * 100
        
        print(f"\n🎉 PROCESSING COMPLETE!")
        print(f"📊 Success Rate: {success_rate:.1f}% ({successful}/{total})")
        print(f"📄 Results saved: {manifest_path}")
        print(f"📁 Processed videos: {processor.processed_dir}")
    
    return results

if __name__ == "__main__":
    results = main()
