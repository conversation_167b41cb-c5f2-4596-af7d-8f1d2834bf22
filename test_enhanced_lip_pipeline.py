#!/usr/bin/env python3
"""
Test Enhanced Lip Detection Pipeline
====================================

Tests the enhanced lip detection pipeline with focus on actual lip detection quality.
Creates visual comparison between original intelligent pipeline and enhanced version.
"""

import sys
import random
import cv2
import base64
import logging
from pathlib import Path
from typing import List, Dict, Any

# Add src to path
sys.path.append('src')
from preprocessing.enhanced_lip_detection_pipeline import EnhancedLipDetectionPipeline

def collect_test_videos(base_dir: Path, count: int = 10) -> List[Path]:
    """Collect test videos - mix of original and already-cropped."""
    all_videos = []
    
    # Collect all MP4 videos
    for speaker_dir in base_dir.iterdir():
        if speaker_dir.is_dir() and speaker_dir.name.startswith('speaker_'):
            for class_dir in speaker_dir.iterdir():
                if class_dir.is_dir():
                    videos = list(class_dir.glob('*.mp4'))
                    all_videos.extend(videos)
    
    print(f"📁 Found {len(all_videos)} total videos")
    
    # Separate by type for balanced testing
    original_videos = [v for v in all_videos if '_topmid' not in v.name.lower()]
    cropped_videos = [v for v in all_videos if '_topmid' in v.name.lower()]
    
    print(f"   🎬 Original videos: {len(original_videos)}")
    print(f"   ✂️  Cropped videos: {len(cropped_videos)}")
    
    # Select balanced mix
    selected = []
    if original_videos:
        selected.extend(random.sample(original_videos, min(count//2, len(original_videos))))
    if cropped_videos:
        selected.extend(random.sample(cropped_videos, min(count//2, len(cropped_videos))))
    
    # Fill remaining slots
    remaining = count - len(selected)
    if remaining > 0:
        remaining_videos = [v for v in all_videos if v not in selected]
        if remaining_videos:
            selected.extend(random.sample(remaining_videos, min(remaining, len(remaining_videos))))
    
    print(f"🎲 Selected {len(selected)} videos for testing")
    return selected

def extract_frames_with_metadata(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Extract first frames with enhanced metadata."""
    frame_data = {}
    
    for result in results:
        if result['processing_status'] == 'success':
            output_path = result['output_path']
            
            try:
                cap = cv2.VideoCapture(output_path)
                if cap.isOpened():
                    ret, frame = cap.read()
                    if ret:
                        # Convert to base64 for HTML display
                        _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 90])
                        frame_b64 = base64.b64encode(buffer).decode('utf-8')
                        
                        # Get video properties
                        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                        
                        frame_data[result['source_filename']] = {
                            'frame_b64': frame_b64,
                            'width': width,
                            'height': height,
                            'frame_count': frame_count,
                            'detection_method': result.get('detection_method', 'unknown'),
                            'detection_confidence': result.get('detection_confidence', 0),
                            'detection_quality': result.get('detection_quality', 'unknown'),
                            'validation_score': result.get('validation_score', 0),
                            'video_classification': result.get('video_classification', 'unknown'),
                            'processing_time': result.get('processing_time', 0)
                        }
                    cap.release()
            except Exception as e:
                print(f"⚠️  Error extracting frame from {output_path}: {e}")
    
    return frame_data

def create_enhanced_html_browser(results: List[Dict[str, Any]], frame_data: Dict[str, Any], 
                                output_file: Path) -> None:
    """Create enhanced HTML browser with quality metrics."""
    
    # Calculate statistics
    total_videos = len(results)
    successful = sum(1 for r in results if r['processing_status'] == 'success')
    failed = sum(1 for r in results if r['processing_status'] == 'failed')
    
    # Quality distribution
    high_quality = sum(1 for r in results if r.get('detection_quality') == 'high')
    medium_quality = sum(1 for r in results if r.get('detection_quality') == 'medium')
    low_quality = sum(1 for r in results if r.get('detection_quality') == 'low')
    
    # Method distribution
    method_counts = {}
    for result in results:
        if result['processing_status'] == 'success':
            method = result.get('detection_method', 'unknown')
            method_counts[method] = method_counts.get(method, 0) + 1
    
    # Average confidence
    confidences = [r.get('detection_confidence', 0) for r in results if r['processing_status'] == 'success']
    avg_confidence = sum(confidences) / len(confidences) if confidences else 0
    
    html_content = f'''<!DOCTYPE html>
<html>
<head>
    <title>🎯 Enhanced Lip Detection Pipeline - Test Results</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
        .header {{ background: #2d3748; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
        .stats-section {{ background: #e6fffa; border: 2px solid #38b2ac; padding: 15px; border-radius: 8px; margin-bottom: 20px; }}
        .quality-section {{ background: #fff5f5; border: 2px solid #e53e3e; padding: 15px; border-radius: 8px; margin-bottom: 20px; }}
        .stats-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 15px; }}
        .stat-item {{ background: white; padding: 10px; border-radius: 6px; text-align: center; }}
        .frames-section {{ background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .frames-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 15px; margin-top: 20px; }}
        .frame-item {{ border: 2px solid #e2e8f0; border-radius: 8px; padding: 12px; background: #fafafa; }}
        .frame-item.high {{ border-color: #48bb78; background: #f0fff4; }}
        .frame-item.medium {{ border-color: #ed8936; background: #fffaf0; }}
        .frame-item.low {{ border-color: #e53e3e; background: #fff5f5; }}
        .frame-title {{ font-size: 12px; font-weight: bold; margin-bottom: 8px; color: #2d3748; }}
        .frame-image {{ width: 100%; border: 1px solid #e2e8f0; border-radius: 4px; }}
        .frame-specs {{ font-size: 11px; color: #718096; margin-top: 8px; line-height: 1.4; }}
        .quality-badge {{ display: inline-block; padding: 2px 8px; border-radius: 12px; font-size: 10px; font-weight: bold; margin: 2px; }}
        .quality-high {{ background: #c6f6d5; color: #22543d; }}
        .quality-medium {{ background: #fed7aa; color: #9c4221; }}
        .quality-low {{ background: #fed7d7; color: #742a2a; }}
        .method-tag {{ display: inline-block; background: #bee3f8; color: #2c5282; padding: 2px 6px; border-radius: 4px; font-size: 10px; margin: 2px; }}
        .confidence-bar {{ width: 100%; height: 8px; background: #e2e8f0; border-radius: 4px; margin: 4px 0; }}
        .confidence-fill {{ height: 100%; border-radius: 4px; }}
        .confidence-high {{ background: #48bb78; }}
        .confidence-medium {{ background: #ed8936; }}
        .confidence-low {{ background: #e53e3e; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 Enhanced Lip Detection Pipeline - Test Results</h1>
        <p><strong>Focus:</strong> Actual lip detection quality over format compliance</p>
        <p><strong>Test Date:</strong> {Path().cwd().name} - Enhanced Detection Methods</p>
    </div>
    
    <div class="stats-section">
        <h3>📊 Processing Statistics</h3>
        <div class="stats-grid">
            <div class="stat-item">
                <h4>Total Videos</h4>
                <p style="font-size: 24px; color: #4c51bf; margin: 5px 0;">{total_videos}</p>
            </div>
            <div class="stat-item">
                <h4>✅ Successful</h4>
                <p style="font-size: 24px; color: #38a169; margin: 5px 0;">{successful}</p>
            </div>
            <div class="stat-item">
                <h4>❌ Failed</h4>
                <p style="font-size: 24px; color: #e53e3e; margin: 5px 0;">{failed}</p>
            </div>
            <div class="stat-item">
                <h4>Avg Confidence</h4>
                <p style="font-size: 24px; color: #805ad5; margin: 5px 0;">{avg_confidence:.3f}</p>
            </div>
        </div>
    </div>
    
    <div class="quality-section">
        <h3>🎯 Detection Quality Distribution</h3>
        <div class="stats-grid">
            <div class="stat-item">
                <h4>🟢 High Quality</h4>
                <p style="font-size: 20px; color: #38a169; margin: 5px 0;">{high_quality}</p>
                <p style="font-size: 12px; color: #718096;">Confidence ≥ 0.7</p>
            </div>
            <div class="stat-item">
                <h4>🟡 Medium Quality</h4>
                <p style="font-size: 20px; color: #ed8936; margin: 5px 0;">{medium_quality}</p>
                <p style="font-size: 12px; color: #718096;">Confidence 0.4-0.7</p>
            </div>
            <div class="stat-item">
                <h4>🔴 Low Quality</h4>
                <p style="font-size: 20px; color: #e53e3e; margin: 5px 0;">{low_quality}</p>
                <p style="font-size: 12px; color: #718096;">Confidence < 0.4</p>
            </div>
        </div>
        
        <h4 style="margin-top: 20px;">🔍 Detection Methods Used:</h4>
        <p>'''
    
    for method, count in method_counts.items():
        html_content += f'<span class="method-tag">{method}: {count}</span>'
    
    html_content += f'''
        </p>
    </div>
    
    <div class="frames-section">
        <h3>📹 Enhanced Lip Detection Results ({len(frame_data)} videos)</h3>
        <p><strong>Quality Focus:</strong> Each frame shows detection confidence, validation score, and visual quality assessment</p>
        
        <div class="frames-grid">'''
    
    # Sort by quality for better presentation
    sorted_items = sorted(frame_data.items(), key=lambda x: x[1]['detection_confidence'], reverse=True)
    
    for i, (filename, data) in enumerate(sorted_items, 1):
        quality = data['detection_quality']
        confidence = data['detection_confidence']
        validation = data['validation_score']
        
        # Determine confidence bar color
        if confidence >= 0.7:
            conf_color = 'confidence-high'
        elif confidence >= 0.4:
            conf_color = 'confidence-medium'
        else:
            conf_color = 'confidence-low'
        
        html_content += f'''
            <div class="frame-item {quality}">
                <div class="frame-title">{i}. {filename[:45]}...</div>
                <img src="data:image/jpeg;base64,{data['frame_b64']}" alt="Frame {i}" class="frame-image">
                <div class="frame-specs">
                    <span class="quality-badge quality-{quality}">{quality.upper()} QUALITY</span><br>
                    <strong>Detection:</strong> {data['detection_method']}<br>
                    <strong>Confidence:</strong> {confidence:.3f}
                    <div class="confidence-bar">
                        <div class="confidence-fill {conf_color}" style="width: {confidence*100}%"></div>
                    </div>
                    <strong>Validation:</strong> {validation:.3f}<br>
                    <strong>Classification:</strong> {data['video_classification']}<br>
                    <strong>Resolution:</strong> {data['width']}×{data['height']}<br>
                    <strong>Frames:</strong> {data['frame_count']}<br>
                    <strong>Time:</strong> {data['processing_time']:.2f}s
                </div>
            </div>'''
    
    html_content += '''
        </div>
    </div>
    
    <div class="quality-section">
        <h3>🔍 Quality Assessment Guide</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
            <div style="background: #f0fff4; padding: 15px; border-radius: 8px; border: 2px solid #48bb78;">
                <h4 style="color: #22543d;">🟢 High Quality (≥0.7)</h4>
                <p style="font-size: 12px; color: #2f855a;">Clear lip visibility, good contrast, proper positioning. Ready for training.</p>
            </div>
            <div style="background: #fffaf0; padding: 15px; border-radius: 8px; border: 2px solid #ed8936;">
                <h4 style="color: #9c4221;">🟡 Medium Quality (0.4-0.7)</h4>
                <p style="font-size: 12px; color: #c05621;">Lips visible but may need manual review. Usable with caution.</p>
            </div>
            <div style="background: #fff5f5; padding: 15px; border-radius: 8px; border: 2px solid #e53e3e;">
                <h4 style="color: #742a2a;">🔴 Low Quality (<0.4)</h4>
                <p style="font-size: 12px; color: #c53030;">Poor lip detection. May need reprocessing or exclusion.</p>
            </div>
        </div>
    </div>
</body>
</html>'''
    
    # Save HTML file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Enhanced HTML browser created: {output_file.absolute()}")

def main():
    """Main test function."""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    print("🎯 ENHANCED LIP DETECTION PIPELINE TEST")
    print("="*50)
    print("Focus: Actual lip detection quality over format compliance")
    print()
    
    # Configuration
    base_dir = Path('speaker_sets/full_speaker_sets_top7')
    output_dir = Path('test_enhanced_lip_detection_output')
    manifest_path = Path('enhanced_lip_test_manifest.csv')
    html_output = Path('enhanced_lip_detection_test_results.html')
    
    # Check input directory
    if not base_dir.exists():
        print(f"❌ Input directory not found: {base_dir}")
        return
    
    # Collect test videos
    test_videos = collect_test_videos(base_dir, count=10)
    
    if not test_videos:
        print("❌ No videos found for testing")
        return
    
    # Initialize enhanced pipeline
    pipeline = EnhancedLipDetectionPipeline(
        output_dir=output_dir,
        manifest_path=manifest_path
    )
    
    print(f"\n🎬 Processing {len(test_videos)} videos with enhanced detection...")
    print("-" * 50)
    
    # Process all videos
    results = []
    for i, video_path in enumerate(test_videos, 1):
        print(f"\n[{i}/{len(test_videos)}] Processing: {video_path.name}")
        result = pipeline.process_single_video(video_path)
        results.append(result)
    
    # Save manifest
    pipeline.save_manifest(results)
    
    # Print statistics
    pipeline.print_statistics()
    
    # Extract frames with enhanced metadata
    print("\n📸 Extracting frames with quality metrics...")
    frame_data = extract_frames_with_metadata(results)
    print(f"✅ Extracted {len(frame_data)} frames with quality data")
    
    # Create enhanced HTML browser
    print("\n🌐 Creating enhanced HTML browser...")
    create_enhanced_html_browser(results, frame_data, html_output)
    
    print(f"\n🎉 Enhanced test completed!")
    print(f"📄 Manifest: {manifest_path.absolute()}")
    print(f"🌐 Enhanced browser: {html_output.absolute()}")
    print(f"📁 Output videos: {output_dir.absolute()}")
    print("\n🔍 Review the HTML browser to assess lip detection quality!")

if __name__ == '__main__':
    main()
