#!/usr/bin/env python3
"""
Test Intelligent Lip Detection Pipeline
=======================================

Tests the intelligent lip detection pipeline on exactly 20 randomly selected videos
from speaker_sets/full_speaker_sets_top7 and creates an HTML browser for visual inspection.
"""

import sys
import random
import cv2
import base64
import logging
from pathlib import Path
from typing import List, Dict, Any

# Add src to path
sys.path.append('src')
from preprocessing.intelligent_lip_detection_pipeline import IntelligentLipDetectionPipeline

def collect_random_videos(base_dir: Path, count: int = 20) -> List[Path]:
    """Collect exactly 20 random videos from the speaker sets."""
    all_videos = []
    
    # Collect all MP4 videos
    for speaker_dir in base_dir.iterdir():
        if speaker_dir.is_dir() and speaker_dir.name.startswith('speaker_'):
            for class_dir in speaker_dir.iterdir():
                if class_dir.is_dir():
                    videos = list(class_dir.glob('*.mp4'))
                    all_videos.extend(videos)
    
    print(f"📁 Found {len(all_videos)} total videos in speaker sets")
    
    # Randomly select exactly the requested count
    if len(all_videos) >= count:
        selected = random.sample(all_videos, count)
        print(f"🎲 Randomly selected {len(selected)} videos for testing")
        return selected
    else:
        print(f"⚠️  Only {len(all_videos)} videos available (requested {count})")
        return all_videos

def extract_first_frames(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Extract first frame from each successfully processed video."""
    frame_data = {}
    
    for result in results:
        if result['processing_status'] == 'success':
            output_path = result['output_path']
            
            try:
                cap = cv2.VideoCapture(output_path)
                if cap.isOpened():
                    ret, frame = cap.read()
                    if ret:
                        # Convert to base64 for HTML display
                        _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 90])
                        frame_b64 = base64.b64encode(buffer).decode('utf-8')
                        
                        # Get video properties for display
                        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                        
                        frame_data[result['source_filename']] = {
                            'frame_b64': frame_b64,
                            'width': width,
                            'height': height,
                            'frame_count': frame_count,
                            'processing_method': result.get('processing_method', 'unknown'),
                            'classification': result.get('classification', {}),
                            'output_channels': result.get('output_channels', 'unknown')
                        }
                    cap.release()
            except Exception as e:
                print(f"⚠️  Error extracting frame from {output_path}: {e}")
    
    return frame_data

def create_html_browser(results: List[Dict[str, Any]], frame_data: Dict[str, Any], 
                       output_file: Path) -> None:
    """Create HTML browser showing all processed video first frames."""
    
    # Count statistics
    total_videos = len(results)
    successful = sum(1 for r in results if r['processing_status'] == 'success')
    failed = sum(1 for r in results if r['processing_status'] == 'failed')
    skipped = sum(1 for r in results if r['processing_status'] == 'skipped_already_processed')
    
    # Count processing methods
    method_counts = {}
    classification_counts = {'original': 0, 'already_cropped': 0, 'unknown': 0}
    
    for result in results:
        if result['processing_status'] == 'success':
            method = result.get('processing_method', 'unknown')
            method_counts[method] = method_counts.get(method, 0) + 1
            
            classification = result.get('classification', {}).get('classification', 'unknown')
            classification_counts[classification] = classification_counts.get(classification, 0) + 1
    
    html_content = f'''<!DOCTYPE html>
<html>
<head>
    <title>🎯 Intelligent Lip Detection Pipeline - Test Results</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
        .header {{ background: #4c51bf; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
        .stats-section {{ background: #e6fffa; border: 2px solid #38b2ac; padding: 15px; border-radius: 8px; margin-bottom: 20px; }}
        .stats-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }}
        .stat-item {{ background: white; padding: 10px; border-radius: 6px; text-align: center; }}
        .frames-section {{ background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .frames-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin-top: 20px; }}
        .frame-item {{ border: 2px solid #e2e8f0; border-radius: 8px; padding: 10px; background: #fafafa; }}
        .frame-item.success {{ border-color: #68d391; }}
        .frame-item.failed {{ border-color: #fc8181; }}
        .frame-title {{ font-size: 12px; font-weight: bold; margin-bottom: 8px; color: #2d3748; }}
        .frame-image {{ width: 100%; border: 1px solid #e2e8f0; border-radius: 4px; }}
        .frame-specs {{ font-size: 11px; color: #718096; margin-top: 8px; }}
        .method-tag {{ display: inline-block; background: #bee3f8; color: #2c5282; padding: 2px 6px; border-radius: 4px; font-size: 10px; margin: 2px; }}
        .classification-tag {{ display: inline-block; background: #c6f6d5; color: #2f855a; padding: 2px 6px; border-radius: 4px; font-size: 10px; margin: 2px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 Intelligent Lip Detection Pipeline - Test Results</h1>
        <p><strong>Test Date:</strong> {Path().cwd().name} - 20 Random Videos</p>
        <p>Comprehensive testing of intelligent video classification and lip detection</p>
    </div>
    
    <div class="stats-section">
        <h3>📊 Processing Statistics</h3>
        <div class="stats-grid">
            <div class="stat-item">
                <h4>Total Videos</h4>
                <p style="font-size: 24px; color: #4c51bf; margin: 5px 0;">{total_videos}</p>
            </div>
            <div class="stat-item">
                <h4>✅ Successful</h4>
                <p style="font-size: 24px; color: #38a169; margin: 5px 0;">{successful}</p>
            </div>
            <div class="stat-item">
                <h4>❌ Failed</h4>
                <p style="font-size: 24px; color: #e53e3e; margin: 5px 0;">{failed}</p>
            </div>
            <div class="stat-item">
                <h4>⏭️ Skipped</h4>
                <p style="font-size: 24px; color: #d69e2e; margin: 5px 0;">{skipped}</p>
            </div>
        </div>
        
        <h4 style="margin-top: 20px;">🎬 Video Classifications:</h4>
        <p>
            <span class="classification-tag">Original: {classification_counts['original']}</span>
            <span class="classification-tag">Already Cropped: {classification_counts['already_cropped']}</span>
            <span class="classification-tag">Unknown: {classification_counts['unknown']}</span>
        </p>
        
        <h4>🎯 Processing Methods Used:</h4>
        <p>'''
    
    for method, count in method_counts.items():
        html_content += f'<span class="method-tag">{method}: {count}</span>'
    
    html_content += f'''
        </p>
        
        <h4>🎉 Success Rate: {(successful/total_videos*100):.1f}%</h4>
    </div>
    
    <div class="frames-section">
        <h3>📹 First Frame Visual Inspection ({len(frame_data)} videos)</h3>
        <p><strong>Requirements Check:</strong> All frames should be 96×64 pixels, single channel grayscale, with visible mouth regions</p>
        
        <div class="frames-grid">'''
    
    # Add successful frames
    for i, (filename, data) in enumerate(frame_data.items(), 1):
        classification = data['classification'].get('classification', 'unknown')
        confidence = data['classification'].get('confidence', 0)
        
        html_content += f'''
            <div class="frame-item success">
                <div class="frame-title">{i}. {filename[:40]}...</div>
                <img src="data:image/jpeg;base64,{data['frame_b64']}" alt="Frame {i}" class="frame-image">
                <div class="frame-specs">
                    <strong>Resolution:</strong> {data['width']}×{data['height']}<br>
                    <strong>Frames:</strong> {data['frame_count']}<br>
                    <strong>Channels:</strong> {data['output_channels']}<br>
                    <strong>Method:</strong> {data['processing_method']}<br>
                    <strong>Classification:</strong> {classification} ({confidence:.2f})
                </div>
            </div>'''
    
    # Add failed videos
    failed_count = 0
    for result in results:
        if result['processing_status'] != 'success':
            failed_count += 1
            status = result['processing_status']
            error = result.get('error', result.get('skip_reason', 'Unknown'))
            
            html_content += f'''
            <div class="frame-item failed">
                <div class="frame-title">{len(frame_data) + failed_count}. {result['source_filename'][:40]}...</div>
                <div style="height: 100px; background: #fed7d7; display: flex; align-items: center; justify-content: center; border-radius: 4px;">
                    <span style="color: #e53e3e; font-weight: bold;">❌ {status.upper()}</span>
                </div>
                <div class="frame-specs">
                    <strong>Status:</strong> {status}<br>
                    <strong>Reason:</strong> {error[:50]}...
                </div>
            </div>'''
    
    html_content += '''
        </div>
    </div>
    
    <div class="stats-section">
        <h3>🔍 Technical Validation</h3>
        <p><strong>All successful videos should meet these exact requirements:</strong></p>
        <ul>
            <li>✅ <strong>Resolution:</strong> Exactly 96×64 pixels</li>
            <li>✅ <strong>Channels:</strong> 1 (true grayscale, not 3-channel)</li>
            <li>✅ <strong>Frame Count:</strong> Exactly 32 frames</li>
            <li>✅ <strong>Mouth Visibility:</strong> Clear lip region visible</li>
            <li>✅ <strong>File Format:</strong> Valid MP4</li>
        </ul>
        <p><strong>Visual Assessment:</strong> Review each frame above to ensure proper lip detection and cropping quality.</p>
    </div>
</body>
</html>'''
    
    # Save HTML file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ HTML browser created: {output_file.absolute()}")

def main():
    """Main test function."""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    print("🎯 INTELLIGENT LIP DETECTION PIPELINE TEST")
    print("="*50)
    
    # Configuration
    base_dir = Path('speaker_sets/full_speaker_sets_top7')
    output_dir = Path('test_intelligent_lip_detection_output')
    manifest_path = Path('intelligent_lip_test_manifest.csv')
    html_output = Path('intelligent_lip_detection_test_results.html')
    
    # Check input directory
    if not base_dir.exists():
        print(f"❌ Input directory not found: {base_dir}")
        return
    
    # Collect random videos
    test_videos = collect_random_videos(base_dir, count=20)
    
    if not test_videos:
        print("❌ No videos found for testing")
        return
    
    # Initialize pipeline
    pipeline = IntelligentLipDetectionPipeline(
        output_dir=output_dir,
        manifest_path=manifest_path
    )
    
    print(f"\n🎬 Processing {len(test_videos)} videos...")
    print("-" * 50)
    
    # Process all videos
    results = []
    for i, video_path in enumerate(test_videos, 1):
        print(f"\n[{i}/{len(test_videos)}] Processing: {video_path.name}")
        result = pipeline.process_single_video(video_path)
        results.append(result)
    
    # Save manifest
    pipeline.save_manifest(results)
    
    # Print statistics
    pipeline.print_statistics()
    
    # Extract first frames
    print("\n📸 Extracting first frames for visual inspection...")
    frame_data = extract_first_frames(results)
    print(f"✅ Extracted {len(frame_data)} frames")
    
    # Create HTML browser
    print("\n🌐 Creating HTML browser...")
    create_html_browser(results, frame_data, html_output)
    
    print(f"\n🎉 Test completed!")
    print(f"📄 Manifest: {manifest_path.absolute()}")
    print(f"🌐 Visual browser: {html_output.absolute()}")
    print(f"📁 Output videos: {output_dir.absolute()}")

if __name__ == '__main__':
    main()
