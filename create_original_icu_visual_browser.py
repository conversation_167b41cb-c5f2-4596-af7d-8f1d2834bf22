#!/usr/bin/env python3
"""
Original ICU Pipeline Visual Browser Creator
==========================================

Creates an interactive HTML browser to visually inspect frames from videos
processed with the original ICU geometric cropping pipeline.

Features:
- Extracts 8 sample frames from each processed video
- Organizes results by class for easy comparison
- Shows processing success/failure status
- Displays frame counts and resolution info
- Base64 encoded frames for direct HTML embedding

Author: Augment Agent
Date: 2025-09-29
"""

import cv2
import base64
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple
import logging

class OriginalICUVisualBrowser:
    """Creates visual browser for original ICU pipeline results."""
    
    def __init__(self):
        self.processing_dir = Path("speaker_sets_original_icu_processing")
        self.processed_dir = self.processing_dir / "processed"
        self.manifest_path = self.processing_dir / "original_icu_manifest.csv"
        self.output_html = self.processing_dir / "original_icu_visual_browser.html"
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def extract_sample_frames(self, video_path: Path, num_frames: int = 8) -> List[str]:
        """Extract sample frames from video and convert to base64."""
        try:
            cap = cv2.VideoCapture(str(video_path))
            if not cap.isOpened():
                return []
            
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            if total_frames == 0:
                return []
            
            # Calculate frame indices to sample
            if total_frames <= num_frames:
                frame_indices = list(range(total_frames))
            else:
                frame_indices = [int(i * total_frames / num_frames) for i in range(num_frames)]
            
            frames_b64 = []
            for frame_idx in frame_indices:
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                ret, frame = cap.read()
                
                if ret:
                    # Convert frame to base64
                    _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
                    frame_b64 = base64.b64encode(buffer).decode('utf-8')
                    frames_b64.append(frame_b64)
            
            cap.release()
            return frames_b64
            
        except Exception as e:
            self.logger.error(f"Error extracting frames from {video_path}: {e}")
            return []
    
    def load_processing_results(self) -> pd.DataFrame:
        """Load processing results from manifest."""
        if not self.manifest_path.exists():
            self.logger.error(f"Manifest not found: {self.manifest_path}")
            return pd.DataFrame()
        
        try:
            df = pd.read_csv(self.manifest_path)
            self.logger.info(f"Loaded {len(df)} processing results")
            return df
        except Exception as e:
            self.logger.error(f"Error loading manifest: {e}")
            return pd.DataFrame()
    
    def create_html_browser(self):
        """Create interactive HTML browser for visual inspection."""
        print("🎬 Extracting sample frames from processed videos...")
        
        # Load processing results
        results_df = self.load_processing_results()
        if results_df.empty:
            print("❌ No processing results found")
            return
        
        # Group by class
        results_by_class = {}
        for class_name in results_df['class'].unique():
            class_results = results_df[results_df['class'] == class_name].to_dict('records')
            results_by_class[class_name] = class_results
        
        # Extract frames for each video
        video_data = {}
        for class_name, class_results in results_by_class.items():
            video_data[class_name] = []
            
            for result in class_results:
                video_info = {
                    'filename': Path(result['input_path']).name,
                    'success': result['success'],
                    'processing_time': result.get('processing_time', 0),
                    'frame_count': result.get('frame_count', 0),
                    'original_resolution': result.get('original_resolution', ''),
                    'output_resolution': result.get('output_resolution', ''),
                    'error': result.get('error', ''),
                    'frames': []
                }
                
                # Extract frames if video was successfully processed
                if result['success'] and result.get('output_path'):
                    output_path = Path(result['output_path'])
                    if output_path.exists():
                        print(f"   📹 Processing: {video_info['filename']}")
                        video_info['frames'] = self.extract_sample_frames(output_path)
                
                video_data[class_name].append(video_info)
        
        # Generate HTML
        html_content = self.generate_html_content(video_data, results_df)
        
        # Save HTML file
        with open(self.output_html, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ Visual browser created: {self.output_html}")
        print(f"🌐 Open in browser: file://{self.output_html.absolute()}")
    
    def generate_html_content(self, video_data: Dict, results_df: pd.DataFrame) -> str:
        """Generate HTML content for the visual browser."""
        
        # Calculate summary statistics
        total_videos = len(results_df)
        successful_videos = len(results_df[results_df['success'] == True])
        success_rate = (successful_videos / total_videos * 100) if total_videos > 0 else 0
        
        html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Original ICU Pipeline - Visual Results Browser</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }}
        .summary {{ background: white; padding: 15px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .class-section {{ background: white; margin-bottom: 20px; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .class-header {{ background: #4a5568; color: white; padding: 15px; font-weight: bold; font-size: 18px; }}
        .video-item {{ border-bottom: 1px solid #e2e8f0; padding: 20px; }}
        .video-item:last-child {{ border-bottom: none; }}
        .video-header {{ display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }}
        .video-title {{ font-weight: bold; color: #2d3748; }}
        .status-success {{ background: #48bb78; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; }}
        .status-failed {{ background: #f56565; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; }}
        .video-info {{ font-size: 12px; color: #718096; margin-bottom: 15px; }}
        .frames-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px; }}
        .frame-item {{ text-align: center; }}
        .frame-item img {{ max-width: 100%; height: auto; border: 1px solid #e2e8f0; border-radius: 4px; }}
        .frame-label {{ font-size: 10px; color: #a0aec0; margin-top: 5px; }}
        .error-message {{ background: #fed7d7; color: #c53030; padding: 10px; border-radius: 4px; font-size: 12px; }}
        .stats {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }}
        .stat-item {{ text-align: center; }}
        .stat-number {{ font-size: 24px; font-weight: bold; color: #4a5568; }}
        .stat-label {{ font-size: 12px; color: #718096; text-transform: uppercase; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🎬 Original ICU Pipeline - Visual Results Browser</h1>
        <p>Visual inspection of videos processed with the original ICU geometric cropping pipeline (pre-checkpoint 8)</p>
    </div>
    
    <div class="summary">
        <h2>📊 Processing Summary</h2>
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">{total_videos}</div>
                <div class="stat-label">Total Videos</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{successful_videos}</div>
                <div class="stat-label">Successful</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{total_videos - successful_videos}</div>
                <div class="stat-label">Failed</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{success_rate:.1f}%</div>
                <div class="stat-label">Success Rate</div>
            </div>
        </div>
    </div>
"""
        
        # Add class sections
        for class_name, videos in video_data.items():
            class_successful = sum(1 for v in videos if v['success'])
            class_total = len(videos)
            
            html += f"""
    <div class="class-section">
        <div class="class-header">
            📁 {class_name.upper()} ({class_successful}/{class_total} successful)
        </div>
"""
            
            for video in videos:
                status_class = "status-success" if video['success'] else "status-failed"
                status_text = "SUCCESS" if video['success'] else "FAILED"
                
                html += f"""
        <div class="video-item">
            <div class="video-header">
                <div class="video-title">{video['filename']}</div>
                <div class="{status_class}">{status_text}</div>
            </div>
            <div class="video-info">
                Processing Time: {video['processing_time']:.2f}s | 
                Frames: {video['frame_count']} | 
                Resolution: {video['original_resolution']} → {video['output_resolution']}
            </div>
"""
                
                if video['success'] and video['frames']:
                    html += """
            <div class="frames-grid">
"""
                    for i, frame_b64 in enumerate(video['frames']):
                        html += f"""
                <div class="frame-item">
                    <img src="data:image/jpeg;base64,{frame_b64}" alt="Frame {i+1}">
                    <div class="frame-label">Frame {i+1}</div>
                </div>
"""
                    html += """
            </div>
"""
                elif not video['success']:
                    html += f"""
            <div class="error-message">
                ❌ Processing failed: {video['error']}
            </div>
"""
                else:
                    html += """
            <div class="error-message">
                ⚠️ No frames available for display
            </div>
"""
                
                html += """
        </div>
"""
            
            html += """
    </div>
"""
        
        html += """
</body>
</html>
"""
        
        return html

def main():
    """Main function to create visual browser."""
    browser = OriginalICUVisualBrowser()
    browser.create_html_browser()

if __name__ == "__main__":
    main()
