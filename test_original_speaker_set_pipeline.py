#!/usr/bin/env python3
"""
Original Speaker Set Pipeline Validation Test
============================================

This script tests the original ICU geometric cropping pipeline that was used
for speaker set videos before the GRID corpus and adaptive pipeline development.

OBJECTIVE:
- Use the original ICU geometric cropping pipeline from src/preprocessing/icu_geometric_crop.py
- Process speaker set videos with the original approach (top 50% height, middle 33% width)
- Generate visual validation interface to compare with recent adaptive pipeline results
- Understand how the original pipeline handled speaker set video format

ORIGINAL PIPELINE SPECS:
- Crops to top 50% height and middle 33% width (top-middle grid cell)
- Resizes to 96×96 pixels (not 96×64 like recent pipelines)
- No image processing applied (pure geometric only)
- Preserves original color format

Author: Augment Agent
Date: 2025-09-29
Status: Original Pipeline Validation
"""

import os
import sys
import cv2
import numpy as np
import random
import json
import time
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import logging
from datetime import datetime

# Add src to path for imports
sys.path.append('src')
sys.path.append('src/preprocessing')

# Import the original ICU geometric cropper
from icu_geometric_crop import ICUGeometricCropper

class OriginalPipelineValidator:
    """
    Validator for testing the original ICU geometric cropping pipeline on speaker set videos.
    """
    
    def __init__(self):
        """Initialize the validator with the original pipeline."""
        self.output_dir = Path("original_pipeline_validation")
        self.output_dir.mkdir(exist_ok=True)
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # Define the 7 target classes
        self.target_classes = [
            'doctor', 'glasses', 'i_need_to_move', 
            'my_back_hurts', 'my_mouth_is_dry', 'phone', 'pillow'
        ]
        
        # Speaker sets directory
        self.speaker_sets_dir = Path("speaker_sets/full_speaker_sets_top7")
        
    def select_random_videos(self, videos_per_class: int = 5) -> Dict[str, List[Path]]:
        """
        Randomly select videos from each class subfolder.
        
        Args:
            videos_per_class: Number of videos to select per class
            
        Returns:
            Dictionary mapping class names to lists of selected video paths
        """
        selected_videos = {}
        
        for class_name in self.target_classes:
            class_videos = []
            
            # Search across all speakers for this class
            for speaker_dir in self.speaker_sets_dir.glob("speaker_*"):
                class_dir = speaker_dir / class_name
                if class_dir.exists():
                    # Find all MP4 files in this class directory
                    mp4_files = list(class_dir.glob("*.mp4"))
                    class_videos.extend(mp4_files)
            
            if class_videos:
                # Randomly select videos_per_class videos
                if len(class_videos) >= videos_per_class:
                    selected = random.sample(class_videos, videos_per_class)
                else:
                    selected = class_videos  # Take all if fewer than requested
                
                selected_videos[class_name] = selected
                self.logger.info(f"📁 {class_name}: Selected {len(selected)} videos from {len(class_videos)} available")
            else:
                self.logger.warning(f"⚠️ No videos found for class: {class_name}")
                selected_videos[class_name] = []
        
        total_selected = sum(len(videos) for videos in selected_videos.values())
        self.logger.info(f"🎯 Total videos selected: {total_selected}")
        
        return selected_videos
    
    def process_videos_with_original_pipeline(self, selected_videos: Dict[str, List[Path]]) -> Dict[str, Any]:
        """
        Process selected videos using the original ICU geometric cropping pipeline.
        
        Args:
            selected_videos: Dictionary of class names to video paths
            
        Returns:
            Dictionary with processing results
        """
        self.logger.info("🚀 Processing videos with original ICU geometric cropping pipeline...")
        
        # Create temporary directory for original pipeline processing
        temp_input_dir = self.output_dir / "temp_input"
        temp_output_dir = self.output_dir / "processed"
        temp_input_dir.mkdir(exist_ok=True)
        temp_output_dir.mkdir(exist_ok=True)
        
        # Copy selected videos to temp input directory with class prefixes
        video_mapping = {}  # Maps temp filename to original info
        
        for class_name, videos in selected_videos.items():
            for i, video_path in enumerate(videos):
                # Create a temporary filename that includes class info
                temp_filename = f"{class_name}_{i:02d}_{video_path.name}"
                temp_path = temp_input_dir / temp_filename
                
                # Copy video to temp location
                import shutil
                shutil.copy2(video_path, temp_path)
                
                video_mapping[temp_filename] = {
                    'original_path': str(video_path),
                    'class_name': class_name,
                    'video_index': i,
                    'temp_path': str(temp_path)
                }
        
        # Initialize original ICU geometric cropper
        # Note: Original pipeline outputs 96×96, not 96×64
        cropper = ICUGeometricCropper(
            source_dir=str(temp_input_dir),
            output_dir=str(temp_output_dir),
            manifest_path=str(self.output_dir / "original_processing_manifest.csv")
        )
        
        # Process all videos
        self.logger.info(f"📹 Processing {len(video_mapping)} videos with original pipeline...")
        processing_results = cropper.process_all_videos()
        
        # Organize results by class
        results_by_class = {}
        successful = 0
        failed = 0
        
        for result in processing_results:
            if result:
                # Extract class info from filename
                source_filename = Path(result['source_path']).name
                if source_filename in video_mapping:
                    class_name = video_mapping[source_filename]['class_name']
                    
                    if class_name not in results_by_class:
                        results_by_class[class_name] = []
                    
                    # Add original path info
                    result['original_path'] = video_mapping[source_filename]['original_path']
                    result['class_name'] = class_name
                    
                    results_by_class[class_name].append(result)
                    
                    if result.get('processing_status') == 'success':
                        successful += 1
                    else:
                        failed += 1
        
        # Calculate overall statistics
        total_videos = successful + failed
        success_rate = (successful / total_videos * 100) if total_videos > 0 else 0
        
        summary = {
            'total_videos': total_videos,
            'successful': successful,
            'failed': failed,
            'success_rate': success_rate,
            'results_by_class': results_by_class,
            'video_mapping': video_mapping,
            'processing_timestamp': datetime.now().isoformat(),
            'pipeline_type': 'original_icu_geometric_crop',
            'output_resolution': '96x96',
            'crop_method': 'top_50pct_mid_33pct'
        }
        
        self.logger.info(f"🎯 Original pipeline processing complete: {successful}/{total_videos} successful ({success_rate:.1f}%)")
        
        return summary
    
    def extract_sample_frames(self, video_path: Path, max_frames: int = 5) -> List[np.ndarray]:
        """
        Extract sample frames from a video for visual inspection.
        
        Args:
            video_path: Path to video file
            max_frames: Maximum number of frames to extract
            
        Returns:
            List of frame arrays
        """
        frames = []
        
        try:
            cap = cv2.VideoCapture(str(video_path))
            if not cap.isOpened():
                return frames
            
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            if total_frames == 0:
                cap.release()
                return frames
            
            # Extract frames at regular intervals
            frame_indices = np.linspace(0, total_frames - 1, min(max_frames, total_frames), dtype=int)
            
            for frame_idx in frame_indices:
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                ret, frame = cap.read()
                if ret:
                    frames.append(frame)
            
            cap.release()
            
        except Exception as e:
            self.logger.warning(f"Failed to extract frames from {video_path}: {e}")
        
        return frames
    
    def generate_visual_inspection_interface(self, processing_summary: Dict[str, Any]) -> Path:
        """
        Generate an interactive HTML interface for visual inspection.
        
        Args:
            processing_summary: Results from original pipeline processing
            
        Returns:
            Path to generated HTML file
        """
        self.logger.info("🎨 Generating visual inspection interface...")
        
        # Create directories for frame extraction
        frames_dir = self.output_dir / "frames"
        frames_dir.mkdir(exist_ok=True)
        
        original_frames_dir = frames_dir / "original"
        processed_frames_dir = frames_dir / "processed"
        original_frames_dir.mkdir(exist_ok=True)
        processed_frames_dir.mkdir(exist_ok=True)
        
        # Extract frames for visual comparison
        frame_data = []
        
        for class_name, results in processing_summary['results_by_class'].items():
            for result in results:
                original_path = Path(result['original_path'])
                processed_path = Path(result['output_path'])
                
                # Extract original frames
                original_frames = self.extract_sample_frames(original_path, max_frames=3)
                
                # Extract processed frames
                processed_frames = self.extract_sample_frames(processed_path, max_frames=3)
                
                # Save frames for web display
                video_id = f"{class_name}_{original_path.stem}"
                
                original_frame_paths = []
                processed_frame_paths = []
                
                for i, frame in enumerate(original_frames):
                    frame_filename = f"{video_id}_original_{i:02d}.jpg"
                    frame_path = original_frames_dir / frame_filename
                    cv2.imwrite(str(frame_path), frame)
                    original_frame_paths.append(f"frames/original/{frame_filename}")
                
                for i, frame in enumerate(processed_frames):
                    frame_filename = f"{video_id}_processed_{i:02d}.jpg"
                    frame_path = processed_frames_dir / frame_filename
                    cv2.imwrite(str(frame_path), frame)
                    processed_frame_paths.append(f"frames/processed/{frame_filename}")
                
                frame_data.append({
                    'class_name': class_name,
                    'video_name': original_path.name,
                    'original_frames': original_frame_paths,
                    'processed_frames': processed_frame_paths,
                    'success': result.get('processing_status') == 'success',
                    'original_resolution': result.get('original_resolution', 'unknown'),
                    'output_resolution': result.get('output_resolution', '96x96'),
                    'crop_method': result.get('crop_method', 'icu_geometric_top50_mid33'),
                    'processed_frames_count': result.get('processed_frames', 0),
                    'success_rate': result.get('success_rate', 0)
                })
        
        # Generate HTML content
        html_content = self.create_html_interface(processing_summary, frame_data)
        
        # Save HTML file
        html_path = self.output_dir / "original_pipeline_visual_inspection.html"
        with open(html_path, 'w') as f:
            f.write(html_content)
        
        self.logger.info(f"🎨 Visual inspection interface saved: {html_path}")
        return html_path

    def create_html_interface(self, processing_summary: Dict[str, Any], frame_data: List[Dict]) -> str:
        """
        Create HTML content for visual inspection interface.

        Args:
            processing_summary: Processing results summary
            frame_data: Frame data for visual display

        Returns:
            HTML content string
        """
        # Calculate statistics
        total_videos = processing_summary['total_videos']
        successful = processing_summary['successful']
        success_rate = processing_summary['success_rate']

        # Group frame data by class
        frames_by_class = {}
        for frame_info in frame_data:
            class_name = frame_info['class_name']
            if class_name not in frames_by_class:
                frames_by_class[class_name] = []
            frames_by_class[class_name].append(frame_info)

        html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Original Speaker Set Pipeline - Visual Inspection</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }}

        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }}

        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }}

        .header p {{
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }}

        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}

        .stat-card {{
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }}

        .stat-value {{
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }}

        .stat-label {{
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}

        .class-section {{
            background: white;
            margin-bottom: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }}

        .class-header {{
            background: #667eea;
            color: white;
            padding: 20px;
            font-size: 1.3em;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}

        .video-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            padding: 20px;
        }}

        .video-card {{
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            transition: transform 0.2s, box-shadow 0.2s;
        }}

        .video-card:hover {{
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }}

        .video-card.success {{
            border-color: #4CAF50;
        }}

        .video-card.failed {{
            border-color: #f44336;
        }}

        .video-header {{
            padding: 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
        }}

        .video-title {{
            font-weight: bold;
            margin-bottom: 5px;
            word-break: break-all;
        }}

        .video-meta {{
            font-size: 0.85em;
            color: #666;
        }}

        .frames-container {{
            padding: 15px;
        }}

        .frames-row {{
            margin-bottom: 15px;
        }}

        .frames-label {{
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }}

        .frames-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 8px;
        }}

        .frame-img {{
            width: 100%;
            height: auto;
            border-radius: 4px;
            border: 1px solid #ddd;
            cursor: pointer;
            transition: transform 0.2s;
        }}

        .frame-img:hover {{
            transform: scale(1.05);
        }}

        .status-badge {{
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }}

        .status-success {{
            background: #4CAF50;
            color: white;
        }}

        .status-failed {{
            background: #f44336;
            color: white;
        }}

        .pipeline-info {{
            background: #e3f2fd;
            border-left: 4px solid #2196F3;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 0 8px 8px 0;
        }}

        .pipeline-info h3 {{
            margin-top: 0;
            color: #1976D2;
        }}

        .comparison-note {{
            background: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 8px 8px 0;
        }}

        .modal {{
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
        }}

        .modal-content {{
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-width: 90%;
            max-height: 90%;
        }}

        .modal-content img {{
            width: 100%;
            height: auto;
            border-radius: 8px;
        }}

        .close {{
            position: absolute;
            top: 10px;
            right: 25px;
            color: white;
            font-size: 35px;
            font-weight: bold;
            cursor: pointer;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🎬 Original Speaker Set Pipeline</h1>
        <p>Visual Inspection Report - ICU Geometric Cropping (Pre-GRID Development)</p>
        <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>

    <div class="pipeline-info">
        <h3>📋 Original Pipeline Specifications</h3>
        <ul>
            <li><strong>Method:</strong> ICU Geometric Cropping (Top 50% height, Middle 33% width)</li>
            <li><strong>Output Resolution:</strong> 96×96 pixels (not 96×64 like recent pipelines)</li>
            <li><strong>Processing:</strong> Pure geometric operations only (no image enhancement)</li>
            <li><strong>Color Format:</strong> Preserves original color format</li>
            <li><strong>Target:</strong> Pre-cropped face videos with lips in top-middle portion</li>
        </ul>
    </div>

    <div class="comparison-note">
        <strong>📊 Comparison Context:</strong> This is the original preprocessing approach used before GRID corpus integration and adaptive pipeline development. Compare these results with the recent adaptive pipeline (90% success rate) to understand the evolution of the preprocessing methodology.
    </div>

    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value">{total_videos}</div>
            <div class="stat-label">Total Videos</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{successful}</div>
            <div class="stat-label">Successful</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{success_rate:.1f}%</div>
            <div class="stat-label">Success Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">96×96</div>
            <div class="stat-label">Output Resolution</div>
        </div>
    </div>"""

        # Add class sections
        for class_name in sorted(frames_by_class.keys()):
            class_frames = frames_by_class[class_name]
            class_success_count = sum(1 for f in class_frames if f['success'])
            class_total = len(class_frames)
            class_success_rate = (class_success_count / class_total * 100) if class_total > 0 else 0

            html_content += f"""
    <div class="class-section">
        <div class="class-header">
            {class_name.replace('_', ' ').title()}
            <span style="float: right; font-weight: normal;">
                {class_success_count}/{class_total} ({class_success_rate:.1f}%)
            </span>
        </div>
        <div class="video-grid">"""

            for frame_info in class_frames:
                status_class = "success" if frame_info['success'] else "failed"
                status_badge_class = "status-success" if frame_info['success'] else "status-failed"
                status_text = "SUCCESS" if frame_info['success'] else "FAILED"

                html_content += f"""
            <div class="video-card {status_class}">
                <div class="video-header">
                    <div class="video-title">{frame_info['video_name']}</div>
                    <div class="video-meta">
                        <span class="status-badge {status_badge_class}">{status_text}</span>
                        <br>
                        Original: {frame_info['original_resolution']} → Processed: {frame_info['output_resolution']}
                        <br>
                        Frames: {frame_info['processed_frames_count']} | Success Rate: {frame_info['success_rate']:.1%}
                    </div>
                </div>
                <div class="frames-container">
                    <div class="frames-row">
                        <div class="frames-label">📹 Original Frames</div>
                        <div class="frames-grid">"""

                for frame_path in frame_info['original_frames']:
                    html_content += f"""
                            <img src="{frame_path}" alt="Original frame" class="frame-img" onclick="openModal(this.src)">"""

                html_content += f"""
                        </div>
                    </div>
                    <div class="frames-row">
                        <div class="frames-label">⚙️ Processed Frames (96×96)</div>
                        <div class="frames-grid">"""

                for frame_path in frame_info['processed_frames']:
                    html_content += f"""
                            <img src="{frame_path}" alt="Processed frame" class="frame-img" onclick="openModal(this.src)">"""

                html_content += """
                        </div>
                    </div>
                </div>
            </div>"""

            html_content += """
        </div>
    </div>"""

        # Add modal and JavaScript
        html_content += """
    <!-- Modal for enlarged images -->
    <div id="imageModal" class="modal" onclick="closeModal()">
        <span class="close" onclick="closeModal()">&times;</span>
        <div class="modal-content">
            <img id="modalImage" src="" alt="Enlarged frame">
        </div>
    </div>

    <script>
        function openModal(src) {
            document.getElementById('imageModal').style.display = 'block';
            document.getElementById('modalImage').src = src;
        }

        function closeModal() {
            document.getElementById('imageModal').style.display = 'none';
        }

        // Close modal when clicking outside the image
        window.onclick = function(event) {
            var modal = document.getElementById('imageModal');
            if (event.target == modal) {
                closeModal();
            }
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        });
    </script>
</body>
</html>"""

        return html_content

def main():
    """
    Main execution function for original pipeline validation.
    """
    print("🎬 Original Speaker Set Pipeline Validation")
    print("=" * 70)
    print("🎯 OBJECTIVE: Test original ICU geometric cropping pipeline")
    print("📊 BASELINE: Pre-GRID corpus and adaptive pipeline development")
    print("🔍 METHOD: Top 50% height, middle 33% width → 96×96 pixels")
    print("✅ COMPARISON: Compare with recent adaptive pipeline results")
    print()

    # Initialize validator
    validator = OriginalPipelineValidator()

    # Step 1: Select random videos
    print("📋 Step 1: Selecting random videos from speaker sets...")
    selected_videos = validator.select_random_videos(videos_per_class=5)

    if not any(selected_videos.values()):
        print("❌ No videos found in speaker sets directory!")
        return

    # Step 2: Process with original pipeline
    print("\n🚀 Step 2: Processing videos with original ICU geometric cropping pipeline...")
    processing_summary = validator.process_videos_with_original_pipeline(selected_videos)

    # Step 3: Generate visual inspection interface
    print("\n🎨 Step 3: Generating visual inspection interface...")
    html_path = validator.generate_visual_inspection_interface(processing_summary)

    # Step 4: Save results
    results_path = validator.output_dir / "original_pipeline_results.json"
    with open(results_path, 'w') as f:
        # Convert Path objects to strings for JSON serialization
        json_summary = json.loads(json.dumps(processing_summary, default=str))
        json.dump(json_summary, f, indent=2)

    # Display results summary
    print("\n" + "=" * 70)
    print("🎯 ORIGINAL PIPELINE VALIDATION RESULTS")
    print("=" * 70)

    total_videos = processing_summary['total_videos']
    successful = processing_summary['successful']
    success_rate = processing_summary['success_rate']

    print(f"📊 Total Videos Processed: {total_videos}")
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {processing_summary['failed']}")
    print(f"📈 Success Rate: {success_rate:.1f}%")
    print()

    print("📋 Results by Class:")
    for class_name, results in processing_summary['results_by_class'].items():
        class_success = sum(1 for r in results if r.get('processing_status') == 'success')
        class_total = len(results)
        class_rate = (class_success / class_total * 100) if class_total > 0 else 0
        print(f"   {class_name}: {class_success}/{class_total} ({class_rate:.1f}%)")

    print()
    print("🎨 Visual Inspection Interface:")
    print(f"   📄 HTML Report: {html_path}")
    print(f"   💾 JSON Results: {results_path}")
    print()

    # Comparison with adaptive pipeline
    print("📊 COMPARISON WITH RECENT ADAPTIVE PIPELINE:")
    print("   🔄 Adaptive Pipeline: 90.0% success rate (speaker set videos)")
    print(f"   📈 Original Pipeline: {success_rate:.1f}% success rate")

    if success_rate >= 90:
        print("   🎉 Original pipeline performs as well as adaptive pipeline!")
    elif success_rate >= 70:
        print("   ✅ Original pipeline shows good performance")
    elif success_rate >= 50:
        print("   ⚠️ Original pipeline shows moderate performance")
    else:
        print("   ❌ Original pipeline shows lower performance than adaptive")

    print()
    print("🚀 NEXT STEPS:")
    print("   1. Open the HTML report to visually inspect results")
    print("   2. Compare frame quality with adaptive pipeline results")
    print("   3. Analyze differences in mouth region extraction")
    print("   4. Consider integration insights for future development")

    print(f"\n📋 Open this file in your browser: {html_path.absolute()}")

if __name__ == "__main__":
    main()
