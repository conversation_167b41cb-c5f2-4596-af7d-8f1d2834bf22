#!/usr/bin/env python3
"""
Visual Browser for Proven Pipeline Results
==========================================

Creates browser interface to visually inspect frames from videos processed
with the proven GRID preprocessing pipeline.

Author: Augment Agent
Date: 2025-09-29
"""

import cv2
import pathlib
import base64
import json
from typing import List, Dict, Any
import pandas as pd

def extract_sample_frames(video_path: pathlib.Path, num_frames: int = 8) -> List[str]:
    """Extract sample frames from video and return as base64 encoded images."""
    frames_b64 = []
    
    try:
        cap = cv2.VideoCapture(str(video_path))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        if total_frames == 0:
            return []
        
        # Extract evenly spaced frames
        frame_indices = [int(i * total_frames / num_frames) for i in range(num_frames)]
        
        for frame_idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = cap.read()
            
            if ret:
                # Convert to RGB if color, keep grayscale as is
                if len(frame.shape) == 3:
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                # Encode frame as base64
                _, buffer = cv2.imencode('.png', frame)
                frame_b64 = base64.b64encode(buffer).decode('utf-8')
                frames_b64.append(frame_b64)
        
        cap.release()
        
    except Exception as e:
        print(f"Error extracting frames from {video_path}: {e}")
    
    return frames_b64

def create_proven_pipeline_visual_browser():
    """Create visual browser interface for proven pipeline results."""
    
    # Load manifest
    manifest_path = pathlib.Path("speaker_sets_proven_processing/proven_pipeline_manifest.csv")
    if not manifest_path.exists():
        print("❌ Manifest file not found! Run process_speaker_sets_proven_pipeline.py first.")
        return
    
    df = pd.read_csv(manifest_path)
    
    print("🎬 Extracting sample frames from processed videos...")
    
    # Process each video
    video_data = []
    
    for idx, row in df.iterrows():
        video_name = pathlib.Path(row['input_path']).name
        output_path = pathlib.Path(row['output_path'])
        
        print(f"   📹 Processing: {video_name}")
        
        # Extract frames if video exists
        frames_b64 = []
        if output_path.exists():
            frames_b64 = extract_sample_frames(output_path, num_frames=8)
        
        video_info = {
            'name': video_name,
            'success': row['success'],
            'class_name': row.get('class_name', 'unknown'),
            'speaker_set': row.get('speaker_set', 'unknown'),
            'original_frames': row.get('original_frames', 0),
            'processed_frames': row.get('processed_frames', 0),
            'processing_time': row.get('processing_time', 0),
            'error_message': row.get('error_message', ''),
            'frames': frames_b64
        }
        
        video_data.append(video_info)
    
    # Group by class for better organization
    videos_by_class = {}
    for video in video_data:
        class_name = video['class_name']
        if class_name not in videos_by_class:
            videos_by_class[class_name] = []
        videos_by_class[class_name].append(video)
    
    # Create HTML interface
    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Proven Pipeline Visual Results</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .header {{
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }}
        .summary {{
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .class-section {{
            background: white;
            margin-bottom: 30px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .class-header {{
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 20px;
            font-size: 20px;
            font-weight: bold;
        }}
        .video-card {{
            border-bottom: 1px solid #eee;
            padding: 20px;
        }}
        .video-card:last-child {{
            border-bottom: none;
        }}
        .video-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }}
        .video-name {{
            font-weight: bold;
            font-size: 16px;
        }}
        .status-success {{
            color: #28a745;
            font-weight: bold;
        }}
        .status-failed {{
            color: #dc3545;
            font-weight: bold;
        }}
        .video-details {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }}
        .detail-item {{
            font-size: 14px;
        }}
        .frames-container {{
            margin-top: 15px;
        }}
        .frames-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
        }}
        .frame-item {{
            text-align: center;
        }}
        .frame-image {{
            max-width: 100%;
            height: auto;
            border: 2px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
        }}
        .frame-label {{
            margin-top: 5px;
            font-size: 11px;
            color: #666;
        }}
        .error-message {{
            color: #dc3545;
            background-color: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🎬 Proven Pipeline Visual Results</h1>
        <p>Visual inspection of speaker set videos processed with proven GRID preprocessing pipeline</p>
        <p><strong>Target Format:</strong> 96×64 grayscale, 32 frames</p>
    </div>
    
    <div class="summary">
        <h2>📊 Processing Summary</h2>
        <p><strong>Total Videos:</strong> {len(video_data)}</p>
        <p><strong>Successful:</strong> {sum(1 for v in video_data if v['success'])}</p>
        <p><strong>Failed:</strong> {sum(1 for v in video_data if not v['success'])}</p>
        <p><strong>Success Rate:</strong> {sum(1 for v in video_data if v['success']) / len(video_data) * 100:.1f}%</p>
        <p><strong>Classes Processed:</strong> {len(videos_by_class)}</p>
    </div>
"""
    
    # Add class sections
    for class_name, videos in videos_by_class.items():
        successful_videos = sum(1 for v in videos if v['success'])
        total_videos = len(videos)
        
        html_content += f"""
    <div class="class-section">
        <div class="class-header">
            📁 {class_name.upper().replace('_', ' ')} ({successful_videos}/{total_videos} successful)
        </div>
"""
        
        # Add video cards for this class
        for video in videos:
            status_class = "status-success" if video['success'] else "status-failed"
            status_text = "✅ SUCCESS" if video['success'] else "❌ FAILED"
            
            html_content += f"""
        <div class="video-card">
            <div class="video-header">
                <div class="video-name">{video['name']}</div>
                <div class="{status_class}">{status_text}</div>
            </div>
            
            <div class="video-details">
                <div class="detail-item">
                    <strong>Speaker Set:</strong> {video['speaker_set']}
                </div>
                <div class="detail-item">
                    <strong>Original Frames:</strong> {video['original_frames']}
                </div>
                <div class="detail-item">
                    <strong>Processed Frames:</strong> {video['processed_frames']}
                </div>
                <div class="detail-item">
                    <strong>Processing Time:</strong> {video['processing_time']:.2f}s
                </div>
            </div>
            
            {f'<div class="error-message"><strong>Error:</strong> {video["error_message"]}</div>' if video['error_message'] else ''}
            
            <div class="frames-container">
                <h4>Sample Frames (96×64 grayscale, 32 total frames)</h4>
                <div class="frames-grid">
"""
            
            # Add frames
            if video['frames']:
                for i, frame_b64 in enumerate(video['frames']):
                    html_content += f"""
                    <div class="frame-item">
                        <img src="data:image/png;base64,{frame_b64}" alt="Frame {i+1}" class="frame-image">
                        <div class="frame-label">Frame {i+1}</div>
                    </div>
"""
            else:
                html_content += """
                    <div class="frame-item">
                        <div style="padding: 40px; background-color: #f8f9fa; border: 2px dashed #ddd; border-radius: 5px;">
                            No frames available
                        </div>
                    </div>
"""
            
            html_content += """
                </div>
            </div>
        </div>
"""
        
        html_content += """
    </div>
"""
    
    html_content += """
</body>
</html>
"""
    
    # Save HTML file
    html_path = pathlib.Path("speaker_sets_proven_processing/proven_pipeline_visual_browser.html")
    with open(html_path, 'w') as f:
        f.write(html_content)
    
    print(f"✅ Visual browser created: {html_path}")
    return html_path

if __name__ == "__main__":
    html_path = create_proven_pipeline_visual_browser()
    print(f"🌐 Open in browser: file://{html_path.absolute()}")
