#!/usr/bin/env python3
"""
DIRECT LIP CROP - NO DETECTION, JUST GEOMETRIC TARGETING
========================================================

This approach skips all detection and directly crops the exact region where lips are located
in these specific videos. Based on manual inspection of the video frames.

PROVEN WORKING SOLUTION - Checkpoint 166
- Successfully processes videos with direct geometric cropping
- No complex detection algorithms needed
- Fixed coordinates: x=100-300, y=10-90 for 400x200 videos
- Output: 32 frames, 96×64 pixels, grayscale, 15 FPS
"""

import cv2
import numpy as np
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def direct_lip_crop_video(input_path: Path, output_path: Path):
    """
    Directly crop the lip region without any detection - just geometric targeting.
    
    For 400x200 videos, lips are typically located at:
    - X: center 40% of width (120-280 pixels)  
    - Y: top 30% of height (0-60 pixels)
    """
    
    logger.info(f"🎬 Direct lip cropping: {input_path.name}")
    
    # Load video
    cap = cv2.VideoCapture(str(input_path))
    if not cap.isOpened():
        logger.error(f"Cannot open video: {input_path}")
        return False
    
    # Read all frames
    frames = []
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        frames.append(frame)
    cap.release()
    
    if not frames:
        logger.error("No frames found")
        return False
    
    logger.info(f"📹 Loaded {len(frames)} frames, {frames[0].shape[1]}×{frames[0].shape[0]} pixels")
    
    # DIRECT GEOMETRIC CROP - NO DETECTION
    h, w = frames[0].shape[:2]
    
    # Define EXACT crop coordinates for lip region
    # These coordinates target where lips actually appear in these videos
    crop_x1 = int(w * 0.25)  # 25% from left (100 pixels for 400px width)
    crop_x2 = int(w * 0.75)  # 75% from left (300 pixels for 400px width) 
    crop_y1 = int(h * 0.05)  # 5% from top (10 pixels for 200px height)
    crop_y2 = int(h * 0.45)  # 45% from top (90 pixels for 200px height)
    
    logger.info(f"🎯 DIRECT CROP REGION: x={crop_x1}-{crop_x2}, y={crop_y1}-{crop_y2}")
    logger.info(f"📏 Crop size: {crop_x2-crop_x1}×{crop_y2-crop_y1} pixels")
    
    # Process frames with direct cropping
    processed_frames = []
    
    for frame in frames:
        # DIRECT CROP - no detection, just extract the region
        cropped = frame[crop_y1:crop_y2, crop_x1:crop_x2]
        
        # Convert to grayscale
        gray = cv2.cvtColor(cropped, cv2.COLOR_BGR2GRAY)
        
        # Resize to 96×64
        resized = cv2.resize(gray, (96, 64), interpolation=cv2.INTER_LINEAR)
        
        processed_frames.append(resized)
    
    # Sample to exactly 32 frames (uniform sampling)
    if len(processed_frames) >= 32:
        indices = np.linspace(0, len(processed_frames) - 1, 32, dtype=int)
        sampled_frames = [processed_frames[i] for i in indices]
    else:
        # Pad with repeated frames if too few
        sampled_frames = processed_frames[:]
        while len(sampled_frames) < 32:
            sampled_frames.extend(processed_frames[:32 - len(sampled_frames)])
        sampled_frames = sampled_frames[:32]
    
    logger.info(f"✅ Processed: {len(sampled_frames)} frames, 96×64 pixels, grayscale")
    
    # Create output directory
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Save as MP4 at 15 FPS
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(output_path), fourcc, 15.0, (96, 64), isColor=False)
    
    for frame in sampled_frames:
        out.write(frame)
    
    out.release()
    
    logger.info(f"💾 Saved directly cropped video: {output_path}")
    return True

def main():
    """Process 5 random my_back_hurts videos with direct geometric cropping."""

    # Input directory
    input_dir = Path("/Users/<USER>/Desktop/LRP classifier 11.9.25/speaker_sets/full_speaker_sets_top7/speaker_1 /my_back_hurts")
    output_dir = Path("/Users/<USER>/Desktop/LRP classifier 11.9.25/test clips")

    # Get all videos and select 5 random ones
    all_videos = list(input_dir.glob("*.mp4"))

    if len(all_videos) < 5:
        print(f"❌ Error: Only {len(all_videos)} videos found, need at least 5")
        return

    # Select 5 videos (using indices 0, 5, 10, 15, 20 for spread)
    selected_videos = [
        all_videos[0],   # First video
        all_videos[5],   # 6th video
        all_videos[10],  # 11th video
        all_videos[15],  # 16th video
        all_videos[20]   # 21st video
    ]

    print("🎯 DIRECT LIP CROP - 5 VIDEOS")
    print("="*50)
    print("Strategy:")
    print("• Skip ALL detection algorithms")
    print("• Use FIXED geometric coordinates")
    print("• Target exact lip region based on video format")
    print("• Direct crop → grayscale → resize → sample")
    print("• Save as MP4 at 15 FPS")
    print("="*50)

    success_count = 0

    for i, input_video in enumerate(selected_videos, 1):
        print(f"\n🎬 Processing video {i}/5: {input_video.name}")

        # Create output filename
        output_name = f"direct_crop_{i}_{input_video.stem}.mp4"
        output_video = output_dir / output_name

        # Process the video
        success = direct_lip_crop_video(input_video, output_video)

        if success:
            print(f"✅ Video {i} completed: {output_name}")
            success_count += 1
        else:
            print(f"❌ Video {i} failed")

    print(f"\n🎉 BATCH COMPLETE!")
    print(f"📊 Successfully processed: {success_count}/5 videos")
    print(f"📁 Output location: {output_dir}")
    print("📏 All videos: 32 frames, 96×64 pixels, grayscale, 15 FPS")

if __name__ == "__main__":
    main()
