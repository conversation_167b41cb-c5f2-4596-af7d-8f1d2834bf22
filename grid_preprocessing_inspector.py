#!/usr/bin/env python3
"""
GRID Preprocessing Quality Inspector
===================================

Visual inspection tool to verify GRID preprocessing quality across both speakers s1 and s4.
Creates an HTML interface showing sample frames from preprocessed videos with quality indicators.

Features:
- Random sampling from both speakers across different word classes
- Frame extraction from beginning, middle, and end of videos
- Visual quality assessment with labeled indicators
- Automatic browser launch for immediate inspection

Usage:
    python grid_preprocessing_inspector.py

Author: Augment Agent
Date: 2025-09-29
"""

import os
import sys
import cv2
import numpy as np
import random
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import base64
import webbrowser
import tempfile
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GRIDPreprocessingInspector:
    """Visual inspection tool for GRID preprocessing quality."""
    
    def __init__(self):
        self.grid_data_dir = Path("sagemaker_training_files/data/grid_subset_3spk")
        self.word_classes = [
            'at', 'bin', 'blue', 'green', 'in', 'lay', 'now', 'one', 
            'place', 'please', 'red', 'set', 'soon', 'white', 'with'
        ]
        self.target_resolution = (96, 64)  # width, height
        self.expected_frames = 32
        
    def find_available_videos(self) -> Dict[str, Dict[str, List[Path]]]:
        """Find all available videos organized by speaker and word class."""
        
        videos = {'speaker_s1': {}, 'speaker_s4': {}}
        
        for speaker in ['speaker_s1', 'speaker_s4']:
            speaker_dir = self.grid_data_dir / speaker
            
            if not speaker_dir.exists():
                logger.warning(f"Speaker directory not found: {speaker_dir}")
                continue
                
            videos[speaker] = {}
            
            for word_class in self.word_classes:
                word_dir = speaker_dir / word_class
                
                if word_dir.exists():
                    video_files = list(word_dir.glob("*.mp4"))
                    if video_files:
                        videos[speaker][word_class] = video_files
                        logger.info(f"Found {len(video_files)} videos for {speaker}/{word_class}")
        
        return videos
    
    def select_sample_videos(self, videos: Dict[str, Dict[str, List[Path]]], 
                           samples_per_speaker: int = 5) -> List[Dict]:
        """Select random sample videos from each speaker across different word classes."""
        
        selected_videos = []
        
        for speaker in ['speaker_s1', 'speaker_s4']:
            if speaker not in videos or not videos[speaker]:
                logger.warning(f"No videos found for {speaker}")
                continue
            
            # Get available word classes for this speaker
            available_classes = list(videos[speaker].keys())
            
            if len(available_classes) < samples_per_speaker:
                logger.warning(f"Only {len(available_classes)} word classes available for {speaker}")
                sample_classes = available_classes
            else:
                # Randomly select word classes
                sample_classes = random.sample(available_classes, samples_per_speaker)
            
            for word_class in sample_classes:
                # Randomly select one video from this word class
                video_files = videos[speaker][word_class]
                selected_video = random.choice(video_files)
                
                selected_videos.append({
                    'speaker': speaker,
                    'word_class': word_class,
                    'video_path': selected_video,
                    'video_name': selected_video.name
                })
                
                logger.info(f"Selected: {speaker}/{word_class}/{selected_video.name}")
        
        return selected_videos
    
    def extract_sample_frames(self, video_path: Path, num_frames: int = 4) -> List[Tuple[np.ndarray, int]]:
        """Extract sample frames from video (beginning, middle, end)."""
        
        cap = cv2.VideoCapture(str(video_path))
        
        if not cap.isOpened():
            logger.error(f"Could not open video: {video_path}")
            return []
        
        # Get video properties
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        if total_frames == 0:
            logger.error(f"Video has no frames: {video_path}")
            cap.release()
            return []
        
        # Calculate frame indices to extract
        if num_frames >= total_frames:
            frame_indices = list(range(total_frames))
        else:
            # Evenly distribute frames across video
            frame_indices = [int(i * total_frames / num_frames) for i in range(num_frames)]
        
        extracted_frames = []
        
        for frame_idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = cap.read()
            
            if ret:
                extracted_frames.append((frame, frame_idx))
            else:
                logger.warning(f"Could not read frame {frame_idx} from {video_path}")
        
        cap.release()
        
        logger.info(f"Extracted {len(extracted_frames)} frames from {video_path.name} (total: {total_frames})")
        return extracted_frames
    
    def analyze_frame_quality(self, frame: np.ndarray, video_info: Dict) -> Dict:
        """Analyze frame quality and preprocessing correctness."""
        
        height, width = frame.shape[:2]
        is_grayscale = len(frame.shape) == 2 or (len(frame.shape) == 3 and frame.shape[2] == 1)
        
        # Check resolution
        resolution_correct = (width, height) == self.target_resolution
        
        # Check if grayscale
        grayscale_correct = is_grayscale
        
        # Analyze mouth region (assume center region contains mouth)
        center_y, center_x = height // 2, width // 2
        mouth_region = frame[center_y-10:center_y+10, center_x-15:center_x+15]
        
        # Calculate contrast in mouth region (higher = better mouth visibility)
        mouth_contrast = np.std(mouth_region) if mouth_region.size > 0 else 0
        
        # Check for proper cropping (mouth should be in upper portion of frame)
        upper_region = frame[:height//2, :]
        lower_region = frame[height//2:, :]
        
        upper_activity = np.std(upper_region)
        lower_activity = np.std(lower_region)
        
        # Mouth should be more active in upper region for proper cropping
        cropping_correct = upper_activity > lower_activity * 0.8
        
        quality_analysis = {
            'resolution_correct': resolution_correct,
            'actual_resolution': (width, height),
            'grayscale_correct': grayscale_correct,
            'mouth_contrast': float(mouth_contrast),
            'cropping_correct': cropping_correct,
            'upper_activity': float(upper_activity),
            'lower_activity': float(lower_activity)
        }
        
        return quality_analysis
    
    def frame_to_base64(self, frame: np.ndarray) -> str:
        """Convert frame to base64 string for HTML embedding."""
        
        # Ensure frame is in correct format for encoding
        if len(frame.shape) == 2:
            # Grayscale
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_GRAY2RGB)
        elif frame.shape[2] == 3:
            # BGR to RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        else:
            frame_rgb = frame
        
        # Encode as JPEG
        _, buffer = cv2.imencode('.jpg', frame_rgb)
        frame_base64 = base64.b64encode(buffer).decode('utf-8')
        
        return frame_base64
    
    def generate_html_report(self, sample_data: List[Dict]) -> str:
        """Generate HTML report with visual inspection interface."""
        
        html_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GRID Preprocessing Quality Inspector</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
        }
        
        .summary {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .speaker-section {
            margin-bottom: 40px;
        }
        
        .speaker-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            padding: 10px;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            color: white;
            border-radius: 5px;
        }
        
        .video-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .video-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
        }
        
        .video-info {
            font-weight: bold;
            color: #333;
        }
        
        .quality-indicators {
            display: flex;
            gap: 10px;
        }
        
        .indicator {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .indicator.good {
            background-color: #4CAF50;
            color: white;
        }
        
        .indicator.warning {
            background-color: #FF9800;
            color: white;
        }
        
        .indicator.error {
            background-color: #f44336;
            color: white;
        }
        
        .frames-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .frame-item {
            text-align: center;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            background: #fafafa;
        }
        
        .frame-image {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            border: 1px solid #ccc;
        }
        
        .frame-info {
            margin-top: 8px;
            font-size: 12px;
            color: #666;
        }
        
        .quality-details {
            margin-top: 10px;
            font-size: 11px;
            background: #f0f0f0;
            padding: 5px;
            border-radius: 4px;
        }
        
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .stats-table th, .stats-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .stats-table th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎬 GRID Preprocessing Quality Inspector</h1>
        <p>Visual verification of 2-speaker GRID preprocessing pipeline</p>
        <p><strong>Target Resolution:</strong> 96×64 | <strong>Expected Frames:</strong> 32 | <strong>Format:</strong> Grayscale</p>
    </div>
    
    <div class="summary">
        <h2>📊 Inspection Summary</h2>
        <table class="stats-table">
            <tr>
                <th>Metric</th>
                <th>Speaker S1</th>
                <th>Speaker S4</th>
                <th>Overall</th>
            </tr>
"""
        
        # Calculate summary statistics
        s1_videos = [v for v in sample_data if v['speaker'] == 'speaker_s1']
        s4_videos = [v for v in sample_data if v['speaker'] == 'speaker_s4']
        
        html_content += f"""
            <tr>
                <td>Videos Sampled</td>
                <td>{len(s1_videos)}</td>
                <td>{len(s4_videos)}</td>
                <td>{len(sample_data)}</td>
            </tr>
            <tr>
                <td>Word Classes</td>
                <td>{len(set(v['word_class'] for v in s1_videos))}</td>
                <td>{len(set(v['word_class'] for v in s4_videos))}</td>
                <td>{len(set(v['word_class'] for v in sample_data))}</td>
            </tr>
        </table>
    </div>
"""
        
        # Group by speaker
        speakers_data = {}
        for video_data in sample_data:
            speaker = video_data['speaker']
            if speaker not in speakers_data:
                speakers_data[speaker] = []
            speakers_data[speaker].append(video_data)
        
        # Generate content for each speaker
        for speaker, videos in speakers_data.items():
            speaker_name = speaker.replace('speaker_', '').upper()
            html_content += f"""
    <div class="speaker-section">
        <div class="speaker-title">🎭 Speaker {speaker_name} ({len(videos)} videos)</div>
"""
            
            for video_data in videos:
                # Quality indicators
                quality_indicators = []
                
                if video_data.get('frames'):
                    sample_quality = video_data['frames'][0].get('quality', {})
                    
                    if sample_quality.get('resolution_correct'):
                        quality_indicators.append('<span class="indicator good">✓ Resolution</span>')
                    else:
                        quality_indicators.append('<span class="indicator error">✗ Resolution</span>')
                    
                    if sample_quality.get('grayscale_correct'):
                        quality_indicators.append('<span class="indicator good">✓ Grayscale</span>')
                    else:
                        quality_indicators.append('<span class="indicator error">✗ Grayscale</span>')
                    
                    if sample_quality.get('cropping_correct'):
                        quality_indicators.append('<span class="indicator good">✓ Cropping</span>')
                    else:
                        quality_indicators.append('<span class="indicator warning">⚠ Cropping</span>')
                    
                    mouth_contrast = sample_quality.get('mouth_contrast', 0)
                    if mouth_contrast > 20:
                        quality_indicators.append('<span class="indicator good">✓ Mouth Visible</span>')
                    else:
                        quality_indicators.append('<span class="indicator warning">⚠ Low Contrast</span>')
                
                html_content += f"""
        <div class="video-container">
            <div class="video-header">
                <div class="video-info">
                    📹 {video_data['video_name']}<br>
                    🏷️ Word: <strong>{video_data['word_class']}</strong>
                </div>
                <div class="quality-indicators">
                    {''.join(quality_indicators)}
                </div>
            </div>
            
            <div class="frames-grid">
"""
                
                # Add frames
                if video_data.get('frames'):
                    for frame_data in video_data['frames']:
                        quality = frame_data.get('quality', {})
                        
                        html_content += f"""
                <div class="frame-item">
                    <img src="data:image/jpeg;base64,{frame_data['base64']}" 
                         alt="Frame {frame_data['frame_number']}" 
                         class="frame-image">
                    <div class="frame-info">
                        Frame {frame_data['frame_number']}<br>
                        Resolution: {quality.get('actual_resolution', 'Unknown')}
                    </div>
                    <div class="quality-details">
                        Mouth Contrast: {quality.get('mouth_contrast', 0):.1f}<br>
                        Upper Activity: {quality.get('upper_activity', 0):.1f}<br>
                        Lower Activity: {quality.get('lower_activity', 0):.1f}
                    </div>
                </div>
"""
                
                html_content += """
            </div>
        </div>
"""
            
            html_content += """
    </div>
"""
        
        html_content += """
    <div class="summary">
        <h2>🔍 Quality Assessment Guidelines</h2>
        <ul>
            <li><strong>Resolution:</strong> Should be exactly 96×64 pixels</li>
            <li><strong>Grayscale:</strong> Videos should be converted to grayscale</li>
            <li><strong>Cropping:</strong> Mouth should be visible in upper portion (geometric crop: top 50% height, middle 33% width)</li>
            <li><strong>Mouth Visibility:</strong> Higher contrast values indicate better mouth region visibility</li>
            <li><strong>Frame Count:</strong> Each video should have exactly 32 frames after temporal sampling</li>
        </ul>
    </div>
</body>
</html>
"""
        
        return html_content
    
    def run_inspection(self) -> bool:
        """Run the complete inspection process."""
        
        logger.info("🔍 Starting GRID preprocessing quality inspection...")
        
        # Find available videos
        logger.info("📁 Scanning for available videos...")
        videos = self.find_available_videos()
        
        if not videos['speaker_s1'] and not videos['speaker_s4']:
            logger.error("❌ No videos found in either speaker directory")
            return False
        
        # Select sample videos
        logger.info("🎯 Selecting sample videos...")
        selected_videos = self.select_sample_videos(videos, samples_per_speaker=5)
        
        if not selected_videos:
            logger.error("❌ No videos could be selected")
            return False
        
        logger.info(f"✅ Selected {len(selected_videos)} videos for inspection")
        
        # Process each video
        sample_data = []
        
        for video_info in selected_videos:
            logger.info(f"🎬 Processing {video_info['speaker']}/{video_info['word_class']}/{video_info['video_name']}")
            
            # Extract frames
            frames = self.extract_sample_frames(video_info['video_path'], num_frames=4)
            
            if not frames:
                logger.warning(f"⚠️ No frames extracted from {video_info['video_name']}")
                continue
            
            # Process frames
            frame_data = []
            for frame, frame_idx in frames:
                # Analyze quality
                quality = self.analyze_frame_quality(frame, video_info)
                
                # Convert to base64
                frame_base64 = self.frame_to_base64(frame)
                
                frame_data.append({
                    'frame_number': frame_idx,
                    'base64': frame_base64,
                    'quality': quality
                })
            
            video_info['frames'] = frame_data
            sample_data.append(video_info)
        
        # Generate HTML report
        logger.info("📄 Generating HTML report...")
        html_content = self.generate_html_report(sample_data)
        
        # Save and open report
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
            f.write(html_content)
            html_file = f.name
        
        logger.info(f"💾 Report saved to: {html_file}")
        
        # Open in browser
        logger.info("🌐 Opening report in browser...")
        webbrowser.open(f'file://{html_file}')
        
        logger.info("🎉 Inspection complete!")
        return True

def main():
    """Main execution function."""
    
    print("🔍 GRID Preprocessing Quality Inspector")
    print("=" * 50)
    
    inspector = GRIDPreprocessingInspector()
    
    if inspector.run_inspection():
        print("✅ Visual inspection completed successfully!")
        print("📊 Check your browser for the detailed quality report")
    else:
        print("❌ Inspection failed")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
