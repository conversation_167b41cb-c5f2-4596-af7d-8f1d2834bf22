<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background: #f0f0f0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            padding: 15px 30px;
            margin: 10px;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background: #2196F3;
            color: white;
        }
        button:hover {
            background: #1976D2;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Simple Backend Connection Test</h1>
        <p>This page tests the basic connection to the lip-reading backend server.</p>
        
        <button onclick="testHealth()">Test Health Endpoint</button>
        <button onclick="testCameraEndpoint()">Test Camera Endpoint</button>
        <button onclick="testModelEndpoint()">Test Model Endpoint</button>
        
        <div id="result"></div>
    </div>

    <script>
        const API_URL = 'http://localhost:5000';
        const resultDiv = document.getElementById('result');
        
        function showResult(message, isSuccess = true) {
            resultDiv.innerHTML = `<div class="result ${isSuccess ? 'success' : 'error'}">${message}</div>`;
            console.log(message);
        }
        
        async function testHealth() {
            showResult('Testing health endpoint...', true);
            
            try {
                const response = await fetch(`${API_URL}/health`);
                const data = await response.json();
                
                if (data.status === 'healthy') {
                    showResult(`✅ Health Check Successful!
                    
Server Info: ${data.server_info}
Model Loaded: ${data.model_loaded}
Model Parameters: ${data.model_parameters?.toLocaleString()}
Classes: ${data.classes?.join(', ')}
Confidence Threshold: ${data.confidence_threshold}`, true);
                } else {
                    showResult(`❌ Health check failed: ${JSON.stringify(data)}`, false);
                }
            } catch (error) {
                showResult(`❌ Connection failed: ${error.message}
                
Make sure the backend server is running:
python demo_backend_server.py`, false);
            }
        }
        
        async function testCameraEndpoint() {
            showResult('Testing camera endpoint...', true);
            
            try {
                const response = await fetch(`${API_URL}/camera-test`);
                const data = await response.json();
                
                if (data.success) {
                    showResult(`✅ Camera Endpoint Successful!
                    
Message: ${data.message}
Server Info: ${data.server_info}
Model Loaded: ${data.model_loaded}
Classes: ${data.classes?.join(', ')}
Timestamp: ${data.timestamp}`, true);
                } else {
                    showResult(`❌ Camera endpoint failed: ${JSON.stringify(data)}`, false);
                }
            } catch (error) {
                showResult(`❌ Camera endpoint failed: ${error.message}`, false);
            }
        }
        
        async function testModelEndpoint() {
            showResult('Testing model endpoint...', true);
            
            try {
                const response = await fetch(`${API_URL}/test`);
                const data = await response.json();
                
                if (data.success) {
                    showResult(`✅ Model Test Successful!
                    
Message: ${data.message}
Test Mode: ${data.test_mode}
Top Predictions:
  1. ${data.top2[0].class}: ${data.top2[0].confidence.toFixed(1)}%
  2. ${data.top2[1].class}: ${data.top2[1].confidence.toFixed(1)}%`, true);
                } else {
                    showResult(`❌ Model test failed: ${data.error}`, false);
                }
            } catch (error) {
                showResult(`❌ Model test failed: ${error.message}`, false);
            }
        }
        
        // Auto-run health check on load
        window.addEventListener('load', () => {
            console.log('🧪 Simple test page loaded');
            console.log('API URL:', API_URL);
            testHealth();
        });
    </script>
</body>
</html>
