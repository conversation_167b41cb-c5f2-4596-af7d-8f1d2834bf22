#!/usr/bin/env python3
"""
Dataset Balancing Script for SageMaker Training
Creates exactly 20 videos per class in each speaker subfolder

Strategy:
- Subfolders with <20 videos: Apply lighting augmentation to reach 20 videos
- Subfolders with >20 videos: Randomly select 20 videos, remove excess
- Target: 6 speakers × 7 classes × 20 videos = 840 total videos
"""

import cv2
import numpy as np
import random
import shutil
import csv
from pathlib import Path
from datetime import datetime
import json

class DatasetBalancer:
    def __init__(self, source_dir, target_dir):
        self.source_dir = Path(source_dir)
        self.target_dir = Path(target_dir)
        self.target_videos_per_class = 20
        self.classes = ['doctor', 'glasses', 'i_need_to_move', 'my_back_hurts', 'my_mouth_is_dry', 'phone', 'pillow']
        self.speakers = ['speaker_1', 'speaker_2', 'speaker_3', 'speaker_4', 'speaker_5', 'speaker_6']
        
        # Augmentation parameters (lighting only)
        self.brightness_range = (-15, 15)  # ±15%
        self.contrast_range = (0.9, 1.1)   # 0.9-1.1x
        self.gamma_range = (0.95, 1.05)    # 0.95-1.05x
        
        # Processing log
        self.processing_log = []
        
        # Create target directory
        self.target_dir.mkdir(parents=True, exist_ok=True)
        
    def apply_lighting_augmentation(self, frame, aug_type):
        """Apply lighting-only augmentation to a frame"""
        if aug_type == 'brightness':
            # Random brightness adjustment
            brightness = random.randint(*self.brightness_range)
            if brightness >= 0:
                frame = cv2.add(frame, np.ones(frame.shape, dtype=np.uint8) * brightness)
            else:
                frame = cv2.subtract(frame, np.ones(frame.shape, dtype=np.uint8) * abs(brightness))
                
        elif aug_type == 'contrast':
            # Random contrast adjustment
            contrast = random.uniform(*self.contrast_range)
            frame = cv2.convertScaleAbs(frame, alpha=contrast, beta=0)
            
        elif aug_type == 'gamma':
            # Random gamma correction
            gamma = random.uniform(*self.gamma_range)
            # Build lookup table for gamma correction
            inv_gamma = 1.0 / gamma
            table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in np.arange(0, 256)]).astype("uint8")
            frame = cv2.LUT(frame, table)
            
        return frame
    
    def create_augmented_video(self, source_video_path, output_path, aug_type, aug_id):
        """Create an augmented version of a video with lighting changes"""
        cap = cv2.VideoCapture(str(source_video_path))
        
        if not cap.isOpened():
            return False
            
        # Get video properties
        fps = 15  # Target FPS
        width = 96
        height = 64
        
        # Create video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height), isColor=False)
        
        frames_processed = 0
        target_frames = 32
        
        # Read all frames first
        all_frames = []
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            all_frames.append(frame)
        
        cap.release()
        
        if len(all_frames) == 0:
            out.release()
            return False
        
        # Sample frames uniformly to get exactly 32 frames
        if len(all_frames) >= target_frames:
            # Sample uniformly
            indices = np.linspace(0, len(all_frames) - 1, target_frames, dtype=int)
            sampled_frames = [all_frames[i] for i in indices]
        else:
            # Repeat frames if needed
            sampled_frames = []
            for i in range(target_frames):
                frame_idx = i % len(all_frames)
                sampled_frames.append(all_frames[frame_idx])
        
        # Process and write frames with augmentation
        for frame in sampled_frames:
            # Convert to grayscale if needed
            if len(frame.shape) == 3:
                gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            else:
                gray_frame = frame.copy()
            
            # Apply augmentation
            augmented_frame = self.apply_lighting_augmentation(gray_frame, aug_type)
            
            # Resize to target resolution
            resized_frame = cv2.resize(augmented_frame, (width, height))
            
            out.write(resized_frame)
            frames_processed += 1
        
        out.release()
        
        return frames_processed == target_frames
    
    def balance_subfolder(self, speaker, class_name):
        """Balance a specific speaker/class subfolder to exactly 20 videos"""
        source_subfolder = self.source_dir / speaker / class_name
        target_subfolder = self.target_dir / speaker / class_name
        
        # Create target subfolder
        target_subfolder.mkdir(parents=True, exist_ok=True)
        
        # Get all videos in source subfolder
        source_videos = list(source_subfolder.glob('*.mp4'))
        current_count = len(source_videos)
        
        log_entry = {
            'speaker': speaker,
            'class': class_name,
            'original_count': current_count,
            'target_count': self.target_videos_per_class,
            'action': '',
            'videos_copied': 0,
            'videos_augmented': 0,
            'videos_removed': 0,
            'augmentation_types': []
        }
        
        if current_count == self.target_videos_per_class:
            # Perfect count - just copy all videos
            log_entry['action'] = 'copy_all'
            for video in source_videos:
                target_path = target_subfolder / video.name
                shutil.copy2(video, target_path)
                log_entry['videos_copied'] += 1
                
        elif current_count < self.target_videos_per_class:
            # Need augmentation
            log_entry['action'] = 'augment'
            
            # Copy all original videos first
            for video in source_videos:
                target_path = target_subfolder / video.name
                shutil.copy2(video, target_path)
                log_entry['videos_copied'] += 1
            
            # Calculate how many augmented videos we need
            videos_needed = self.target_videos_per_class - current_count
            
            # Create augmented videos
            augmentation_types = ['brightness', 'contrast', 'gamma']
            
            for i in range(videos_needed):
                # Select random source video and augmentation type
                source_video = random.choice(source_videos)
                aug_type = random.choice(augmentation_types)
                
                # Create augmented filename
                base_name = source_video.stem
                aug_filename = f"{base_name}_aug_{aug_type}_{i+1}.mp4"
                target_path = target_subfolder / aug_filename
                
                # Create augmented video
                success = self.create_augmented_video(source_video, target_path, aug_type, i+1)
                
                if success:
                    log_entry['videos_augmented'] += 1
                    log_entry['augmentation_types'].append(aug_type)
                else:
                    print(f"⚠️  Failed to create augmented video: {aug_filename}")
                    
        else:
            # Too many videos - randomly select 20
            log_entry['action'] = 'reduce'
            log_entry['videos_removed'] = current_count - self.target_videos_per_class
            
            # Randomly select 20 videos
            selected_videos = random.sample(source_videos, self.target_videos_per_class)
            
            for video in selected_videos:
                target_path = target_subfolder / video.name
                shutil.copy2(video, target_path)
                log_entry['videos_copied'] += 1
        
        self.processing_log.append(log_entry)
        
        # Verify final count
        final_videos = list(target_subfolder.glob('*.mp4'))
        final_count = len(final_videos)
        
        status = "✅" if final_count == self.target_videos_per_class else "❌"
        print(f"{status} {speaker}/{class_name}: {current_count} → {final_count} videos ({log_entry['action']})")
        
        return final_count == self.target_videos_per_class

    def balance_entire_dataset(self):
        """Balance the entire dataset to exactly 20 videos per class per speaker"""
        print("🎯 Starting Dataset Balancing for SageMaker Training")
        print(f"📁 Source: {self.source_dir}")
        print(f"📁 Target: {self.target_dir}")
        print(f"🎬 Target: {len(self.speakers)} speakers × {len(self.classes)} classes × {self.target_videos_per_class} videos = {len(self.speakers) * len(self.classes) * self.target_videos_per_class} total videos")
        print()

        total_success = 0
        total_subfolders = len(self.speakers) * len(self.classes)

        # Set random seed for reproducibility
        random.seed(42)

        # Process each speaker/class combination
        for speaker in self.speakers:
            print(f"📁 Processing {speaker}...")

            for class_name in self.classes:
                success = self.balance_subfolder(speaker, class_name)
                if success:
                    total_success += 1

        print()
        print("🎯 DATASET BALANCING COMPLETE!")
        print(f"✅ Successfully balanced: {total_success}/{total_subfolders} subfolders")

        # Generate summary report
        self.generate_summary_report()

        return total_success == total_subfolders

    def generate_summary_report(self):
        """Generate detailed summary report and manifest"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save detailed log as JSON
        log_file = self.target_dir / f"balancing_log_{timestamp}.json"
        with open(log_file, 'w') as f:
            json.dump(self.processing_log, f, indent=2)

        # Create CSV manifest
        manifest_file = self.target_dir / f"dataset_manifest_{timestamp}.csv"
        with open(manifest_file, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['Speaker', 'Class', 'Original_Count', 'Final_Count', 'Action', 'Videos_Copied', 'Videos_Augmented', 'Videos_Removed', 'Augmentation_Types'])

            for entry in self.processing_log:
                writer.writerow([
                    entry['speaker'],
                    entry['class'],
                    entry['original_count'],
                    entry['target_count'],
                    entry['action'],
                    entry['videos_copied'],
                    entry['videos_augmented'],
                    entry['videos_removed'],
                    ', '.join(entry['augmentation_types'])
                ])

        # Generate summary statistics
        total_original = sum(entry['original_count'] for entry in self.processing_log)
        total_final = sum(entry['target_count'] for entry in self.processing_log)
        total_copied = sum(entry['videos_copied'] for entry in self.processing_log)
        total_augmented = sum(entry['videos_augmented'] for entry in self.processing_log)
        total_removed = sum(entry['videos_removed'] for entry in self.processing_log)

        actions_summary = {}
        for entry in self.processing_log:
            action = entry['action']
            actions_summary[action] = actions_summary.get(action, 0) + 1

        # Create summary report
        summary_file = self.target_dir / f"balancing_summary_{timestamp}.txt"
        with open(summary_file, 'w') as f:
            f.write("DATASET BALANCING SUMMARY REPORT\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Source Directory: {self.source_dir}\n")
            f.write(f"Target Directory: {self.target_dir}\n\n")

            f.write("OVERALL STATISTICS:\n")
            f.write(f"  Total Original Videos: {total_original}\n")
            f.write(f"  Total Final Videos: {total_final}\n")
            f.write(f"  Videos Copied: {total_copied}\n")
            f.write(f"  Videos Augmented: {total_augmented}\n")
            f.write(f"  Videos Removed: {total_removed}\n\n")

            f.write("ACTIONS TAKEN:\n")
            for action, count in actions_summary.items():
                f.write(f"  {action}: {count} subfolders\n")
            f.write("\n")

            f.write("AUGMENTATION PARAMETERS:\n")
            f.write(f"  Brightness Range: {self.brightness_range[0]}% to {self.brightness_range[1]}%\n")
            f.write(f"  Contrast Range: {self.contrast_range[0]}x to {self.contrast_range[1]}x\n")
            f.write(f"  Gamma Range: {self.gamma_range[0]} to {self.gamma_range[1]}\n\n")

            f.write("TARGET SPECIFICATIONS:\n")
            f.write(f"  Videos per Class per Speaker: {self.target_videos_per_class}\n")
            f.write(f"  Total Speakers: {len(self.speakers)}\n")
            f.write(f"  Total Classes: {len(self.classes)}\n")
            f.write(f"  Expected Total Videos: {len(self.speakers) * len(self.classes) * self.target_videos_per_class}\n")
            f.write(f"  Video Format: 32 frames, 96×64 pixels, grayscale, 15 FPS, MP4\n")

        print(f"📊 Reports generated:")
        print(f"  📋 Detailed log: {log_file.name}")
        print(f"  📊 CSV manifest: {manifest_file.name}")
        print(f"  📄 Summary report: {summary_file.name}")


def main():
    """Main execution function"""
    source_dir = "/Users/<USER>/Desktop/LRP classifier 11.9.25/speaker_sets/full_speaker_sets_top7_8pm"
    target_dir = "/Users/<USER>/Desktop/LRP classifier 11.9.25/speaker_sets/sagemaker_ready_classifier_speaker_sets"

    print("🎯 DATASET BALANCING FOR SAGEMAKER TRAINING")
    print("=" * 60)

    # Create balancer instance
    balancer = DatasetBalancer(source_dir, target_dir)

    # Execute balancing
    success = balancer.balance_entire_dataset()

    if success:
        print("\n🎉 SUCCESS! Dataset balanced and ready for SageMaker training!")
        print(f"📁 Balanced dataset location: {target_dir}")
        print(f"🎬 Total videos: {len(balancer.speakers) * len(balancer.classes) * balancer.target_videos_per_class}")
    else:
        print("\n❌ Some subfolders failed to balance correctly. Check the logs for details.")

    return success


if __name__ == "__main__":
    main()
