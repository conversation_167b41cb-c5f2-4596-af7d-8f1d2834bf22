#!/usr/bin/env python3
"""
Create 2-speaker GRID pretraining configuration.
"""

import yaml
import csv
from pathlib import Path
from collections import Counter

def create_2speaker_config():
    """Create 2-speaker GRID pretraining configuration."""
    
    base_config_path = Path("sagemaker_training_files/configs/grid_pretrain_s4.yaml")
    
    if not base_config_path.exists():
        print(f"❌ Base GRID config not found: {base_config_path}")
        return False
    
    try:
        with open(base_config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Check manifest for actual data
        manifest_path = Path("sagemaker_training_files/data/grid_manifest.csv")
        speaker_counts = Counter()
        word_counts = Counter()
        
        with open(manifest_path, 'r') as f:
            reader = csv.DictReader(f)
            for row in reader:
                speaker_counts[row['speaker_id']] += 1
                word_counts[row['word']] += 1
        
        print(f"📊 Manifest analysis:")
        print(f"Total entries: {sum(speaker_counts.values())}")
        print(f"Speakers: {dict(speaker_counts)}")
        print(f"Words per class: {dict(word_counts)}")
        
        # Update for 2-speaker training
        config['data_root'] = 'data/grid_subset_3spk'
        config['labels'] = sorted(list(word_counts.keys()))  # Use actual words found
        config['num_classes'] = len(word_counts)
        
        # Adjust parameters for 2-speaker training
        config['batch_size'] = 16      # Increased from 8 (more data available)
        config['epochs'] = 40          # Increased from 30 (more complex learning)
        config['val_split'] = 0.25     # Adjusted from 0.3 (maintain good validation size)
        config['early_stop_patience'] = 15  # Increased from 10 (more patience for complex learning)
        
        # Update output directories
        config['output_dir'] = 'checkpoints/grid_pretrain_s1s4'
        config['tensorboard_dir'] = 'runs/grid_pretrain_s1s4'
        
        # Save 2-speaker config
        two_speaker_config = Path("sagemaker_training_files/configs/grid_pretrain_s1s4.yaml")
        with open(two_speaker_config, 'w') as f:
            yaml.dump(config, f, default_flow_style=False)
        
        print(f"✅ Created 2-speaker config: {two_speaker_config}")
        print(f"Classes ({len(word_counts)}): {sorted(list(word_counts.keys()))}")
        print(f"Batch size: {config['batch_size']}, Epochs: {config['epochs']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating 2-speaker config: {e}")
        return False

def main():
    print("⚙️  Creating 2-speaker GRID training config...")
    
    if create_2speaker_config():
        print("🎉 2-speaker config ready!")
    else:
        print("❌ Failed to create 2-speaker config")
        return False
    
    return True

if __name__ == "__main__":
    main()
