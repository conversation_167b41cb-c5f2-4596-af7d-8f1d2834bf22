#!/usr/bin/env python3
"""
Debug script to examine what's happening with video cropping
"""

import cv2
import numpy as np
from pathlib import Path

def debug_video_cropping():
    """Debug the cropping process step by step"""
    
    # Test with one of the 400x200 videos that should have been cropped
    test_video = "test clips/test_02_my_back_hurts__useruser01__40to64__female__caucasian__20250716T055041.mp4"
    orig_video = "speaker_sets/partial_speaker_sets_top7/speaker 9 volunteer lady green shirt 6 done/my_back_hurts/my_back_hurts__useruser01__40to64__female__caucasian__20250716T055041.mp4"
    
    print("🔍 DEBUGGING VIDEO CROPPING")
    print("=" * 50)
    
    # Check if files exist
    if not Path(test_video).exists():
        print(f"❌ Test video not found: {test_video}")
        return
    if not Path(orig_video).exists():
        print(f"❌ Original video not found: {orig_video}")
        return
    
    print("✅ Both videos found")
    print()
    
    # Examine original video
    print("📹 ORIGINAL VIDEO:")
    cap_orig = cv2.VideoCapture(orig_video)
    ret_orig, frame_orig = cap_orig.read()
    
    if ret_orig:
        h_orig, w_orig = frame_orig.shape[:2]
        print(f"  Dimensions: {w_orig}×{h_orig}")
        print(f"  Shape: {frame_orig.shape}")
        print(f"  Data type: {frame_orig.dtype}")
        
        # Save original frame for inspection
        cv2.imwrite("debug_original.png", frame_orig)
        print("  💾 Saved as debug_original.png")
        
        # Test manual cropping with the coordinates that should have been used
        crop_x1 = int(w_orig * 0.20)  # 20% from left
        crop_x2 = int(w_orig * 0.80)  # 80% from left  
        crop_y1 = int(h_orig * 0.30)  # 30% from top
        crop_y2 = int(h_orig * 0.70)  # 70% from top
        
        print(f"  Expected crop coordinates: x={crop_x1}-{crop_x2}, y={crop_y1}-{crop_y2}")
        print(f"  Expected crop size: {crop_x2-crop_x1}×{crop_y2-crop_y1}")
        
        # Apply manual crop
        manual_crop = frame_orig[crop_y1:crop_y2, crop_x1:crop_x2]
        print(f"  Manual crop shape: {manual_crop.shape}")
        
        # Convert to grayscale and resize like the script should do
        manual_gray = cv2.cvtColor(manual_crop, cv2.COLOR_BGR2GRAY)
        manual_resized = cv2.resize(manual_gray, (96, 64), interpolation=cv2.INTER_LINEAR)
        
        print(f"  Manual processed shape: {manual_resized.shape}")
        cv2.imwrite("debug_manual_crop.png", manual_crop)
        cv2.imwrite("debug_manual_processed.png", manual_resized)
        print("  💾 Saved manual crop as debug_manual_crop.png")
        print("  💾 Saved manual processed as debug_manual_processed.png")
        
    else:
        print("  ❌ Could not read original video")
    
    cap_orig.release()
    print()
    
    # Examine processed video
    print("📹 PROCESSED VIDEO:")
    cap_test = cv2.VideoCapture(test_video)
    ret_test, frame_test = cap_test.read()
    
    if ret_test:
        print(f"  Dimensions: {frame_test.shape[1]}×{frame_test.shape[0]}")
        print(f"  Shape: {frame_test.shape}")
        print(f"  Data type: {frame_test.dtype}")
        print(f"  Min/Max values: {frame_test.min()}/{frame_test.max()}")
        print(f"  Is grayscale: {len(frame_test.shape) == 2}")
        
        # Save processed frame for inspection
        cv2.imwrite("debug_processed.png", frame_test)
        print("  💾 Saved as debug_processed.png")
        
        # Compare with expected result
        if ret_orig:
            print()
            print("🔍 COMPARISON:")
            if frame_test.shape == (64, 96):
                print("  ✅ Output dimensions are correct (64×96)")
            else:
                print(f"  ❌ Output dimensions wrong: expected (64, 96), got {frame_test.shape}")
            
            if len(frame_test.shape) == 2:
                print("  ✅ Output is grayscale")
            else:
                print("  ❌ Output is not grayscale")
                
    else:
        print("  ❌ Could not read processed video")
    
    cap_test.release()
    
    print()
    print("🎯 ANALYSIS COMPLETE!")
    print("Check the saved PNG files to see what actually happened:")
    print("  - debug_original.png: Original frame")
    print("  - debug_manual_crop.png: What the crop should look like")
    print("  - debug_manual_processed.png: What the final result should be")
    print("  - debug_processed.png: What the script actually produced")

if __name__ == "__main__":
    debug_video_cropping()
