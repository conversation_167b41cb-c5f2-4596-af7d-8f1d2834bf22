"""
SageMaker Lip-Reading Training Pipeline
=======================================

Production-ready implementation of the fast-track methodology for achieving
≥82% LOSO validation accuracy on ICU lip-reading tasks.

This package provides:
- Two-stage training pipeline (GRID pretraining → ICU fine-tuning)
- Complete SageMaker integration
- Missing class handling
- Model export for production deployment
- Jupyter notebook workflows

Author: Augment Agent
Date: 2025-09-27
Version: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "Augment Agent"
__description__ = "SageMaker Lip-Reading Training Pipeline"

# Import key components
try:
    from .models import create_model, EnhancedLightweightCNNLSTM
except ImportError:
    create_model = None
    EnhancedLightweightCNNLSTM = None

try:
    from .training import train_icu_main
except ImportError:
    train_icu_main = None

try:
    from .eval import evaluate_tta_main, DualTrackEvaluator
except ImportError:
    evaluate_tta_main = None
    DualTrackEvaluator = None

try:
    from .deploy import export_torchscript_main, export_onnx_main
except ImportError:
    export_torchscript_main = None
    export_onnx_main = None

__all__ = [
    'create_model',
    'EnhancedLightweightCNNLSTM',
    'train_icu_main',
    'evaluate_tta_main',
    'DualTrackEvaluator',
    'export_torchscript_main',
    'export_onnx_main'
]
