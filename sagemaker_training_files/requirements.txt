# SageMaker Lip-Reading Training Dependencies
# Compatible with SageMaker PyTorch containers and GPU training

# Core ML Framework
torch>=2.2.0
torchvision>=0.17.0
torchaudio>=2.2.0

# Computer Vision
opencv-python-headless>=4.9.0
mediapipe>=0.10.0

# Scientific Computing
numpy>=1.24.0
scipy>=1.11.0

# Data Processing
pandas>=2.2.0
scikit-learn>=1.4.0

# Image/Video Processing
albumentations>=1.4.0
imageio>=2.34.0
imageio-ffmpeg>=0.4.9

# Progress and Logging
tqdm>=4.66.0
tensorboard>=2.15.0

# Visualization
matplotlib>=3.8.0
seaborn>=0.13.0
plotly>=5.17.0

# Configuration
PyYAML>=6.0.0
omegaconf>=2.3.0

# Utilities
pathlib2>=2.3.7
psutil>=5.9.0

# Model Export
onnx>=1.15.0
onnxruntime>=1.16.0

# SageMaker Integration
sagemaker>=2.200.0
boto3>=1.34.0

# Development Tools (optional)
jupyter>=1.0.0
ipywidgets>=8.1.0
notebook>=7.0.0

# Fast Data Loading
lmdb>=1.4.1
h5py>=3.10.0

# Memory Optimization
memory-profiler>=0.61.0
py3nvml>=0.2.7

# Distributed Training Support
accelerate>=0.25.0

# Audio Processing (if needed for multimodal)
librosa>=0.10.0

# Additional Utilities
colorama>=0.4.6
rich>=13.7.0
typer>=0.9.0
