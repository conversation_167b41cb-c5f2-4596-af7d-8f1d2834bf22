#!/usr/bin/env python3
"""
SageMaker Training Entry Point
==============================

Main entry point for SageMaker training jobs.
Supports both GRID pretraining and ICU LOSO fine-tuning.

Usage:
    # GRID Pretraining
    python sm_entrypoint.py --mode grid_pretrain --config configs/grid_pretrain.yaml
    
    # ICU LOSO Fine-tuning
    python sm_entrypoint.py --mode icu_finetune --config configs/train_icufinetune_loso.yaml --grid-encoder /path/to/encoder.pt

Author: Augment Agent
Date: 2025-09-27
"""

import os
import sys
import argparse
import logging
import json
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_sagemaker_paths():
    """Setup paths for SageMaker environment."""
    # SageMaker standard paths
    sm_model_dir = os.environ.get('SM_MODEL_DIR', '/opt/ml/model')
    sm_output_dir = os.environ.get('SM_OUTPUT_DATA_DIR', '/opt/ml/output')
    sm_channel_training = os.environ.get('SM_CHANNEL_TRAINING', '/opt/ml/input/data/training')
    
    # Create directories if they don't exist
    Path(sm_model_dir).mkdir(parents=True, exist_ok=True)
    Path(sm_output_dir).mkdir(parents=True, exist_ok=True)
    
    logger.info(f"SageMaker Model Dir: {sm_model_dir}")
    logger.info(f"SageMaker Output Dir: {sm_output_dir}")
    logger.info(f"SageMaker Training Data: {sm_channel_training}")
    
    return sm_model_dir, sm_output_dir, sm_channel_training

def run_grid_pretraining(config_path, model_dir, output_dir):
    """Run GRID corpus pretraining."""
    logger.info("Starting GRID Pretraining...")
    
    # Import and run GRID pretraining
    from training.train_grid_pretrain import main as grid_main
    
    # Modify sys.argv for the training script
    original_argv = sys.argv.copy()
    sys.argv = [
        'train_grid_pretrain.py',
        '--config', config_path,
        '--device', 'auto'
    ]
    
    try:
        grid_main()
        
        # Copy results to SageMaker model directory
        checkpoint_dir = Path('checkpoints/grid_pretrain')
        if checkpoint_dir.exists():
            import shutil
            shutil.copytree(checkpoint_dir, Path(model_dir) / 'grid_pretrain', dirs_exist_ok=True)
            logger.info(f"GRID pretraining results copied to {model_dir}")
        
        return True
    except Exception as e:
        logger.error(f"GRID pretraining failed: {e}")
        return False
    finally:
        sys.argv = original_argv

def run_icu_finetuning(config_path, grid_encoder_path, model_dir, output_dir):
    """Run ICU LOSO fine-tuning."""
    logger.info("Starting ICU LOSO Fine-tuning...")
    
    # Import and run ICU fine-tuning
    from training.finetune_icu_from_grid import main as icu_main
    
    # Modify sys.argv for the training script
    original_argv = sys.argv.copy()
    sys.argv = [
        'finetune_icu_from_grid.py',
        '--grid-encoder', grid_encoder_path,
        '--config', config_path,
        '--device', 'auto'
    ]
    
    try:
        icu_main()
        
        # Copy results to SageMaker model directory
        checkpoint_dir = Path('checkpoints/icu_loso')
        if checkpoint_dir.exists():
            import shutil
            shutil.copytree(checkpoint_dir, Path(model_dir) / 'icu_loso', dirs_exist_ok=True)
            logger.info(f"ICU fine-tuning results copied to {model_dir}")
        
        return True
    except Exception as e:
        logger.error(f"ICU fine-tuning failed: {e}")
        return False
    finally:
        sys.argv = original_argv

def run_full_pipeline(grid_config, icu_config, model_dir, output_dir):
    """Run complete two-stage pipeline."""
    logger.info("Starting Full Two-Stage Pipeline...")
    
    # Stage 1: GRID Pretraining
    success = run_grid_pretraining(grid_config, model_dir, output_dir)
    if not success:
        logger.error("GRID pretraining failed, aborting pipeline")
        return False
    
    # Find encoder from GRID pretraining
    encoder_path = Path(model_dir) / 'grid_pretrain' / 'encoder.pt'
    if not encoder_path.exists():
        # Try local checkpoint directory
        encoder_path = Path('checkpoints/grid_pretrain/encoder.pt')
    
    if not encoder_path.exists():
        logger.error("GRID encoder not found, cannot proceed to ICU fine-tuning")
        return False
    
    # Stage 2: ICU Fine-tuning
    success = run_icu_finetuning(icu_config, str(encoder_path), model_dir, output_dir)
    if not success:
        logger.error("ICU fine-tuning failed")
        return False
    
    logger.info("Full pipeline completed successfully!")
    return True

def main():
    parser = argparse.ArgumentParser(description='SageMaker Training Entry Point')
    parser.add_argument('--mode', required=True, 
                       choices=['grid_pretrain', 'icu_finetune', 'full_pipeline'],
                       help='Training mode')
    parser.add_argument('--config', help='Path to config file')
    parser.add_argument('--grid-config', help='GRID pretraining config (for full pipeline)')
    parser.add_argument('--icu-config', help='ICU fine-tuning config (for full pipeline)')
    parser.add_argument('--grid-encoder', help='Path to GRID encoder (for ICU fine-tuning)')
    args = parser.parse_args()
    
    # Setup SageMaker environment
    model_dir, output_dir, training_data = setup_sagemaker_paths()
    
    # Log environment info
    logger.info(f"Training mode: {args.mode}")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Working directory: {os.getcwd()}")
    
    # Check GPU availability
    try:
        import torch
        logger.info(f"PyTorch version: {torch.__version__}")
        logger.info(f"CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            logger.info(f"CUDA device: {torch.cuda.get_device_name()}")
    except ImportError:
        logger.warning("PyTorch not available")
    
    # Run training based on mode
    success = False
    
    if args.mode == 'grid_pretrain':
        if not args.config:
            logger.error("--config required for GRID pretraining")
            sys.exit(1)
        success = run_grid_pretraining(args.config, model_dir, output_dir)
    
    elif args.mode == 'icu_finetune':
        if not args.config or not args.grid_encoder:
            logger.error("--config and --grid-encoder required for ICU fine-tuning")
            sys.exit(1)
        success = run_icu_finetuning(args.config, args.grid_encoder, model_dir, output_dir)
    
    elif args.mode == 'full_pipeline':
        if not args.grid_config or not args.icu_config:
            logger.error("--grid-config and --icu-config required for full pipeline")
            sys.exit(1)
        success = run_full_pipeline(args.grid_config, args.icu_config, model_dir, output_dir)
    
    # Create training summary
    summary = {
        'mode': args.mode,
        'success': success,
        'model_dir': model_dir,
        'output_dir': output_dir
    }
    
    # Save summary
    with open(Path(output_dir) / 'training_summary.json', 'w') as f:
        json.dump(summary, f, indent=2)
    
    if success:
        logger.info("Training completed successfully!")
        sys.exit(0)
    else:
        logger.error("Training failed!")
        sys.exit(1)

if __name__ == '__main__':
    main()
