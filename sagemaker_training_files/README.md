# SageMaker Lip-Reading Training Pipeline
**Production-Ready Fast-Track Implementation for GPU Training**

This directory contains a complete SageMaker-compatible implementation of our proven fast-track methodology for achieving ≥82% LOSO validation accuracy on ICU lip-reading tasks.

## 🎯 **Training Strategy**

### **Two-Stage Pipeline:**
1. **GRID 3-Speaker Pretraining**: Encoder-only pretraining using viseme-matched vocabulary
2. **ICU LOSO Fine-tuning**: Full model fine-tuning targeting ≥82% validation accuracy

### **Key Features:**
- **Preserves Current Preprocessing**: Zero modifications to existing preprocessing pipeline
- **Missing Class Handling**: Graceful degradation for speakers with incomplete class sets
- **Fast-Track Optimizations**: CosineFCHead, ArcFace loss, MixUp augmentation, progressive unfreezing
- **SageMaker Compatible**: GPU-optimized with proper dependency management

---

## 📁 **Directory Structure**

```
sagemaker_training_files/
├── README.md                           # This file
├── requirements.txt                    # SageMaker dependencies
├── .gitignore                         # Exclude checkpoints/data
├── sm_entrypoint.py                   # SageMaker training entry point
├── configs/                           # Training configurations
│   ├── grid_pretrain.yaml             # GRID pretraining config
│   ├── train_icufinetune_loso.yaml    # ICU LOSO fine-tuning config
│   ├── grid_proxy_map.json            # GRID→ICU class mapping
│   ├── viseme_map.json                # Viseme similarity mappings
│   └── label2idx.json                 # Global class index mapping
├── models/                            # Model architectures (copied from current)
│   ├── backbones/                     # CNN-LSTM backbone
│   └── heads/                         # Classification heads
├── training/                          # Training scripts
│   ├── train_icu_finetune_fixed.py    # Current training script (copied)
│   ├── train_grid_pretrain.py         # GRID pretraining wrapper
│   ├── finetune_icu_from_grid.py      # ICU fine-tuning from GRID
│   └── advanced_training_components.py # MixUp, ArcFace, EMA components
├── eval/                              # Evaluation scripts
│   ├── evaluate_with_tta.py           # Test-time augmentation eval
│   └── dual_track_evaluation.py       # Dual-track evaluation
├── tools/                             # Preprocessing tools (copied byte-for-byte)
├── utils/                             # Utilities (copied byte-for-byte)
├── notebooks/                         # Jupyter workflows
│   ├── 01_grid_pretrain_3spk.ipynb    # GRID pretraining workflow
│   └── 02_icu_loso_finetune.ipynb     # ICU LOSO fine-tuning workflow
├── deploy/                            # Model export
│   ├── export_torchscript.py          # TorchScript export
│   └── export_onnx.py                 # ONNX export
└── data/                              # Data directories (mount points)
    ├── grid_raw/                      # GRID corpus mount
    ├── grid_subset_3spk/              # Selected GRID subset
    ├── stabilized_speaker_sets/       # ICU preprocessing output
    └── stabilized_subclips/           # Subclip preprocessing output
```

---

## 🚀 **Quick Start**

### **1. Environment Setup**
```bash
# Install dependencies
pip install -r requirements.txt

# Verify GPU availability
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
```

### **2. Data Preparation**
```bash
# Mount/copy GRID corpus to data/grid_raw/
# Mount/copy ICU preprocessed data to data/stabilized_speaker_sets/

# Select GRID subset (3 speakers, viseme-matched)
python tools/select_grid_subset.py \
  --grid-root data/grid_raw \
  --output-dir data/grid_subset_3spk \
  --proxy-map configs/grid_proxy_map.json \
  --speakers 3 \
  --max-videos-per-speaker 50

# Apply current preprocessing to GRID subset (use existing scripts exactly)
# Apply current preprocessing to ICU data (use existing scripts exactly)
```

### **3. Training Pipeline**
```bash
# Stage 1: GRID Pretraining (encoder only)
python training/train_grid_pretrain.py --config configs/grid_pretrain.yaml

# Stage 2: ICU LOSO Fine-tuning (from pretrained encoder)
python training/finetune_icu_from_grid.py \
  --grid-encoder checkpoints/grid_pretrain/encoder.pt \
  --config configs/train_icufinetune_loso.yaml
```

### **4. Evaluation**
```bash
# Test-time augmentation evaluation
python eval/evaluate_with_tta.py \
  --checkpoint checkpoints/icu_loso/fold*/best.pt \
  --tta-crops 7
```

---

## ⚙️ **Configuration**

### **Key Configuration Files:**

**`configs/label2idx.json`** - Global class mapping (7 classes):
```json
{
  "doctor": 0, "glasses": 1, "i_need_to_move": 2,
  "my_back_hurts": 3, "my_mouth_is_dry": 4, "phone": 5, "pillow": 6
}
```

**`configs/grid_pretrain.yaml`** - GRID pretraining parameters:
- 3 speakers, viseme-matched vocabulary
- CosineFCHead + ArcFace loss
- Progressive unfreezing (freeze encoder 3 epochs)
- 25 epochs, early stopping patience 7

**`configs/train_icufinetune_loso.yaml`** - ICU LOSO fine-tuning:
- LOSO cross-validation across ~12 speakers
- MixUp augmentation (alpha=0.2)
- Missing class handling enabled
- Target: ≥82% validation accuracy

---

## 🔧 **Missing Class Handling**

The system gracefully handles speakers with missing classes:

- **Training**: Issues warnings but maintains fixed output dimensions
- **Validation**: Marks per-class metrics as "N/A", computes macro-average over present classes
- **Cross-fold Comparison**: Uses "macro_over_global" for consistent comparison
- **No Preprocessing Changes**: All handling done in training/evaluation code only

---

## 📊 **Expected Results**

### **Performance Targets:**
- **GRID Pretraining**: Encoder learns viseme-matched representations
- **ICU LOSO**: ≥82% mean validation accuracy across folds
- **Generalization**: Robust cross-speaker performance
- **Production**: TorchScript/ONNX models for deployment

### **Training Time (GPU):**
- **GRID Pretraining**: ~2-3 hours (3 speakers, 25 epochs)
- **ICU LOSO**: ~8-12 hours (12 folds, 100 epochs each with early stopping)
- **Total Pipeline**: ~10-15 hours end-to-end

---

## 🛡️ **Critical Constraints**

### **Preprocessing Preservation:**
- **Zero modifications** to any preprocessing scripts in `tools/`
- **Exact preservation** of `utils/id_norm.py` and all relative imports
- **Identical behavior** for all current preprocessing CLI flags
- **No parameter overrides** for frame size, ROI dimensions, temporal length

### **Data Flow:**
1. Raw videos → Current preprocessing scripts → Preprocessed tensors
2. Preprocessed tensors → Training/evaluation code → Results
3. **No modifications** to step 1, all improvements in step 2

---

## 📝 **Usage Notes**

### **SageMaker Integration:**
- Use `sm_entrypoint.py` as the SageMaker training script entry point
- Mount data to appropriate directories before training
- Configure GPU instance types (p3.2xlarge or higher recommended)

### **Jupyter Workflows:**
- `01_grid_pretrain_3spk.ipynb`: Complete GRID pretraining workflow
- `02_icu_loso_finetune.ipynb`: Complete ICU LOSO fine-tuning workflow
- Both notebooks include data preparation, training, and evaluation steps

### **Production Deployment:**
- Use `deploy/export_torchscript.py` for TorchScript export
- Use `deploy/export_onnx.py` for ONNX export
- Ship `configs/label2idx.json` with exported models

---

## 🎯 **Success Validation**

### **Mandatory Checks:**
- [ ] All preprocessing tools work exactly as in current project
- [ ] LOSO cross-validation handles missing classes gracefully
- [ ] Training achieves ≥82% validation accuracy target
- [ ] Models export successfully for production deployment
- [ ] All current relative import paths preserved

### **Performance Validation:**
- [ ] GRID pretraining converges without overfitting
- [ ] ICU fine-tuning shows consistent improvement across folds
- [ ] Test-time augmentation provides additional accuracy boost
- [ ] Final models generalize well to unseen speakers

This implementation provides a production-ready path to achieving the ≥82% LOSO accuracy target while preserving all current preprocessing exactly as-is.
