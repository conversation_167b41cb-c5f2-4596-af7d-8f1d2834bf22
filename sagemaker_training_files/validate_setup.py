#!/usr/bin/env python3
"""
SageMaker Setup Validation Script
=================================

Validates that the SageMaker training files directory is properly configured
and all components are working correctly.

Usage:
    python validate_setup.py

Author: Augment Agent
Date: 2025-09-27
"""

import os
import sys
import json
import yaml
from pathlib import Path
import importlib.util

def check_file_exists(path, description):
    """Check if a file exists and report status."""
    exists = Path(path).exists()
    status = "✅" if exists else "❌"
    print(f"  {status} {description}: {path}")
    return exists

def check_directory_structure():
    """Check the directory structure."""
    print("🏗️  Checking Directory Structure...")
    
    required_dirs = [
        "configs",
        "models/backbones",
        "models/heads", 
        "training",
        "eval",
        "tools",
        "utils",
        "notebooks",
        "deploy",
        "data/grid_raw",
        "data/grid_subset_3spk",
        "data/stabilized_speaker_sets",
        "data/stabilized_subclips"
    ]
    
    all_exist = True
    for dir_path in required_dirs:
        exists = check_file_exists(dir_path, f"Directory {dir_path}")
        all_exist = all_exist and exists
    
    return all_exist

def check_configuration_files():
    """Check configuration files."""
    print("\n⚙️  Checking Configuration Files...")
    
    config_files = [
        ("configs/label2idx.json", "Global label mapping"),
        ("configs/grid_pretrain.yaml", "GRID pretraining config"),
        ("configs/train_icufinetune_loso.yaml", "ICU fine-tuning config"),
        ("configs/grid_proxy_map.json", "GRID proxy mapping"),
        ("configs/viseme_map.json", "Viseme similarity mapping")
    ]
    
    all_exist = True
    for file_path, description in config_files:
        exists = check_file_exists(file_path, description)
        all_exist = all_exist and exists
        
        # Validate JSON/YAML syntax
        if exists:
            try:
                if file_path.endswith('.json'):
                    with open(file_path, 'r') as f:
                        json.load(f)
                elif file_path.endswith('.yaml'):
                    with open(file_path, 'r') as f:
                        yaml.safe_load(f)
                print(f"    ✅ Valid syntax")
            except Exception as e:
                print(f"    ❌ Invalid syntax: {e}")
                all_exist = False
    
    return all_exist

def check_model_architecture():
    """Check model architecture files."""
    print("\n🧠 Checking Model Architecture...")
    
    model_files = [
        ("models/__init__.py", "Models package init"),
        ("models/backbones/__init__.py", "Backbones package init"),
        ("models/backbones/cnn_lstm_backbone.py", "CNN-LSTM backbone"),
        ("models/heads/__init__.py", "Heads package init"),
        ("models/heads/small_fc.py", "Small FC head"),
        ("models/heads/cosine_fc.py", "Cosine FC head")
    ]
    
    all_exist = True
    for file_path, description in model_files:
        exists = check_file_exists(file_path, description)
        all_exist = all_exist and exists
    
    # Test model creation
    if all_exist:
        try:
            sys.path.append('.')
            from models import create_model
            
            model = create_model(num_classes=7, head_type="cosine_fc")
            total_params = sum(p.numel() for p in model.parameters())
            print(f"    ✅ Model creation successful: {total_params:,} parameters")
        except Exception as e:
            print(f"    ❌ Model creation failed: {e}")
            all_exist = False
    
    return all_exist

def check_training_scripts():
    """Check training scripts."""
    print("\n🚂 Checking Training Scripts...")
    
    training_files = [
        ("training/__init__.py", "Training package init"),
        ("training/train_icu_finetune_fixed.py", "Current ICU training script"),
        ("training/train_grid_pretrain.py", "GRID pretraining script"),
        ("training/finetune_icu_from_grid.py", "ICU fine-tuning script"),
        ("training/advanced_training_components.py", "Advanced components"),
        ("sm_entrypoint.py", "SageMaker entry point")
    ]
    
    all_exist = True
    for file_path, description in training_files:
        exists = check_file_exists(file_path, description)
        all_exist = all_exist and exists
    
    return all_exist

def check_evaluation_scripts():
    """Check evaluation scripts."""
    print("\n📊 Checking Evaluation Scripts...")
    
    eval_files = [
        ("eval/__init__.py", "Evaluation package init"),
        ("eval/evaluate_with_tta.py", "TTA evaluation script"),
        ("eval/dual_track_evaluation.py", "Dual-track evaluation script")
    ]
    
    all_exist = True
    for file_path, description in eval_files:
        exists = check_file_exists(file_path, description)
        all_exist = all_exist and exists
    
    return all_exist

def check_deployment_scripts():
    """Check deployment scripts."""
    print("\n🚢 Checking Deployment Scripts...")
    
    deploy_files = [
        ("deploy/__init__.py", "Deployment package init"),
        ("deploy/export_torchscript.py", "TorchScript export script"),
        ("deploy/export_onnx.py", "ONNX export script")
    ]
    
    all_exist = True
    for file_path, description in deploy_files:
        exists = check_file_exists(file_path, description)
        all_exist = all_exist and exists
    
    return all_exist

def check_preprocessing_tools():
    """Check preprocessing tools (copied byte-for-byte)."""
    print("\n🔧 Checking Preprocessing Tools...")
    
    tool_files = [
        ("tools/build_grid_manifest.py", "GRID manifest builder"),
        ("tools/build_manifest.py", "Manifest builder"),
        ("tools/create_grid_subset.py", "GRID subset creator"),
        ("tools/make_splits.py", "Split maker"),
        ("tools/make_temporal_subclips.py", "Temporal subclips"),
        ("tools/organize_speaker_data.py", "Speaker data organizer"),
        ("tools/roi_quality_audit.py", "ROI quality audit"),
        ("tools/sanity_checks.py", "Sanity checks"),
        ("tools/select_grid_subset.py", "GRID subset selector"),
        ("tools/stabilize_mouth_roi.py", "ROI stabilization"),
        ("tools/write_labelmap.py", "Label map writer"),
        ("utils/id_norm.py", "ID normalization"),
        ("utils/viseme_mapper.py", "Viseme mapper")
    ]
    
    all_exist = True
    for file_path, description in tool_files:
        exists = check_file_exists(file_path, description)
        all_exist = all_exist and exists
    
    return all_exist

def check_notebooks():
    """Check Jupyter notebooks."""
    print("\n📓 Checking Jupyter Notebooks...")
    
    notebook_files = [
        ("notebooks/01_grid_pretrain_3spk.ipynb", "GRID pretraining workflow"),
        ("notebooks/02_icu_loso_finetune.ipynb", "ICU LOSO fine-tuning workflow")
    ]
    
    all_exist = True
    for file_path, description in notebook_files:
        exists = check_file_exists(file_path, description)
        all_exist = all_exist and exists
        
        # Check notebook syntax
        if exists:
            try:
                with open(file_path, 'r') as f:
                    notebook = json.load(f)
                if 'cells' in notebook:
                    print(f"    ✅ Valid notebook with {len(notebook['cells'])} cells")
                else:
                    print(f"    ❌ Invalid notebook structure")
                    all_exist = False
            except Exception as e:
                print(f"    ❌ Invalid notebook: {e}")
                all_exist = False
    
    return all_exist

def check_dependencies():
    """Check Python dependencies."""
    print("\n📦 Checking Dependencies...")
    
    # Check requirements.txt
    req_exists = check_file_exists("requirements.txt", "Requirements file")
    
    # Check key dependencies
    key_deps = [
        ("torch", "PyTorch"),
        ("torchvision", "TorchVision"),
        ("numpy", "NumPy"),
        ("sklearn", "Scikit-learn"),
        ("yaml", "PyYAML"),
        ("cv2", "OpenCV")
    ]
    
    all_available = req_exists
    for module_name, description in key_deps:
        try:
            if module_name == "cv2":
                import cv2
            elif module_name == "yaml":
                import yaml
            elif module_name == "sklearn":
                import sklearn
            else:
                __import__(module_name)
            print(f"    ✅ {description} available")
        except ImportError:
            print(f"    ❌ {description} not available")
            all_available = False
    
    return all_available

def check_gpu_availability():
    """Check GPU availability."""
    print("\n🖥️  Checking GPU Availability...")
    
    try:
        import torch
        cuda_available = torch.cuda.is_available()
        
        if cuda_available:
            device_name = torch.cuda.get_device_name()
            memory_gb = torch.cuda.get_device_properties(0).total_memory / 1e9
            print(f"    ✅ CUDA available: {device_name}")
            print(f"    ✅ GPU memory: {memory_gb:.1f} GB")
            
            if memory_gb >= 8:
                print(f"    ✅ Sufficient memory for training")
                return True
            else:
                print(f"    ⚠️  Limited memory - consider reducing batch size")
                return True
        else:
            print(f"    ❌ CUDA not available - will use CPU (slow)")
            return False
    except Exception as e:
        print(f"    ❌ Error checking GPU: {e}")
        return False

def main():
    """Main validation function."""
    print("🔍 SageMaker Lip-Reading Training Setup Validation")
    print("=" * 60)
    
    # Change to script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # Run all checks
    checks = [
        ("Directory Structure", check_directory_structure),
        ("Configuration Files", check_configuration_files),
        ("Model Architecture", check_model_architecture),
        ("Training Scripts", check_training_scripts),
        ("Evaluation Scripts", check_evaluation_scripts),
        ("Deployment Scripts", check_deployment_scripts),
        ("Preprocessing Tools", check_preprocessing_tools),
        ("Jupyter Notebooks", check_notebooks),
        ("Dependencies", check_dependencies),
        ("GPU Availability", check_gpu_availability)
    ]
    
    results = {}
    for check_name, check_func in checks:
        try:
            results[check_name] = check_func()
        except Exception as e:
            print(f"    ❌ Error during {check_name}: {e}")
            results[check_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 VALIDATION SUMMARY")
    print("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for check_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {check_name}")
    
    print(f"\nOverall: {passed}/{total} checks passed")
    
    if passed == total:
        print("\n🎉 ALL CHECKS PASSED!")
        print("✅ SageMaker training pipeline is ready for use")
        print("\nNext steps:")
        print("1. Mount your data to data/ directories")
        print("2. Run GRID pretraining: notebooks/01_grid_pretrain_3spk.ipynb")
        print("3. Run ICU fine-tuning: notebooks/02_icu_loso_finetune.ipynb")
        print("4. Target: ≥82% LOSO validation accuracy")
    else:
        failed = total - passed
        print(f"\n⚠️  {failed} CHECKS FAILED")
        print("❌ Please fix the issues above before proceeding")
        print("\nTroubleshooting:")
        print("1. Ensure all files were copied correctly")
        print("2. Install missing dependencies: pip install -r requirements.txt")
        print("3. Check file permissions and paths")
    
    print("\n" + "=" * 60)
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
