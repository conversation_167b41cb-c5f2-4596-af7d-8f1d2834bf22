# SageMaker Lip-Reading Training - Usage Guide

This guide provides step-by-step instructions for using the SageMaker lip-reading training pipeline to achieve ≥82% LOSO validation accuracy.

## 🚀 Quick Start

### 1. Environment Setup

```bash
# Install dependencies
pip install -r requirements.txt

# Verify GPU availability
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
```

### 2. Data Preparation

```bash
# Mount/copy your data to the appropriate directories:
# - GRID corpus → data/grid_raw/
# - ICU preprocessed data → data/stabilized_speaker_sets/

# Verify data structure
ls data/grid_raw/        # Should contain GRID speaker directories
ls data/stabilized_speaker_sets/  # Should contain ICU speaker directories
```

### 3. Two-Stage Training Pipeline

#### Stage 1: GRID Pretraining
```bash
python training/train_grid_pretrain.py --config configs/grid_pretrain.yaml
```

#### Stage 2: ICU LOSO Fine-tuning
```bash
python training/finetune_icu_from_grid.py \
  --grid-encoder checkpoints/grid_pretrain/encoder.pt \
  --config configs/train_icufinetune_loso.yaml
```

### 4. Evaluation
```bash
# Test-time augmentation evaluation
python eval/evaluate_with_tta.py \
  --checkpoint "checkpoints/icu_loso/fold_*/best.pt" \
  --data-root data/stabilized_speaker_sets \
  --tta-crops 7
```

---

## 📋 Detailed Workflows

### Option A: Jupyter Notebooks (Recommended)

1. **GRID Pretraining**: `notebooks/01_grid_pretrain_3spk.ipynb`
2. **ICU Fine-tuning**: `notebooks/02_icu_loso_finetune.ipynb`

### Option B: SageMaker Entry Point

```bash
# Full pipeline
python sm_entrypoint.py \
  --mode full_pipeline \
  --grid-config configs/grid_pretrain.yaml \
  --icu-config configs/train_icufinetune_loso.yaml

# Individual stages
python sm_entrypoint.py --mode grid_pretrain --config configs/grid_pretrain.yaml
python sm_entrypoint.py --mode icu_finetune --config configs/train_icufinetune_loso.yaml --grid-encoder /path/to/encoder.pt
```

### Option C: Direct Script Execution

```bash
# GRID pretraining
python training/train_grid_pretrain.py --config configs/grid_pretrain.yaml

# ICU fine-tuning
python training/finetune_icu_from_grid.py \
  --grid-encoder checkpoints/grid_pretrain/encoder.pt \
  --config configs/train_icufinetune_loso.yaml
```

---

## ⚙️ Configuration

### Key Configuration Files

- **`configs/grid_pretrain.yaml`**: GRID pretraining parameters
- **`configs/train_icufinetune_loso.yaml`**: ICU LOSO fine-tuning parameters
- **`configs/label2idx.json`**: Global class mapping
- **`configs/grid_proxy_map.json`**: GRID→ICU viseme mapping

### Important Parameters

#### GRID Pretraining
```yaml
epochs: 25
batch_size: 16
learning_rate: 0.001
head: "cosine_fc"
loss: "arcface"
freeze_encoder_epochs: 3
```

#### ICU Fine-tuning
```yaml
epochs: 100
batch_size: 8
learning_rate: 0.0005
mixup_alpha: 0.2
target_accuracy: 0.82
early_stop_patience: 10
```

---

## 📊 Expected Results

### Performance Targets
- **GRID Pretraining**: Stable convergence, encoder ready for transfer
- **ICU LOSO**: ≥82% mean validation accuracy across folds
- **Training Time**: ~10-15 hours total on GPU

### Success Criteria
- [ ] GRID encoder saves successfully
- [ ] LOSO cross-validation completes for all folds
- [ ] Mean accuracy ≥ 82%
- [ ] Models export for deployment

---

## 🛠️ Troubleshooting

### Common Issues

#### 1. CUDA Out of Memory
```bash
# Reduce batch size in configs
batch_size: 4  # Instead of 8
gradient_accumulation: 2  # Compensate with accumulation
```

#### 2. Missing Data
```bash
# Check data directories
ls data/grid_raw/
ls data/stabilized_speaker_sets/
ls data/stabilized_speaker_sets/splits_subclips/
```

#### 3. GRID Encoder Not Found
```bash
# Verify GRID pretraining completed
ls checkpoints/grid_pretrain/encoder.pt
```

#### 4. Missing Classes in LOSO Folds
- This is handled gracefully by the training code
- Check logs for warnings about missing classes
- Metrics computed over available classes only

### Debug Mode
```bash
# Enable debug logging
export PYTHONPATH=.
python training/train_grid_pretrain.py --config configs/grid_pretrain.yaml --debug
```

---

## 🚢 Deployment

### Model Export

#### TorchScript
```bash
python deploy/export_torchscript.py \
  --checkpoint checkpoints/icu_loso/best_model.pt \
  --output-dir deployment/torchscript \
  --validate --benchmark
```

#### ONNX
```bash
python deploy/export_onnx.py \
  --checkpoint checkpoints/icu_loso/best_model.pt \
  --output-dir deployment/onnx \
  --validate --benchmark
```

### Production Files
After successful training and export:
```
deployment/
├── best_model.pt          # PyTorch checkpoint
├── label2idx.json         # Class mapping
├── torchscript/
│   ├── model_traced.pt    # TorchScript model
│   └── model_metadata.json
└── onnx/
    ├── model.onnx         # ONNX model
    └── onnx_metadata.json
```

---

## 📈 Performance Monitoring

### Training Metrics
- **Loss**: Should decrease steadily
- **Accuracy**: Target ≥82% validation
- **F1 Score**: Macro-averaged across classes

### Validation Strategy
- **LOSO**: Leave-One-Speaker-Out for honest generalization
- **Missing Classes**: Handled gracefully with warnings
- **Early Stopping**: Prevents overfitting

### Expected Timeline
1. **GRID Pretraining**: 2-3 hours
2. **ICU Fine-tuning**: 8-12 hours (12 folds × ~1 hour each)
3. **Evaluation**: 30 minutes
4. **Export**: 15 minutes

---

## 🔧 Advanced Usage

### Custom Configurations
```bash
# Create custom config
cp configs/train_icufinetune_loso.yaml configs/my_config.yaml
# Edit parameters as needed
python training/finetune_icu_from_grid.py --config configs/my_config.yaml
```

### Hyperparameter Search
```yaml
# Enable in config
hp_search: true
hp_search_trials: 20
hp_search_params:
  learning_rate: [0.0001, 0.001]
  mixup_alpha: [0.0, 0.1, 0.2, 0.3]
```

### Distributed Training
```yaml
# Multi-GPU setup
distributed: true
world_size: 2
```

---

## 📞 Support

### Validation Checklist
- [ ] All preprocessing tools work exactly as in current project
- [ ] LOSO cross-validation handles missing classes gracefully
- [ ] Training achieves ≥82% validation accuracy target
- [ ] Models export successfully for production deployment
- [ ] All current relative import paths preserved

### Performance Validation
- [ ] GRID pretraining converges without overfitting
- [ ] ICU fine-tuning shows consistent improvement across folds
- [ ] Test-time augmentation provides additional accuracy boost
- [ ] Final models generalize well to unseen speakers

For issues or questions, check the training logs and ensure all prerequisites are met according to this guide.
