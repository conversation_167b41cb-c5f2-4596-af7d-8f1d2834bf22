# SageMaker Lip-Reading Training - Git Ignore

# Data directories (too large for git)
data/grid_raw/
data/grid_subset_3spk/
data/stabilized_speaker_sets/
data/stabilized_subclips/
*.mp4
*.avi
*.mov
*.npy
*.npz

# Model checkpoints and outputs
checkpoints/
outputs/
runs/
logs/
*.pt
*.pth
*.ckpt
*.pkl
*.pickle

# Training artifacts
tensorboard_logs/
wandb/
mlruns/
.neptune/

# Jupyter notebook checkpoints
.ipynb_checkpoints/
*.ipynb_checkpoints

# Python cache
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/
*.tmp
*.temp
*.log
*.out
*.err

# SageMaker specific
.sagemaker/
sagemaker_config.json
model.tar.gz

# Large files that shouldn't be in git
*.h5
*.hdf5
*.bin
*.safetensors

# Compiled extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/
