# ICU LOSO Fine-tuning Configuration
# Leave-One-Speaker-Out cross-validation targeting ≥82% accuracy

# Data Configuration
data_root: data/stabilized_speaker_sets
splits_dir: splits_subclips
labels: ["doctor", "glasses", "i_need_to_move", "my_back_hurts", "my_mouth_is_dry", "phone", "pillow"]
num_classes: 7

# Model Architecture
backbone: "cnn_lstm"       # Use current CNN-LSTM architecture
head: "cosine_fc"          # Optimized cosine similarity head
dropout: 0.6
pretrained_encoder: null   # Will be set to GRID encoder path

# Training Parameters
batch_size: 8              # Smaller batch for fine-tuning
epochs: 100
learning_rate: 0.0005      # Lower LR for fine-tuning
optimizer: "adam"
weight_decay: 0.0002
scheduler: "reduce_on_plateau"
scheduler_params:
  factor: 0.5
  patience: 8
  min_lr: 0.00001

# Loss Configuration
loss: "arcface"
arcface:
  margin: 0.2              # Optimized for small dataset
  scale: 16.0              # Calibrated for fine-tuning
label_smoothing: 0.05      # Light label smoothing

# Progressive Training Strategy
freeze_encoder_epochs: 5   # Freeze encoder for first 5 epochs
unfreeze_last_block: true  # Progressive unfreezing
curriculum: true           # Start with high-quality samples

# Data Augmentation (feature-space, preserves lip-sync)
temporal_jitter: true      # Slight temporal variations
temporal_jitter_frames: 2  # ±2 frames variation
mixup_alpha: 0.2          # Feature-space MixUp for regularization

# Class Balancing
sampler:
  enabled: true
  mode: "class"            # Class-balanced sampling
  replacement: true

# Missing Class Handling
allow_missing_classes: true  # Handle speakers with incomplete class sets
missing_class_strategy: "warn_and_continue"
compute_global_metrics: true  # Compute metrics over all classes for comparison

# Regularization
early_stop_patience: 10
save_best: true
gradient_clip: 1.0

# Test-Time Augmentation
tta_crops: 7               # Number of TTA crops for evaluation
tta_enabled: true

# Exponential Moving Average
use_ema: true
ema_decay: 0.999

# Hardware Optimization
amp: true                  # Automatic mixed precision
num_workers: 4
pin_memory: true
persistent_workers: true

# LOSO Cross-Validation
loso_enabled: true
loso_folds: "auto"         # Auto-detect from splits_dir
loso_parallel: false       # Sequential fold training

# Logging and Checkpointing
log_interval: 5
save_interval: 10
output_dir: checkpoints/icu_loso
tensorboard_dir: runs/icu_loso

# Reproducibility
seed: 1337
deterministic: true

# SageMaker Integration
use_sagemaker: true
sagemaker_output_dir: /opt/ml/model

# Validation
val_metrics: ["accuracy", "f1_macro", "f1_weighted"]
val_interval: 1            # Validate every epoch

# Model Export
export_formats: ["pytorch", "torchscript", "onnx"]
export_best_only: true

# Performance Targets
target_accuracy: 0.82      # Target ≥82% LOSO accuracy
success_threshold: 0.80    # Minimum acceptable accuracy

# Memory Optimization
max_memory_usage: 0.8      # Use up to 80% of GPU memory
gradient_accumulation: 1   # Adjust if memory constrained

# Distributed Training (if using multiple GPUs)
distributed: false
world_size: 1
rank: 0

# Advanced Training Techniques
focal_loss: false          # Use focal loss for class imbalance
focal_alpha: 0.25
focal_gamma: 2.0

cosine_annealing: false    # Cosine annealing LR schedule
warmup_epochs: 0

# Debug and Profiling
debug: false
profile: false
check_val_every_n_epoch: 1
log_model_stats: true

# Evaluation Configuration
eval_on_test: true         # Evaluate on test set after training
save_predictions: true     # Save predictions for analysis
save_confusion_matrix: true
save_classification_report: true

# Hyperparameter Search (if enabled)
hp_search: false
hp_search_trials: 20
hp_search_params:
  learning_rate: [0.0001, 0.001]
  batch_size: [4, 8, 16]
  mixup_alpha: [0.0, 0.1, 0.2, 0.3]
