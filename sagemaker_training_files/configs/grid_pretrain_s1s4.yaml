amp: true
arcface:
  label_smoothing: 0.0
  margin: 0.2
  scale: 30.0
backbone: cnn_lstm
batch_size: 16
check_val_every_n_epoch: 1
curriculum: true
data_root: data/grid_subset_3spk
debug: false
deterministic: true
distributed: false
dropout: 0.6
early_stop_patience: 15
ema_decay: 0.999
epochs: 40
export_encoder_only: true
export_formats:
- pytorch
- torchscript
freeze_encoder_epochs: 3
gradient_accumulation: 1
gradient_clip: 1.0
head: cosine_fc
label_smoothing: 0.0
labels:
- at
- bin
- blue
- green
- in
- lay
- now
- one
- place
- please
- red
- set
- soon
- white
- with
learning_rate: 0.001
log_interval: 10
loss: arcface
max_memory_usage: 0.8
mixup_alpha: 0.0
num_classes: 15
num_workers: 4
optimizer: adam
output_dir: checkpoints/grid_pretrain_s1s4
persistent_workers: true
pin_memory: true
profile: false
rank: 0
sagemaker_output_dir: /opt/ml/model
save_best: true
save_interval: 5
scheduler: reduce_on_plateau
scheduler_params:
  factor: 0.5
  min_lr: 1.0e-05
  patience: 5
seed: 1337
temporal_jitter: true
temporal_jitter_frames: 2
tensorboard_dir: runs/grid_pretrain_s1s4
unfreeze_last_block: true
use_ema: false
use_sagemaker: true
val_interval: 1
val_metrics:
- accuracy
- f1_macro
val_split: 0.25
weight_decay: 0.0001
world_size: 1
