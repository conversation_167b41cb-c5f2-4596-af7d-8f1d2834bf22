# GRID Corpus Pretraining Configuration
# 3-speaker subset with viseme-matched vocabulary for encoder pretraining

# Data Configuration
data_root: data/grid_subset_3spk
labels: ["doctor", "glasses", "i_need_to_move", "my_back_hurts", "my_mouth_is_dry", "phone", "pillow"]
num_classes: 7

# Model Architecture
backbone: "cnn_lstm"  # Use current CNN-LSTM architecture
head: "cosine_fc"     # Optimized cosine similarity head
dropout: 0.6

# Training Parameters
batch_size: 16
epochs: 25
learning_rate: 0.001
optimizer: "adam"
weight_decay: 0.0001
scheduler: "reduce_on_plateau"
scheduler_params:
  factor: 0.5
  patience: 5
  min_lr: 0.00001

# Loss Configuration
loss: "arcface"
arcface:
  margin: 0.2      # Optimized for small dataset
  scale: 30.0      # Calibrated for 7-class problem
  label_smoothing: 0.0

# Progressive Training Strategy
freeze_encoder_epochs: 3    # Freeze encoder for first 3 epochs
unfreeze_last_block: true   # Progressive unfreezing strategy
curriculum: true            # Start with high-quality samples

# Data Augmentation (applied in dataloader, not preprocessing)
temporal_jitter: true       # Slight temporal variations
temporal_jitter_frames: 2   # ±2 frames variation
mixup_alpha: 0.0           # Disabled for pretraining stability

# Regularization
early_stop_patience: 7
save_best: true
gradient_clip: 1.0

# Hardware Optimization
amp: true                  # Automatic mixed precision
num_workers: 4
pin_memory: true
persistent_workers: true

# Logging and Checkpointing
log_interval: 10
save_interval: 5
output_dir: checkpoints/grid_pretrain
tensorboard_dir: runs/grid_pretrain

# Reproducibility
seed: 1337
deterministic: true

# SageMaker Integration
use_sagemaker: true
sagemaker_output_dir: /opt/ml/model

# Validation
val_split: 0.2             # 20% validation split
val_interval: 1            # Validate every epoch
val_metrics: ["accuracy", "f1_macro"]

# Model Export
export_encoder_only: true  # Save encoder state_dict separately
export_formats: ["pytorch", "torchscript"]

# Advanced Options
use_ema: false             # Exponential moving average (disabled for pretraining)
ema_decay: 0.999
label_smoothing: 0.0       # No label smoothing for pretraining

# Memory Optimization
max_memory_usage: 0.8      # Use up to 80% of GPU memory
gradient_accumulation: 1   # No gradient accumulation needed

# Distributed Training (if using multiple GPUs)
distributed: false
world_size: 1
rank: 0

# Debug Options
debug: false
profile: false
check_val_every_n_epoch: 1
