#!/usr/bin/env python3
"""
CNN-LSTM Backbone Architecture for Lip-Reading
==============================================

Extracted from current training pipeline to maintain exact compatibility.
This is the proven architecture from train_icu_finetune_fixed.py.

Author: Augment Agent
Date: 2025-09-27
"""

import torch
import torch.nn as nn
import logging

logger = logging.getLogger(__name__)

class CNNLSTMBackbone(nn.Module):
    """
    3D CNN + LSTM backbone for lip-reading.
    
    This is the exact architecture from train_icu_finetune_fixed.py,
    extracted for modular use in the SageMaker pipeline.
    """
    
    def __init__(self, dropout=0.6):
        super().__init__()
        
        # 3D CNN Encoder (exact copy from current implementation)
        self.encoder = nn.Sequential(
            # Block 1
            nn.Conv3d(1, 32, (3, 3, 3), padding=1),
            nn.BatchNorm3d(32),
            nn.ReLU(inplace=True),
            nn.MaxPool3d((1, 2, 2)),
            
            # Block 2
            nn.Conv3d(32, 64, (3, 3, 3), padding=1),
            nn.BatchNorm3d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool3d((2, 2, 2)),
            
            # Block 3
            nn.Conv3d(64, 128, (3, 3, 3), padding=1),
            nn.BatchNorm3d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool3d((2, 2, 2)),
            
            # Block 4 (last block for progressive unfreezing)
            nn.Conv3d(128, 256, (3, 3, 3), padding=1),
            nn.BatchNorm3d(256),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool3d((4, 2, 2))
        )
        
        # LSTM (exact copy from current implementation)
        self.lstm = nn.LSTM(256 * 2 * 2, 256, batch_first=True, dropout=dropout)
        
        # Feature dimension for classification heads
        self.feature_dim = 256
    
    def forward(self, x):
        """
        Forward pass through CNN-LSTM backbone.
        
        Args:
            x: Input tensor [B, C, T, H, W]
            
        Returns:
            features: Feature tensor [B, feature_dim]
        """
        batch_size, channels, frames, height, width = x.shape

        # CNN encoding
        x = self.encoder(x)  # [B, 256, T', H', W']

        # Reshape for LSTM
        x = x.permute(0, 2, 1, 3, 4)  # [B, T', 256, H', W']
        x = x.contiguous().view(batch_size, x.size(1), -1)  # [B, T', 256*H'*W']

        # LSTM
        lstm_out, _ = self.lstm(x)  # [B, T', 256]

        # Use last timestep
        features = lstm_out[:, -1, :]  # [B, 256]
        
        return features
    
    def freeze_encoder(self):
        """Freeze encoder parameters for progressive training."""
        for param in self.encoder.parameters():
            param.requires_grad = False
        logger.info("Encoder frozen")
    
    def unfreeze_encoder(self):
        """Unfreeze all encoder parameters."""
        for param in self.encoder.parameters():
            param.requires_grad = True
        logger.info("Encoder unfrozen")
    
    def unfreeze_last_block(self):
        """Unfreeze only the last CNN block (Block 4)."""
        # Freeze all encoder parameters first
        self.freeze_encoder()
        
        # Unfreeze Block 4 (last 4 layers: Conv3d, BatchNorm3d, ReLU, AdaptiveAvgPool3d)
        for param in self.encoder[-4:].parameters():
            param.requires_grad = True
        
        logger.info("Last CNN block unfrozen")
    
    def get_encoder_state_dict(self):
        """Get encoder state dict for pretraining."""
        return self.encoder.state_dict()
    
    def load_encoder_state_dict(self, state_dict):
        """Load encoder state dict from pretraining."""
        self.encoder.load_state_dict(state_dict)
        logger.info("Encoder state dict loaded")


class EnhancedLightweightCNNLSTM(nn.Module):
    """
    Complete model with backbone + classification head.
    
    This maintains compatibility with the current training pipeline
    while providing modular architecture for SageMaker deployment.
    """
    
    def __init__(self, num_classes=7, dropout=0.6, head_type="small_fc"):
        super().__init__()
        
        # Import heads here to avoid circular imports
        from ..heads import SmallFCHead, CosineFCHead
        
        # Backbone
        self.backbone = CNNLSTMBackbone(dropout=dropout)
        
        # Classification head
        if head_type == "small_fc":
            self.classifier = SmallFCHead(self.backbone.feature_dim, num_classes, dropout)
        elif head_type == "cosine_fc":
            self.classifier = CosineFCHead(self.backbone.feature_dim, num_classes)
        else:  # standard
            self.classifier = nn.Sequential(
                nn.Dropout(dropout),
                nn.Linear(self.backbone.feature_dim, num_classes)
            )
    
    def forward(self, x, labels=None, use_arcface=False):
        """
        Forward pass through complete model.
        
        Args:
            x: Input tensor [B, C, T, H, W]
            labels: Ground truth labels (for ArcFace loss)
            use_arcface: Whether to use ArcFace margin
            
        Returns:
            logits: Classification logits [B, num_classes]
        """
        # Extract features
        features = self.backbone(x)
        
        # Classification (support ArcFace for cosine head)
        if hasattr(self.classifier, 'forward') and 'labels' in self.classifier.forward.__code__.co_varnames:
            return self.classifier(features, labels, use_arcface)
        else:
            return self.classifier(features)
    
    def freeze_encoder(self):
        """Freeze encoder parameters."""
        self.backbone.freeze_encoder()
    
    def unfreeze_encoder(self):
        """Unfreeze encoder parameters."""
        self.backbone.unfreeze_encoder()
    
    def unfreeze_last_block(self):
        """Unfreeze only the last CNN block."""
        self.backbone.unfreeze_last_block()
    
    def load_pretrained_encoder(self, encoder_path):
        """Load pretrained encoder weights."""
        encoder_state_dict = torch.load(encoder_path, map_location='cpu')
        self.backbone.load_encoder_state_dict(encoder_state_dict)
        logger.info(f"Loaded pretrained encoder from {encoder_path}")


def create_model(num_classes=7, dropout=0.6, head_type="small_fc", pretrained_encoder=None):
    """
    Factory function to create model with optional pretrained encoder.
    
    Args:
        num_classes: Number of output classes
        dropout: Dropout rate
        head_type: Type of classification head ("small_fc", "cosine_fc", "standard")
        pretrained_encoder: Path to pretrained encoder weights
        
    Returns:
        model: Complete model ready for training
    """
    model = EnhancedLightweightCNNLSTM(
        num_classes=num_classes,
        dropout=dropout,
        head_type=head_type
    )
    
    if pretrained_encoder is not None:
        model.load_pretrained_encoder(pretrained_encoder)
    
    return model
