#!/usr/bin/env python3
"""
TorchScript Model Export
========================

Export trained lip-reading models to TorchScript for production deployment.
Supports both JIT tracing and scripting modes.

Key Features:
- Model optimization for inference
- Input/output validation
- Metadata embedding
- Performance benchmarking

Author: Augment Agent
Date: 2025-09-27
"""

import os
import sys
import json
import argparse
import logging
import time
from pathlib import Path

import torch
import torch.nn as nn
import numpy as np

# Add project paths
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.append(str(project_root))

from models import create_model

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_model_checkpoint(checkpoint_path, num_classes=7, head_type="cosine_fc", device='cpu'):
    """Load model from checkpoint."""
    logger.info(f"Loading model from: {checkpoint_path}")
    
    model = create_model(
        num_classes=num_classes,
        dropout=0.6,  # Will be disabled in eval mode
        head_type=head_type
    )
    
    # Load state dict
    state_dict = torch.load(checkpoint_path, map_location=device)
    model.load_state_dict(state_dict)
    model.to(device)
    model.eval()  # Set to evaluation mode
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    logger.info(f"Model loaded: {total_params:,} parameters")
    
    return model

def create_example_input(batch_size=1, channels=1, frames=32, height=64, width=96, device='cpu'):
    """Create example input tensor for tracing."""
    example_input = torch.randn(batch_size, channels, frames, height, width, device=device)
    logger.info(f"Example input shape: {example_input.shape}")
    return example_input

def export_torchscript_trace(model, example_input, output_path):
    """Export model using TorchScript tracing."""
    logger.info("Exporting with TorchScript tracing...")
    
    try:
        # Trace the model
        with torch.no_grad():
            traced_model = torch.jit.trace(model, example_input)
        
        # Optimize for inference
        traced_model = torch.jit.optimize_for_inference(traced_model)
        
        # Save traced model
        traced_model.save(output_path)
        logger.info(f"Traced model saved to: {output_path}")
        
        return traced_model
    except Exception as e:
        logger.error(f"Tracing failed: {e}")
        return None

def export_torchscript_script(model, output_path):
    """Export model using TorchScript scripting."""
    logger.info("Exporting with TorchScript scripting...")
    
    try:
        # Script the model
        scripted_model = torch.jit.script(model)
        
        # Optimize for inference
        scripted_model = torch.jit.optimize_for_inference(scripted_model)
        
        # Save scripted model
        scripted_model.save(output_path)
        logger.info(f"Scripted model saved to: {output_path}")
        
        return scripted_model
    except Exception as e:
        logger.error(f"Scripting failed: {e}")
        return None

def validate_exported_model(original_model, exported_model, example_input, tolerance=1e-5):
    """Validate exported model against original."""
    logger.info("Validating exported model...")
    
    try:
        with torch.no_grad():
            # Get outputs from both models
            original_output = original_model(example_input)
            exported_output = exported_model(example_input)
            
            # Compare outputs
            if isinstance(original_output, tuple):
                original_output = original_output[0]
            if isinstance(exported_output, tuple):
                exported_output = exported_output[0]
            
            # Calculate difference
            max_diff = torch.max(torch.abs(original_output - exported_output)).item()
            mean_diff = torch.mean(torch.abs(original_output - exported_output)).item()
            
            logger.info(f"Max difference: {max_diff:.2e}")
            logger.info(f"Mean difference: {mean_diff:.2e}")
            
            if max_diff < tolerance:
                logger.info("✅ Validation passed!")
                return True
            else:
                logger.warning(f"⚠️ Validation failed: max diff {max_diff:.2e} > tolerance {tolerance:.2e}")
                return False
                
    except Exception as e:
        logger.error(f"Validation failed: {e}")
        return False

def benchmark_model(model, example_input, num_runs=100):
    """Benchmark model inference speed."""
    logger.info(f"Benchmarking model ({num_runs} runs)...")
    
    # Warmup
    with torch.no_grad():
        for _ in range(10):
            _ = model(example_input)
    
    # Benchmark
    times = []
    with torch.no_grad():
        for _ in range(num_runs):
            start_time = time.time()
            _ = model(example_input)
            end_time = time.time()
            times.append(end_time - start_time)
    
    # Calculate statistics
    mean_time = np.mean(times) * 1000  # Convert to ms
    std_time = np.std(times) * 1000
    min_time = np.min(times) * 1000
    max_time = np.max(times) * 1000
    
    logger.info(f"Inference time: {mean_time:.2f} ± {std_time:.2f} ms")
    logger.info(f"Min/Max time: {min_time:.2f} / {max_time:.2f} ms")
    
    return {
        'mean_ms': mean_time,
        'std_ms': std_time,
        'min_ms': min_time,
        'max_ms': max_time,
        'num_runs': num_runs
    }

def create_model_metadata(checkpoint_path, export_info, benchmark_results=None):
    """Create metadata for exported model."""
    # Load label mapping
    label_file = project_root / 'configs/label2idx.json'
    with open(label_file, 'r') as f:
        label2idx = json.load(f)
    
    metadata = {
        'model_info': {
            'source_checkpoint': str(checkpoint_path),
            'export_method': export_info['method'],
            'export_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'pytorch_version': torch.__version__
        },
        'model_config': {
            'num_classes': len(label2idx),
            'input_shape': export_info['input_shape'],
            'labels': label2idx
        },
        'deployment_info': {
            'framework': 'torchscript',
            'device_compatibility': ['cpu', 'cuda'],
            'recommended_batch_size': 1
        }
    }
    
    if benchmark_results:
        metadata['performance'] = benchmark_results
    
    return metadata

def main():
    parser = argparse.ArgumentParser(description='Export TorchScript Model')
    parser.add_argument('--checkpoint', required=True, help='Path to model checkpoint')
    parser.add_argument('--output-dir', required=True, help='Output directory')
    parser.add_argument('--method', choices=['trace', 'script', 'both'], default='trace',
                       help='Export method')
    parser.add_argument('--batch-size', type=int, default=1, help='Batch size for tracing')
    parser.add_argument('--input-shape', nargs=4, type=int, default=[32, 64, 96, 1],
                       help='Input shape: frames height width channels')
    parser.add_argument('--device', default='cpu', help='Device for export')
    parser.add_argument('--benchmark', action='store_true', help='Run benchmark')
    parser.add_argument('--validate', action='store_true', help='Validate exported model')
    args = parser.parse_args()
    
    # Setup
    device = torch.device(args.device)
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    logger.info(f"Exporting model to: {output_dir}")
    logger.info(f"Using device: {device}")
    
    # Load model
    model = load_model_checkpoint(args.checkpoint, device=device)
    
    # Create example input
    frames, height, width, channels = args.input_shape
    example_input = create_example_input(
        batch_size=args.batch_size,
        channels=channels,
        frames=frames,
        height=height,
        width=width,
        device=device
    )
    
    # Export models
    exported_models = {}
    
    if args.method in ['trace', 'both']:
        trace_path = output_dir / 'model_traced.pt'
        traced_model = export_torchscript_trace(model, example_input, trace_path)
        if traced_model:
            exported_models['traced'] = traced_model
    
    if args.method in ['script', 'both']:
        script_path = output_dir / 'model_scripted.pt'
        scripted_model = export_torchscript_script(model, script_path)
        if scripted_model:
            exported_models['scripted'] = scripted_model
    
    # Validation
    if args.validate and exported_models:
        for name, exported_model in exported_models.items():
            logger.info(f"Validating {name} model...")
            validate_exported_model(model, exported_model, example_input)
    
    # Benchmarking
    benchmark_results = {}
    if args.benchmark:
        # Benchmark original model
        benchmark_results['original'] = benchmark_model(model, example_input)
        
        # Benchmark exported models
        for name, exported_model in exported_models.items():
            benchmark_results[name] = benchmark_model(exported_model, example_input)
    
    # Create and save metadata
    export_info = {
        'method': args.method,
        'input_shape': [args.batch_size, channels, frames, height, width]
    }
    
    metadata = create_model_metadata(
        args.checkpoint, export_info, benchmark_results if args.benchmark else None
    )
    
    with open(output_dir / 'model_metadata.json', 'w') as f:
        json.dump(metadata, f, indent=2)
    
    logger.info("Export completed successfully!")
    
    # Print summary
    logger.info("="*50)
    logger.info("EXPORT SUMMARY:")
    logger.info(f"Source: {args.checkpoint}")
    logger.info(f"Output: {output_dir}")
    logger.info(f"Method: {args.method}")
    logger.info(f"Models exported: {list(exported_models.keys())}")
    
    if benchmark_results:
        logger.info("PERFORMANCE:")
        for name, results in benchmark_results.items():
            logger.info(f"{name}: {results['mean_ms']:.2f} ± {results['std_ms']:.2f} ms")

if __name__ == '__main__':
    main()
