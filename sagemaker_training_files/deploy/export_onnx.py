#!/usr/bin/env python3
"""
ONNX Model Export
=================

Export trained lip-reading models to ONNX format for cross-platform deployment.
Supports optimization and validation.

Key Features:
- ONNX export with optimization
- Cross-platform compatibility
- Input/output validation
- Model simplification

Author: Augment Agent
Date: 2025-09-27
"""

import os
import sys
import json
import argparse
import logging
import time
from pathlib import Path

import torch
import torch.onnx
import numpy as np

# Add project paths
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.append(str(project_root))

from models import create_model

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_model_checkpoint(checkpoint_path, num_classes=7, head_type="cosine_fc", device='cpu'):
    """Load model from checkpoint."""
    logger.info(f"Loading model from: {checkpoint_path}")
    
    model = create_model(
        num_classes=num_classes,
        dropout=0.6,  # Will be disabled in eval mode
        head_type=head_type
    )
    
    # Load state dict
    state_dict = torch.load(checkpoint_path, map_location=device)
    model.load_state_dict(state_dict)
    model.to(device)
    model.eval()  # Set to evaluation mode
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    logger.info(f"Model loaded: {total_params:,} parameters")
    
    return model

def export_onnx_model(model, example_input, output_path, opset_version=11):
    """Export model to ONNX format."""
    logger.info(f"Exporting to ONNX (opset {opset_version})...")
    
    try:
        # Define input and output names
        input_names = ['video_input']
        output_names = ['class_logits']
        
        # Define dynamic axes for flexible batch size
        dynamic_axes = {
            'video_input': {0: 'batch_size'},
            'class_logits': {0: 'batch_size'}
        }
        
        # Export to ONNX
        torch.onnx.export(
            model,
            example_input,
            output_path,
            export_params=True,
            opset_version=opset_version,
            do_constant_folding=True,
            input_names=input_names,
            output_names=output_names,
            dynamic_axes=dynamic_axes,
            verbose=False
        )
        
        logger.info(f"ONNX model saved to: {output_path}")
        return True
        
    except Exception as e:
        logger.error(f"ONNX export failed: {e}")
        return False

def validate_onnx_model(original_model, onnx_path, example_input, tolerance=1e-4):
    """Validate ONNX model against original PyTorch model."""
    logger.info("Validating ONNX model...")
    
    try:
        import onnxruntime as ort
        
        # Create ONNX Runtime session
        ort_session = ort.InferenceSession(str(onnx_path))
        
        # Get PyTorch output
        with torch.no_grad():
            pytorch_output = original_model(example_input)
            if isinstance(pytorch_output, tuple):
                pytorch_output = pytorch_output[0]
        
        # Get ONNX output
        ort_inputs = {ort_session.get_inputs()[0].name: example_input.cpu().numpy()}
        onnx_output = ort_session.run(None, ort_inputs)[0]
        
        # Compare outputs
        pytorch_np = pytorch_output.cpu().numpy()
        max_diff = np.max(np.abs(pytorch_np - onnx_output))
        mean_diff = np.mean(np.abs(pytorch_np - onnx_output))
        
        logger.info(f"Max difference: {max_diff:.2e}")
        logger.info(f"Mean difference: {mean_diff:.2e}")
        
        if max_diff < tolerance:
            logger.info("✅ ONNX validation passed!")
            return True
        else:
            logger.warning(f"⚠️ ONNX validation failed: max diff {max_diff:.2e} > tolerance {tolerance:.2e}")
            return False
            
    except ImportError:
        logger.warning("ONNX Runtime not available, skipping validation")
        return None
    except Exception as e:
        logger.error(f"ONNX validation failed: {e}")
        return False

def optimize_onnx_model(onnx_path, optimized_path):
    """Optimize ONNX model for inference."""
    logger.info("Optimizing ONNX model...")
    
    try:
        import onnx
        from onnxruntime.tools import optimizer
        
        # Load ONNX model
        model = onnx.load(str(onnx_path))
        
        # Optimize model
        optimized_model = optimizer.optimize_model(
            str(onnx_path),
            model_type='bert',  # Use general optimization
            num_heads=0,
            hidden_size=0
        )
        
        # Save optimized model
        optimized_model.save_model_to_file(str(optimized_path))
        logger.info(f"Optimized ONNX model saved to: {optimized_path}")
        return True
        
    except ImportError:
        logger.warning("ONNX optimization tools not available")
        return False
    except Exception as e:
        logger.error(f"ONNX optimization failed: {e}")
        return False

def get_onnx_model_info(onnx_path):
    """Get information about ONNX model."""
    try:
        import onnx
        
        model = onnx.load(str(onnx_path))
        
        # Get input/output info
        inputs = []
        for input_tensor in model.graph.input:
            shape = [dim.dim_value if dim.dim_value > 0 else 'dynamic' 
                    for dim in input_tensor.type.tensor_type.shape.dim]
            inputs.append({
                'name': input_tensor.name,
                'shape': shape,
                'type': input_tensor.type.tensor_type.elem_type
            })
        
        outputs = []
        for output_tensor in model.graph.output:
            shape = [dim.dim_value if dim.dim_value > 0 else 'dynamic' 
                    for dim in output_tensor.type.tensor_type.shape.dim]
            outputs.append({
                'name': output_tensor.name,
                'shape': shape,
                'type': output_tensor.type.tensor_type.elem_type
            })
        
        return {
            'inputs': inputs,
            'outputs': outputs,
            'opset_version': model.opset_import[0].version if model.opset_import else None
        }
        
    except ImportError:
        logger.warning("ONNX not available for model info")
        return None
    except Exception as e:
        logger.error(f"Failed to get ONNX model info: {e}")
        return None

def benchmark_onnx_model(onnx_path, example_input, num_runs=100):
    """Benchmark ONNX model inference speed."""
    logger.info(f"Benchmarking ONNX model ({num_runs} runs)...")
    
    try:
        import onnxruntime as ort
        
        # Create session
        ort_session = ort.InferenceSession(str(onnx_path))
        
        # Prepare input
        ort_inputs = {ort_session.get_inputs()[0].name: example_input.cpu().numpy()}
        
        # Warmup
        for _ in range(10):
            _ = ort_session.run(None, ort_inputs)
        
        # Benchmark
        times = []
        for _ in range(num_runs):
            start_time = time.time()
            _ = ort_session.run(None, ort_inputs)
            end_time = time.time()
            times.append(end_time - start_time)
        
        # Calculate statistics
        mean_time = np.mean(times) * 1000  # Convert to ms
        std_time = np.std(times) * 1000
        min_time = np.min(times) * 1000
        max_time = np.max(times) * 1000
        
        logger.info(f"ONNX inference time: {mean_time:.2f} ± {std_time:.2f} ms")
        
        return {
            'mean_ms': mean_time,
            'std_ms': std_time,
            'min_ms': min_time,
            'max_ms': max_time,
            'num_runs': num_runs
        }
        
    except ImportError:
        logger.warning("ONNX Runtime not available for benchmarking")
        return None
    except Exception as e:
        logger.error(f"ONNX benchmarking failed: {e}")
        return None

def create_onnx_metadata(checkpoint_path, onnx_info, benchmark_results=None):
    """Create metadata for ONNX model."""
    # Load label mapping
    label_file = project_root / 'configs/label2idx.json'
    with open(label_file, 'r') as f:
        label2idx = json.load(f)
    
    metadata = {
        'model_info': {
            'source_checkpoint': str(checkpoint_path),
            'export_format': 'onnx',
            'export_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'pytorch_version': torch.__version__
        },
        'model_config': {
            'num_classes': len(label2idx),
            'labels': label2idx
        },
        'deployment_info': {
            'framework': 'onnx',
            'runtime': 'onnxruntime',
            'device_compatibility': ['cpu', 'cuda', 'tensorrt'],
            'recommended_batch_size': 1
        }
    }
    
    if onnx_info:
        metadata['onnx_info'] = onnx_info
    
    if benchmark_results:
        metadata['performance'] = benchmark_results
    
    return metadata

def main():
    parser = argparse.ArgumentParser(description='Export ONNX Model')
    parser.add_argument('--checkpoint', required=True, help='Path to model checkpoint')
    parser.add_argument('--output-dir', required=True, help='Output directory')
    parser.add_argument('--opset-version', type=int, default=11, help='ONNX opset version')
    parser.add_argument('--batch-size', type=int, default=1, help='Batch size for export')
    parser.add_argument('--input-shape', nargs=4, type=int, default=[32, 64, 96, 1],
                       help='Input shape: frames height width channels')
    parser.add_argument('--device', default='cpu', help='Device for export')
    parser.add_argument('--optimize', action='store_true', help='Optimize ONNX model')
    parser.add_argument('--validate', action='store_true', help='Validate ONNX model')
    parser.add_argument('--benchmark', action='store_true', help='Benchmark ONNX model')
    args = parser.parse_args()
    
    # Setup
    device = torch.device(args.device)
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    logger.info(f"Exporting ONNX model to: {output_dir}")
    logger.info(f"Using device: {device}")
    
    # Load model
    model = load_model_checkpoint(args.checkpoint, device=device)
    
    # Create example input
    frames, height, width, channels = args.input_shape
    example_input = torch.randn(args.batch_size, channels, frames, height, width, device=device)
    logger.info(f"Example input shape: {example_input.shape}")
    
    # Export ONNX model
    onnx_path = output_dir / 'model.onnx'
    success = export_onnx_model(model, example_input, onnx_path, args.opset_version)
    
    if not success:
        logger.error("ONNX export failed")
        return
    
    # Get model info
    onnx_info = get_onnx_model_info(onnx_path)
    if onnx_info:
        logger.info(f"ONNX model info: {onnx_info}")
    
    # Optimize model
    if args.optimize:
        optimized_path = output_dir / 'model_optimized.onnx'
        optimize_onnx_model(onnx_path, optimized_path)
    
    # Validate model
    if args.validate:
        validate_onnx_model(model, onnx_path, example_input)
    
    # Benchmark model
    benchmark_results = None
    if args.benchmark:
        benchmark_results = benchmark_onnx_model(onnx_path, example_input)
    
    # Create and save metadata
    metadata = create_onnx_metadata(args.checkpoint, onnx_info, benchmark_results)
    
    with open(output_dir / 'onnx_metadata.json', 'w') as f:
        json.dump(metadata, f, indent=2)
    
    logger.info("ONNX export completed successfully!")
    
    # Print summary
    logger.info("="*50)
    logger.info("ONNX EXPORT SUMMARY:")
    logger.info(f"Source: {args.checkpoint}")
    logger.info(f"Output: {onnx_path}")
    logger.info(f"Opset version: {args.opset_version}")
    
    if benchmark_results:
        logger.info(f"Performance: {benchmark_results['mean_ms']:.2f} ± {benchmark_results['std_ms']:.2f} ms")

if __name__ == '__main__':
    main()
