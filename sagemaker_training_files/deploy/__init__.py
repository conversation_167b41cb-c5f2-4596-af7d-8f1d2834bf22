"""
Model deployment scripts for SageMaker lip-reading pipeline.
"""

# Import key deployment functions if available
try:
    from .export_torchscript import main as export_torchscript_main
except ImportError:
    export_torchscript_main = None

try:
    from .export_onnx import main as export_onnx_main
except ImportError:
    export_onnx_main = None

__all__ = ['export_torchscript_main', 'export_onnx_main']
