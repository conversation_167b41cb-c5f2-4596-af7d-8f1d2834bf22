#!/usr/bin/env python3
"""
ICU Fine-tuning from GRID Pretrained Encoder
============================================

Fine-tune ICU lip-reading model using GRID pretrained encoder.
Implements LOSO cross-validation with missing class handling.

Key Features:
- Load GRID pretrained encoder
- LOSO cross-validation across ICU speakers
- Missing class handling (graceful degradation)
- MixUp augmentation for regularization
- Progressive unfreezing strategy
- Target: ≥82% LOSO validation accuracy

Author: Augment Agent
Date: 2025-09-27
"""

import os
import sys
import json
import yaml
import argparse
import logging
import time
from pathlib import Path
import glob

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from sklearn.metrics import accuracy_score, f1_score, classification_report

# Add project paths - adjust for SageMaker structure
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.append(str(project_root))

from models import create_model
from training.advanced_training_components import (
    ComprehensiveVideoDataset,
    StandardizedPreprocessor,
    LabelSmoothingCrossEntropy,
    create_weighted_sampler
)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_config(config_path):
    """Load YAML configuration file."""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config

def get_loso_folds(splits_dir):
    """Get LOSO fold directories."""
    splits_path = Path(splits_dir)
    fold_dirs = sorted([d for d in splits_path.iterdir() if d.is_dir() and d.name.startswith('fold_')])
    return fold_dirs

def create_icu_dataset(fold_dir, split, config):
    """Create ICU dataset for specific fold and split."""
    # Load global label mapping
    with open(project_root / 'configs/label2idx.json', 'r') as f:
        label2idx = json.load(f)
    
    # Create preprocessor (uses current preprocessing pipeline)
    preprocessor = StandardizedPreprocessor()
    
    # Get split file
    split_file = fold_dir / f'{split}.txt'
    if not split_file.exists():
        logger.warning(f"Split file not found: {split_file}")
        return None, label2idx
    
    # Create dataset
    dataset = ComprehensiveVideoDataset(
        data_root=config['data_root'],
        labels=config['labels'],
        label2idx=label2idx,
        preprocessor=preprocessor,
        split_file=str(split_file),
        temporal_jitter=config.get('temporal_jitter', False),
        temporal_jitter_frames=config.get('temporal_jitter_frames', 2),
        allow_missing_classes=config.get('allow_missing_classes', True)
    )
    
    return dataset, label2idx

def mixup_data(x, y, alpha=0.2):
    """Apply MixUp augmentation."""
    if alpha > 0:
        lam = np.random.beta(alpha, alpha)
    else:
        lam = 1
    
    batch_size = x.size(0)
    index = torch.randperm(batch_size).to(x.device)
    
    mixed_x = lam * x + (1 - lam) * x[index, :]
    y_a, y_b = y, y[index]
    
    return mixed_x, y_a, y_b, lam

def mixup_criterion(criterion, pred, y_a, y_b, lam):
    """MixUp loss calculation."""
    return lam * criterion(pred, y_a) + (1 - lam) * criterion(pred, y_b)

def train_fold(fold_dir, config, pretrained_encoder_path, device):
    """Train single LOSO fold."""
    fold_name = fold_dir.name
    logger.info(f"Training fold: {fold_name}")
    
    # Create datasets
    train_dataset, label2idx = create_icu_dataset(fold_dir, 'train', config)
    val_dataset, _ = create_icu_dataset(fold_dir, 'val', config)
    
    if train_dataset is None or val_dataset is None:
        logger.error(f"Failed to create datasets for {fold_name}")
        return None
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['batch_size'],
        shuffle=True,
        num_workers=config.get('num_workers', 4),
        pin_memory=config.get('pin_memory', True)
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=config.get('num_workers', 4),
        pin_memory=config.get('pin_memory', True)
    )
    
    logger.info(f"Train samples: {len(train_dataset)}, Val samples: {len(val_dataset)}")
    
    # Create model with pretrained encoder
    model = create_model(
        num_classes=config['num_classes'],
        dropout=config.get('dropout', 0.6),
        head_type=config.get('head', 'cosine_fc'),
        pretrained_encoder=pretrained_encoder_path
    )
    model.to(device)
    
    # Create loss function
    if config['loss'] == 'arcface':
        from models.heads import ArcFaceLoss
        criterion = ArcFaceLoss(
            margin=config['arcface']['margin'],
            scale=config['arcface']['scale'],
            label_smoothing=config['arcface'].get('label_smoothing', 0.0)
        )
    else:
        criterion = nn.CrossEntropyLoss(label_smoothing=config.get('label_smoothing', 0.0))
    
    # Create optimizer
    optimizer = optim.Adam(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config.get('weight_decay', 0.0002)
    )
    
    # Create scheduler
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer,
        factor=config['scheduler_params']['factor'],
        patience=config['scheduler_params']['patience'],
        min_lr=config['scheduler_params']['min_lr']
    )
    
    # Training loop
    best_val_acc = 0.0
    patience_counter = 0
    
    # Create fold output directory
    fold_output_dir = Path(config['output_dir']) / fold_name
    fold_output_dir.mkdir(parents=True, exist_ok=True)
    
    for epoch in range(1, config['epochs'] + 1):
        # Progressive unfreezing
        if epoch == 1:
            model.freeze_encoder()
        elif epoch == config.get('freeze_encoder_epochs', 5) + 1:
            if config.get('unfreeze_last_block', True):
                model.unfreeze_last_block()
            else:
                model.unfreeze_encoder()
        
        # Train epoch
        model.train()
        train_loss = 0
        train_preds = []
        train_labels = []
        
        for batch_idx, (videos, labels) in enumerate(train_loader):
            videos, labels = videos.to(device), labels.to(device)
            
            # Apply MixUp if enabled
            if config.get('mixup_alpha', 0) > 0:
                videos, labels_a, labels_b, lam = mixup_data(videos, labels, config['mixup_alpha'])
            
            optimizer.zero_grad()
            
            # Forward pass
            use_arcface = config['loss'] == 'arcface'
            outputs = model(videos, labels, use_arcface)
            
            # Calculate loss
            if config.get('mixup_alpha', 0) > 0:
                loss = mixup_criterion(criterion, outputs, labels_a, labels_b, lam)
            else:
                loss = criterion(outputs, labels)
            
            # Backward pass
            loss.backward()
            
            # Gradient clipping
            if config.get('gradient_clip', 0) > 0:
                torch.nn.utils.clip_grad_norm_(model.parameters(), config['gradient_clip'])
            
            optimizer.step()
            
            # Statistics
            train_loss += loss.item()
            if config.get('mixup_alpha', 0) == 0:  # Only collect stats without MixUp
                _, predicted = torch.max(outputs.data, 1)
                train_preds.extend(predicted.cpu().numpy())
                train_labels.extend(labels.cpu().numpy())
        
        # Validation epoch
        model.eval()
        val_loss = 0
        val_preds = []
        val_labels = []
        
        with torch.no_grad():
            for videos, labels in val_loader:
                videos, labels = videos.to(device), labels.to(device)
                
                use_arcface = config['loss'] == 'arcface'
                outputs = model(videos, labels, use_arcface)
                loss = criterion(outputs, labels)
                
                val_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                val_preds.extend(predicted.cpu().numpy())
                val_labels.extend(labels.cpu().numpy())
        
        # Calculate metrics
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        
        if train_preds:  # Only if we have training predictions (no MixUp)
            train_acc = accuracy_score(train_labels, train_preds)
            train_f1 = f1_score(train_labels, train_preds, average='macro', zero_division=0)
        else:
            train_acc = train_f1 = 0.0
        
        val_acc = accuracy_score(val_labels, val_preds)
        val_f1 = f1_score(val_labels, val_preds, average='macro', zero_division=0)
        
        # Update scheduler
        scheduler.step(val_loss)
        
        # Log results
        if epoch % config.get('log_interval', 5) == 0:
            logger.info(f"{fold_name} Epoch {epoch}/{config['epochs']}")
            logger.info(f"Train: Loss={train_loss:.4f}, Acc={train_acc:.3f}, F1={train_f1:.3f}")
            logger.info(f"Val: Loss={val_loss:.4f}, Acc={val_acc:.3f}, F1={val_f1:.3f}")
        
        # Save best model
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            patience_counter = 0
            
            torch.save(model.state_dict(), fold_output_dir / 'best.pt')
            
            # Save detailed results
            results = {
                'epoch': epoch,
                'val_accuracy': val_acc,
                'val_f1': val_f1,
                'val_loss': val_loss,
                'classification_report': classification_report(val_labels, val_preds, output_dict=True, zero_division=0)
            }
            
            with open(fold_output_dir / 'best_results.json', 'w') as f:
                json.dump(results, f, indent=2)
        else:
            patience_counter += 1
        
        # Early stopping
        if patience_counter >= config.get('early_stop_patience', 10):
            logger.info(f"{fold_name} early stopping at epoch {epoch}")
            break
    
    logger.info(f"{fold_name} completed. Best validation accuracy: {best_val_acc:.3f}")
    return best_val_acc

def main():
    parser = argparse.ArgumentParser(description='ICU Fine-tuning from GRID')
    parser.add_argument('--grid-encoder', required=True, help='Path to GRID pretrained encoder')
    parser.add_argument('--config', required=True, help='Path to config file')
    parser.add_argument('--device', default='auto', help='Device to use (auto, cpu, cuda)')
    args = parser.parse_args()
    
    # Load configuration
    config = load_config(args.config)
    
    # Set device
    if args.device == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(args.device)
    
    logger.info(f"Using device: {device}")
    logger.info(f"GRID encoder: {args.grid_encoder}")
    
    # Set random seeds
    if config.get('seed'):
        torch.manual_seed(config['seed'])
        np.random.seed(config['seed'])
        if torch.cuda.is_available():
            torch.cuda.manual_seed(config['seed'])
    
    # Get LOSO folds
    splits_dir = Path(config['data_root']) / config['splits_dir']
    fold_dirs = get_loso_folds(splits_dir)
    
    if not fold_dirs:
        logger.error(f"No LOSO folds found in {splits_dir}")
        return
    
    logger.info(f"Found {len(fold_dirs)} LOSO folds")
    
    # Train each fold
    fold_results = {}
    
    for fold_dir in fold_dirs:
        try:
            best_acc = train_fold(fold_dir, config, args.grid_encoder, device)
            if best_acc is not None:
                fold_results[fold_dir.name] = best_acc
        except Exception as e:
            logger.error(f"Error training {fold_dir.name}: {e}")
            continue
    
    # Calculate overall results
    if fold_results:
        mean_acc = np.mean(list(fold_results.values()))
        std_acc = np.std(list(fold_results.values()))
        
        logger.info("="*50)
        logger.info("LOSO Cross-Validation Results:")
        for fold_name, acc in fold_results.items():
            logger.info(f"{fold_name}: {acc:.3f}")
        logger.info(f"Mean ± Std: {mean_acc:.3f} ± {std_acc:.3f}")
        
        # Check if target achieved
        target_acc = config.get('target_accuracy', 0.82)
        if mean_acc >= target_acc:
            logger.info(f"🎯 TARGET ACHIEVED! {mean_acc:.3f} ≥ {target_acc:.3f}")
        else:
            logger.info(f"Target not reached: {mean_acc:.3f} < {target_acc:.3f}")
        
        # Save overall results
        overall_results = {
            'fold_results': fold_results,
            'mean_accuracy': mean_acc,
            'std_accuracy': std_acc,
            'target_achieved': mean_acc >= target_acc,
            'config': config
        }
        
        output_dir = Path(config['output_dir'])
        with open(output_dir / 'loso_results.json', 'w') as f:
            json.dump(overall_results, f, indent=2)
    else:
        logger.error("No successful fold training completed")

if __name__ == '__main__':
    main()
