#!/usr/bin/env python3
"""
GRID Corpus Pretraining Script
==============================

Pretrain the CNN-LSTM encoder on GRID corpus subset with viseme-matched vocabulary.
This provides better initialization for ICU fine-tuning.

Key Features:
- 3-speaker GRID subset with viseme-matched words
- Encoder-only pretraining with frozen classification head initially
- Progressive unfreezing strategy
- CosineFCHead + ArcFace loss for enhanced discrimination
- Saves encoder state_dict for ICU fine-tuning

Author: Augment Agent
Date: 2025-09-27
"""

import os
import sys
import json
import yaml
import argparse
import logging
import time
from pathlib import Path

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, random_split
import numpy as np
from sklearn.metrics import accuracy_score, f1_score

# Add project paths - adjust for SageMaker structure
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.append(str(project_root))

from models import create_model
from training.advanced_training_components import (
    ComprehensiveVideoDataset,
    StandardizedPreprocessor,
    LabelSmoothingCrossEntropy
)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_config(config_path):
    """Load YAML configuration file."""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config

def create_grid_dataset(config):
    """Create GRID dataset for pretraining."""
    # Load global label mapping
    with open('configs/label2idx.json', 'r') as f:
        label2idx = json.load(f)
    
    # Create preprocessor (uses current preprocessing pipeline)
    preprocessor = StandardizedPreprocessor()
    
    # Create dataset
    dataset = ComprehensiveVideoDataset(
        data_root=config['data_root'],
        labels=config['labels'],
        label2idx=label2idx,
        preprocessor=preprocessor,
        temporal_jitter=config.get('temporal_jitter', False),
        temporal_jitter_frames=config.get('temporal_jitter_frames', 2)
    )
    
    return dataset, label2idx

def train_epoch(model, dataloader, criterion, optimizer, device, epoch, config):
    """Train for one epoch."""
    model.train()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    for batch_idx, (videos, labels) in enumerate(dataloader):
        videos, labels = videos.to(device), labels.to(device)
        
        optimizer.zero_grad()
        
        # Forward pass
        use_arcface = config['loss'] == 'arcface'
        outputs = model(videos, labels, use_arcface)
        loss = criterion(outputs, labels)
        
        # Backward pass
        loss.backward()
        
        # Gradient clipping
        if config.get('gradient_clip', 0) > 0:
            torch.nn.utils.clip_grad_norm_(model.parameters(), config['gradient_clip'])
        
        optimizer.step()
        
        # Statistics
        total_loss += loss.item()
        _, predicted = torch.max(outputs.data, 1)
        all_preds.extend(predicted.cpu().numpy())
        all_labels.extend(labels.cpu().numpy())
        
        if batch_idx % config.get('log_interval', 10) == 0:
            logger.info(f'Epoch {epoch}, Batch {batch_idx}/{len(dataloader)}, Loss: {loss.item():.4f}')
    
    # Calculate metrics
    avg_loss = total_loss / len(dataloader)
    accuracy = accuracy_score(all_labels, all_preds)
    f1 = f1_score(all_labels, all_preds, average='macro')
    
    return avg_loss, accuracy, f1

def validate_epoch(model, dataloader, criterion, device, config):
    """Validate for one epoch."""
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for videos, labels in dataloader:
            videos, labels = videos.to(device), labels.to(device)
            
            # Forward pass
            use_arcface = config['loss'] == 'arcface'
            outputs = model(videos, labels, use_arcface)
            loss = criterion(outputs, labels)
            
            # Statistics
            total_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            all_preds.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    # Calculate metrics
    avg_loss = total_loss / len(dataloader)
    accuracy = accuracy_score(all_labels, all_preds)
    f1 = f1_score(all_labels, all_preds, average='macro')
    
    return avg_loss, accuracy, f1

def main():
    parser = argparse.ArgumentParser(description='GRID Corpus Pretraining')
    parser.add_argument('--config', required=True, help='Path to config file')
    parser.add_argument('--device', default='auto', help='Device to use (auto, cpu, cuda)')
    args = parser.parse_args()
    
    # Load configuration
    config = load_config(args.config)
    
    # Set device
    if args.device == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(args.device)
    
    logger.info(f"Using device: {device}")
    
    # Set random seeds
    if config.get('seed'):
        torch.manual_seed(config['seed'])
        np.random.seed(config['seed'])
        if torch.cuda.is_available():
            torch.cuda.manual_seed(config['seed'])
    
    # Create dataset
    logger.info("Creating GRID dataset...")
    dataset, label2idx = create_grid_dataset(config)
    
    # Split dataset
    val_size = int(config.get('val_split', 0.2) * len(dataset))
    train_size = len(dataset) - val_size
    train_dataset, val_dataset = random_split(dataset, [train_size, val_size])
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['batch_size'],
        shuffle=True,
        num_workers=config.get('num_workers', 4),
        pin_memory=config.get('pin_memory', True)
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=config.get('num_workers', 4),
        pin_memory=config.get('pin_memory', True)
    )
    
    logger.info(f"Train samples: {len(train_dataset)}, Val samples: {len(val_dataset)}")
    
    # Create model
    model = create_model(
        num_classes=config['num_classes'],
        dropout=config.get('dropout', 0.6),
        head_type=config.get('head', 'cosine_fc')
    )
    model.to(device)
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.info(f"Model created: {total_params:,} total params, {trainable_params:,} trainable")
    
    # Create loss function
    if config['loss'] == 'arcface':
        from models.heads import ArcFaceLoss
        criterion = ArcFaceLoss(
            margin=config['arcface']['margin'],
            scale=config['arcface']['scale'],
            label_smoothing=config['arcface'].get('label_smoothing', 0.0)
        )
    else:
        criterion = nn.CrossEntropyLoss(label_smoothing=config.get('label_smoothing', 0.0))
    
    # Create optimizer
    optimizer = optim.Adam(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config.get('weight_decay', 0.0001)
    )
    
    # Create scheduler
    if config.get('scheduler') == 'reduce_on_plateau':
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            factor=config['scheduler_params']['factor'],
            patience=config['scheduler_params']['patience'],
            min_lr=config['scheduler_params']['min_lr']
        )
    else:
        scheduler = None
    
    # Training loop
    best_val_acc = 0.0
    patience_counter = 0
    
    # Create output directory
    output_dir = Path(config['output_dir'])
    output_dir.mkdir(parents=True, exist_ok=True)
    
    logger.info("Starting GRID pretraining...")
    
    for epoch in range(1, config['epochs'] + 1):
        # Progressive unfreezing
        if epoch == 1:
            model.freeze_encoder()
        elif epoch == config.get('freeze_encoder_epochs', 3) + 1:
            if config.get('unfreeze_last_block', True):
                model.unfreeze_last_block()
            else:
                model.unfreeze_encoder()
        
        # Train
        train_loss, train_acc, train_f1 = train_epoch(
            model, train_loader, criterion, optimizer, device, epoch, config
        )
        
        # Validate
        val_loss, val_acc, val_f1 = validate_epoch(
            model, val_loader, criterion, device, config
        )
        
        # Update scheduler
        if scheduler:
            scheduler.step(val_loss)
        
        # Log results
        logger.info(f"Epoch {epoch}/{config['epochs']}")
        logger.info(f"Train: Loss={train_loss:.4f}, Acc={train_acc:.3f}, F1={train_f1:.3f}")
        logger.info(f"Val: Loss={val_loss:.4f}, Acc={val_acc:.3f}, F1={val_f1:.3f}")
        
        # Save best model
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            patience_counter = 0
            
            # Save complete model
            torch.save(model.state_dict(), output_dir / 'best_model.pt')
            
            # Save encoder only (for ICU fine-tuning)
            torch.save(model.backbone.get_encoder_state_dict(), output_dir / 'encoder.pt')
            
            logger.info(f"New best model saved: {val_acc:.3f}")
        else:
            patience_counter += 1
        
        # Early stopping
        if patience_counter >= config.get('early_stop_patience', 7):
            logger.info(f"Early stopping at epoch {epoch}")
            break
        
        # Save checkpoint
        if epoch % config.get('save_interval', 5) == 0:
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_val_acc': best_val_acc,
            }, output_dir / f'checkpoint_epoch_{epoch}.pt')
    
    logger.info(f"GRID pretraining completed. Best validation accuracy: {best_val_acc:.3f}")
    logger.info(f"Encoder saved to: {output_dir / 'encoder.pt'}")

if __name__ == '__main__':
    main()
