#!/usr/bin/env python3
"""
Create GRID manifest and update config for single speaker training.
"""

import csv
import yaml
from pathlib import Path

def create_grid_manifest():
    """Create manifest file for GRID pretraining."""
    
    data_dir = Path("sagemaker_training_files/data/grid_subset_3spk")
    manifest_path = Path("sagemaker_training_files/data/grid_manifest.csv")
    
    manifest_data = []
    
    # Scan preprocessed data
    for speaker_dir in data_dir.iterdir():
        if not speaker_dir.is_dir():
            continue
            
        speaker_id = speaker_dir.name
        
        for word_dir in speaker_dir.iterdir():
            if not word_dir.is_dir():
                continue
                
            word = word_dir.name
            
            for video_file in word_dir.glob("*.mp4"):
                relative_path = video_file.relative_to(data_dir)
                manifest_data.append({
                    'video_path': str(relative_path),
                    'speaker_id': speaker_id,
                    'word': word,
                    'class_label': word,  # For GRID pretraining
                    'split': 'train'
                })
    
    # Write manifest
    with open(manifest_path, 'w', newline='') as f:
        if manifest_data:
            writer = csv.DictWriter(f, fieldnames=manifest_data[0].keys())
            writer.writeheader()
            writer.writerows(manifest_data)
    
    print(f"Created manifest with {len(manifest_data)} entries: {manifest_path}")
    return len(manifest_data) > 0

def update_grid_config():
    """Update GRID pretraining config for single speaker."""
    
    config_path = Path("sagemaker_training_files/configs/grid_pretrain.yaml")
    
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Get unique words from manifest
    manifest_path = Path("sagemaker_training_files/data/grid_manifest.csv")
    unique_words = set()
    
    with open(manifest_path, 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            unique_words.add(row['word'])
    
    # Update config for single speaker
    config['data_root'] = 'data/grid_subset_3spk'
    config['labels'] = sorted(list(unique_words))  # Use actual words found
    config['num_classes'] = len(unique_words)
    config['batch_size'] = 8  # Smaller batch for single speaker
    config['epochs'] = 30     # More epochs for single speaker
    config['val_split'] = 0.3  # Larger validation split
    config['early_stop_patience'] = 10  # Adjusted for single speaker
    
    # Save updated config
    single_speaker_config = Path("sagemaker_training_files/configs/grid_pretrain_s4.yaml")
    with open(single_speaker_config, 'w') as f:
        yaml.dump(config, f, default_flow_style=False)
    
    print(f"Created single-speaker config: {single_speaker_config}")
    print(f"Classes ({len(unique_words)}): {sorted(list(unique_words))}")
    return True

def main():
    print("📋 Creating GRID manifest and config...")
    
    if create_grid_manifest():
        print("✅ Manifest created successfully")
    else:
        print("❌ Failed to create manifest")
        return False
    
    if update_grid_config():
        print("✅ Config updated successfully")
    else:
        print("❌ Failed to update config")
        return False
    
    print("🎉 Ready for GRID pretraining!")
    return True

if __name__ == "__main__":
    main()
