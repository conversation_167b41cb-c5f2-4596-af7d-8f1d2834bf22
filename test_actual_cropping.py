#!/usr/bin/env python3
"""
Test Actual Cropping - Verify Lip Region Cropping
=================================================

Quick test to verify that the robust pipeline is actually cropping to detected lip regions.
"""

import sys
import cv2
import base64
import logging
from pathlib import Path
from typing import List, Dict, Any

# Add src to path
sys.path.append('src')
from preprocessing.robust_lip_detection_pipeline import RobustLipDetectionPipeline

def create_cropping_verification_html(results: List[Dict[str, Any]], output_file: Path) -> None:
    """Create HTML to verify actual cropping is happening."""
    
    html_content = '''<!DOCTYPE html>
<html>
<head>
    <title>🔪 Cropping Verification Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .header { background: #dc2626; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .test-item { background: white; padding: 15px; margin: 15px 0; border-radius: 8px; border: 2px solid #e5e7eb; }
        .test-item.success { border-color: #10b981; }
        .test-item.failed { border-color: #ef4444; }
        .frame-image { width: 200px; border: 1px solid #ccc; margin: 5px; }
        .crop-info { font-size: 12px; color: #666; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔪 Cropping Verification Test</h1>
        <p><strong>Purpose:</strong> Verify that videos are actually being cropped to detected lip regions</p>
    </div>
    '''
    
    for i, result in enumerate(results, 1):
        if result['processing_status'] == 'success':
            # Extract first frame to verify cropping
            output_path = result['output_path']
            
            try:
                cap = cv2.VideoCapture(output_path)
                if cap.isOpened():
                    ret, frame = cap.read()
                    if ret:
                        # Convert to base64
                        _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 90])
                        frame_b64 = base64.b64encode(buffer).decode('utf-8')
                        
                        # Get properties
                        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                        
                        bbox = result.get('bbox_used', 'N/A')
                        
                        html_content += f'''
                        <div class="test-item success">
                            <h3>{i}. {result['source_filename']}</h3>
                            <img src="data:image/jpeg;base64,{frame_b64}" alt="Cropped Frame" class="frame-image">
                            <div class="crop-info">
                                <strong>Output Resolution:</strong> {width}×{height}<br>
                                <strong>Detection Method:</strong> {result.get('detection_method', 'unknown')}<br>
                                <strong>Bbox Used:</strong> {bbox}<br>
                                <strong>Confidence:</strong> {result.get('detection_confidence', 0):.3f}<br>
                                <strong>Expected:</strong> Should show ONLY the lip region, not full face
                            </div>
                        </div>'''
                    cap.release()
            except Exception as e:
                html_content += f'''
                <div class="test-item failed">
                    <h3>{i}. {result['source_filename']}</h3>
                    <p>❌ Error extracting frame: {e}</p>
                </div>'''
        else:
            html_content += f'''
            <div class="test-item failed">
                <h3>{i}. {result['source_filename']}</h3>
                <p>❌ Processing failed: {result.get('error', 'Unknown error')}</p>
            </div>'''
    
    html_content += '''
    <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin-top: 20px;">
        <h3>🔍 Verification Checklist</h3>
        <p><strong>What to look for:</strong></p>
        <ul>
            <li>✅ Images should show ONLY lip regions, not full faces</li>
            <li>✅ Lips should be clearly visible and centered</li>
            <li>✅ Background should be minimal (just around mouth area)</li>
            <li>❌ If you see full faces or large face regions, cropping is NOT working</li>
        </ul>
    </div>
    </body>
    </html>'''
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Cropping verification HTML created: {output_file.absolute()}")

def main():
    """Quick cropping verification test."""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    print("🔪 CROPPING VERIFICATION TEST")
    print("="*40)
    print("Testing if videos are actually being cropped to lip regions...")
    print()
    
    # Test with just 3 videos for quick verification
    base_dir = Path('speaker_sets/full_speaker_sets_top7')
    output_dir = Path('test_cropping_verification')
    manifest_path = Path('cropping_test_manifest.csv')
    html_output = Path('cropping_verification_test.html')
    
    if not base_dir.exists():
        print(f"❌ Input directory not found: {base_dir}")
        return
    
    # Get 3 test videos quickly
    test_videos = []
    for speaker_dir in base_dir.iterdir():
        if speaker_dir.is_dir() and speaker_dir.name.startswith('speaker_'):
            for class_dir in speaker_dir.iterdir():
                if class_dir.is_dir():
                    videos = list(class_dir.glob('*.mp4'))
                    if videos:
                        test_videos.append(videos[0])  # Just take first video from each class
                        if len(test_videos) >= 3:
                            break
            if len(test_videos) >= 3:
                break
    
    if not test_videos:
        print("❌ No test videos found")
        return
    
    print(f"🎬 Testing cropping with {len(test_videos)} videos...")
    
    # Initialize pipeline
    pipeline = RobustLipDetectionPipeline(
        output_dir=output_dir,
        manifest_path=manifest_path
    )
    
    # Process videos
    results = []
    for i, video_path in enumerate(test_videos, 1):
        print(f"\n[{i}/{len(test_videos)}] Testing: {video_path.name}")
        result = pipeline.process_single_video(video_path)
        results.append(result)
    
    # Create verification HTML
    print("\n🌐 Creating cropping verification browser...")
    create_cropping_verification_html(results, html_output)
    
    print(f"\n🔍 VERIFICATION COMPLETE!")
    print(f"📄 Results: {html_output.absolute()}")
    print("\n⚠️  IMPORTANT: Open the HTML file and check if the images show ONLY lip regions!")
    print("   If you see full faces, the cropping is still not working correctly.")

if __name__ == '__main__':
    main()
