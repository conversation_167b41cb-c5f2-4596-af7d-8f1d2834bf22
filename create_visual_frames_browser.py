#!/usr/bin/env python3
"""
Create Visual Frames Browser for Enhanced Pipeline Results
Extracts sample frames from processed videos and creates browser interface
"""

import cv2
import pathlib
import base64
import json
from typing import List, Dict, Any
import pandas as pd

def extract_sample_frames(video_path: pathlib.Path, num_frames: int = 5) -> List[str]:
    """Extract sample frames from video and return as base64 encoded images."""
    frames_b64 = []
    
    try:
        cap = cv2.VideoCapture(str(video_path))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        if total_frames == 0:
            return []
        
        # Extract evenly spaced frames
        frame_indices = [int(i * total_frames / num_frames) for i in range(num_frames)]
        
        for frame_idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = cap.read()
            
            if ret:
                # Convert to RGB if color, keep grayscale as is
                if len(frame.shape) == 3:
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                # Encode frame as base64
                _, buffer = cv2.imencode('.png', frame)
                frame_b64 = base64.b64encode(buffer).decode('utf-8')
                frames_b64.append(frame_b64)
        
        cap.release()
        
    except Exception as e:
        print(f"Error extracting frames from {video_path}: {e}")
    
    return frames_b64

def create_visual_browser():
    """Create visual browser interface for enhanced pipeline results."""
    
    # Load manifest
    manifest_path = pathlib.Path("enhanced_pipeline_validation/enhanced_pipeline_manifest.csv")
    if not manifest_path.exists():
        print("❌ Manifest file not found!")
        return
    
    df = pd.read_csv(manifest_path)
    
    # Create output directory
    output_dir = pathlib.Path("enhanced_pipeline_validation/visual_frames")
    output_dir.mkdir(exist_ok=True)
    
    # Process each video
    video_data = []
    
    print("🎬 Extracting sample frames from processed videos...")
    
    for idx, row in df.iterrows():
        video_name = pathlib.Path(row['input_path']).name
        output_path = pathlib.Path(row['output_path'])
        
        print(f"   📹 Processing: {video_name}")
        
        # Extract frames if video exists
        frames_b64 = []
        if output_path.exists():
            frames_b64 = extract_sample_frames(output_path, num_frames=5)
        
        video_info = {
            'name': video_name,
            'success': row['success'],
            'format_type': row.get('format_type', 'unknown'),
            'crop_status': row.get('crop_status', 'unknown'),
            'lip_position': row.get('lip_position', 'unknown'),
            'original_frames': row.get('original_frames', 0),
            'processed_frames': row.get('processed_frames', 0),
            'output_resolution': row.get('output_resolution', 'unknown'),
            'output_format': row.get('output_format', 'unknown'),
            'validation_resolution': row.get('validation_resolution', False),
            'validation_channels': row.get('validation_channels', False),
            'validation_frames': row.get('validation_frames', False),
            'validation_mouth': row.get('validation_mouth', False),
            'validation_file': row.get('validation_file', False),
            'error_message': row.get('error_message', ''),
            'frames': frames_b64
        }
        
        video_data.append(video_info)
    
    # Create HTML interface
    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Pipeline Visual Results</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }}
        .summary {{
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .video-card {{
            background: white;
            margin-bottom: 20px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .video-header {{
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
        }}
        .video-name {{
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 5px;
        }}
        .status-success {{
            color: #28a745;
            font-weight: bold;
        }}
        .status-failed {{
            color: #dc3545;
            font-weight: bold;
        }}
        .video-details {{
            padding: 15px 20px;
            background-color: #f8f9fa;
        }}
        .detail-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }}
        .detail-item {{
            background: white;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }}
        .frames-container {{
            padding: 20px;
        }}
        .frames-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }}
        .frame-item {{
            text-align: center;
        }}
        .frame-image {{
            max-width: 100%;
            height: auto;
            border: 2px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
        }}
        .frame-label {{
            margin-top: 5px;
            font-size: 12px;
            color: #666;
        }}
        .validation-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }}
        .validation-item {{
            padding: 8px;
            border-radius: 5px;
            text-align: center;
            font-size: 12px;
            font-weight: bold;
        }}
        .validation-pass {{
            background-color: #d4edda;
            color: #155724;
        }}
        .validation-fail {{
            background-color: #f8d7da;
            color: #721c24;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🎬 Enhanced Pipeline Visual Results</h1>
        <p>Visual inspection of processed speaker set videos</p>
    </div>
    
    <div class="summary">
        <h2>📊 Processing Summary</h2>
        <p><strong>Total Videos:</strong> {len(video_data)}</p>
        <p><strong>Successful:</strong> {sum(1 for v in video_data if v['success'])}</p>
        <p><strong>Failed:</strong> {sum(1 for v in video_data if not v['success'])}</p>
        <p><strong>Success Rate:</strong> {sum(1 for v in video_data if v['success']) / len(video_data) * 100:.1f}%</p>
    </div>
"""
    
    # Add video cards
    for video in video_data:
        status_class = "status-success" if video['success'] else "status-failed"
        status_text = "✅ SUCCESS" if video['success'] else "❌ FAILED"
        
        html_content += f"""
    <div class="video-card">
        <div class="video-header">
            <div class="video-name">{video['name']}</div>
            <div class="{status_class}">{status_text}</div>
        </div>
        
        <div class="video-details">
            <div class="detail-grid">
                <div class="detail-item">
                    <strong>Format:</strong> {video['format_type']}
                </div>
                <div class="detail-item">
                    <strong>Crop Status:</strong> {video['crop_status']}
                </div>
                <div class="detail-item">
                    <strong>Lip Position:</strong> {video['lip_position']}
                </div>
                <div class="detail-item">
                    <strong>Frames:</strong> {video['original_frames']} → {video['processed_frames']}
                </div>
                <div class="detail-item">
                    <strong>Resolution:</strong> {video['output_resolution']}
                </div>
                <div class="detail-item">
                    <strong>Format:</strong> {video['output_format']}
                </div>
            </div>
            
            <div class="validation-grid">
                <div class="validation-item {'validation-pass' if video['validation_resolution'] else 'validation-fail'}">
                    Resolution: {'✓' if video['validation_resolution'] else '✗'}
                </div>
                <div class="validation-item {'validation-pass' if video['validation_channels'] else 'validation-fail'}">
                    Channels: {'✓' if video['validation_channels'] else '✗'}
                </div>
                <div class="validation-item {'validation-pass' if video['validation_frames'] else 'validation-fail'}">
                    Frames: {'✓' if video['validation_frames'] else '✗'}
                </div>
                <div class="validation-item {'validation-pass' if video['validation_mouth'] else 'validation-fail'}">
                    Mouth: {'✓' if video['validation_mouth'] else '✗'}
                </div>
                <div class="validation-item {'validation-pass' if video['validation_file'] else 'validation-fail'}">
                    File: {'✓' if video['validation_file'] else '✗'}
                </div>
            </div>
            
            {f'<p style="color: #dc3545; margin-top: 10px;"><strong>Error:</strong> {video["error_message"]}</p>' if video['error_message'] else ''}
        </div>
        
        <div class="frames-container">
            <h4>Sample Frames (96×64 grayscale, 32 total frames)</h4>
            <div class="frames-grid">
"""
        
        # Add frames
        if video['frames']:
            for i, frame_b64 in enumerate(video['frames']):
                html_content += f"""
                <div class="frame-item">
                    <img src="data:image/png;base64,{frame_b64}" alt="Frame {i+1}" class="frame-image">
                    <div class="frame-label">Frame {i+1}</div>
                </div>
"""
        else:
            html_content += """
                <div class="frame-item">
                    <div style="padding: 40px; background-color: #f8f9fa; border: 2px dashed #ddd; border-radius: 5px;">
                        No frames available
                    </div>
                </div>
"""
        
        html_content += """
            </div>
        </div>
    </div>
"""
    
    html_content += """
</body>
</html>
"""
    
    # Save HTML file
    html_path = pathlib.Path("enhanced_pipeline_validation/visual_frames_browser.html")
    with open(html_path, 'w') as f:
        f.write(html_content)
    
    print(f"✅ Visual browser created: {html_path}")
    return html_path

if __name__ == "__main__":
    html_path = create_visual_browser()
    print(f"🌐 Open in browser: file://{html_path.absolute()}")
