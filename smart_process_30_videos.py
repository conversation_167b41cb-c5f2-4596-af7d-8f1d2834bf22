#!/usr/bin/env python3
"""
SMART BATCH PROCESS 30 RANDOM VIDEOS - Adaptive Cropping
========================================================

Intelligently processes videos by detecting their format:
- Full-size videos (400×200): Apply direct lip cropping
- Already-cropped videos (132×100): Skip cropping, just resize/process
- Other formats: Handle appropriately
"""

import cv2
import numpy as np
from pathlib import Path
import logging
import random
import glob

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def smart_process_video(input_path: Path, output_path: Path):
    """
    Intelligently process video based on its dimensions.
    """
    
    logger.info(f"🎬 Smart processing: {input_path.name}")
    
    # Load video
    cap = cv2.VideoCapture(str(input_path))
    if not cap.isOpened():
        logger.error(f"Cannot open video: {input_path}")
        return False
    
    # Read all frames
    frames = []
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        frames.append(frame)
    cap.release()
    
    if not frames:
        logger.error("No frames found")
        return False
    
    h, w = frames[0].shape[:2]
    logger.info(f"📹 Loaded {len(frames)} frames, {w}×{h} pixels")
    
    # SMART PROCESSING BASED ON VIDEO DIMENSIONS
    processed_frames = []
    
    if w == 400 and h == 200:
        # FULL-SIZE VIDEO: Apply direct lip cropping
        logger.info("🎯 Full-size video detected - applying direct lip cropping")
        
        # Define EXACT crop coordinates for lip region
        crop_x1 = int(w * 0.25)  # 25% from left (100 pixels)
        crop_x2 = int(w * 0.75)  # 75% from left (300 pixels) 
        crop_y1 = int(h * 0.05)  # 5% from top (10 pixels)
        crop_y2 = int(h * 0.45)  # 45% from top (90 pixels)
        
        for frame in frames:
            # DIRECT CROP - extract lip region
            cropped = frame[crop_y1:crop_y2, crop_x1:crop_x2]
            
            # Convert to grayscale
            gray = cv2.cvtColor(cropped, cv2.COLOR_BGR2GRAY)
            
            # Resize to 96×64
            resized = cv2.resize(gray, (96, 64), interpolation=cv2.INTER_LINEAR)
            
            processed_frames.append(resized)
            
    elif w == 132 and h == 100:
        # ALREADY-CROPPED VIDEO: Skip cropping, just process
        logger.info("✂️ Already-cropped video detected - skipping crop, processing directly")
        
        for frame in frames:
            # Convert to grayscale (no cropping needed)
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # Resize to 96×64
            resized = cv2.resize(gray, (96, 64), interpolation=cv2.INTER_LINEAR)
            
            processed_frames.append(resized)
            
    else:
        # OTHER FORMAT: Apply proportional cropping
        logger.info(f"🔧 Custom format ({w}×{h}) detected - applying proportional cropping")
        
        # Use same proportions as the proven method
        crop_x1 = int(w * 0.25)  # 25% from left
        crop_x2 = int(w * 0.75)  # 75% from left
        crop_y1 = int(h * 0.05)  # 5% from top
        crop_y2 = int(h * 0.45)  # 45% from top
        
        for frame in frames:
            # Proportional crop
            cropped = frame[crop_y1:crop_y2, crop_x1:crop_x2]
            
            # Convert to grayscale
            gray = cv2.cvtColor(cropped, cv2.COLOR_BGR2GRAY)
            
            # Resize to 96×64
            resized = cv2.resize(gray, (96, 64), interpolation=cv2.INTER_LINEAR)
            
            processed_frames.append(resized)
    
    # Sample to exactly 32 frames (uniform sampling)
    if len(processed_frames) >= 32:
        indices = np.linspace(0, len(processed_frames) - 1, 32, dtype=int)
        sampled_frames = [processed_frames[i] for i in indices]
    else:
        # Pad with repeated frames if too few
        sampled_frames = processed_frames[:]
        while len(sampled_frames) < 32:
            sampled_frames.extend(processed_frames[:32 - len(sampled_frames)])
        sampled_frames = sampled_frames[:32]
    
    # Create output directory
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Save as MP4 at 15 FPS
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(output_path), fourcc, 15.0, (96, 64), isColor=False)
    
    for frame in sampled_frames:
        out.write(frame)
    
    out.release()
    
    logger.info(f"💾 Saved smartly processed video: {output_path}")
    return True

def main():
    """Process 30 random videos with smart format detection."""
    
    # Input and output directories
    input_dir = Path("/Users/<USER>/Desktop/LRP classifier 11.9.25/speaker_sets/full_speaker_sets_top7")
    output_dir = Path("/Users/<USER>/Desktop/LRP classifier 11.9.25/speaker_sets")
    
    print("🧠 SMART BATCH PROCESS 30 RANDOM VIDEOS - ADAPTIVE CROPPING")
    print("="*70)
    print("Strategy:")
    print("• Detect video format automatically")
    print("• Full-size (400×200): Apply direct lip cropping")
    print("• Already-cropped (132×100): Skip cropping, just resize")
    print("• Other formats: Apply proportional cropping")
    print("• Always output: 32 frames, 96×64 pixels, grayscale, 15 FPS")
    print("="*70)
    
    # Find all MP4 videos recursively
    all_videos = []
    for video_path in input_dir.rglob("*.mp4"):
        all_videos.append(video_path)
    
    if len(all_videos) < 30:
        print(f"❌ Error: Only {len(all_videos)} videos found, need at least 30")
        return
    
    print(f"📁 Found {len(all_videos)} total videos across all speakers/classes")
    
    # Randomly select 30 videos (different seed for new selection)
    random.seed(456)  # New seed for third batch
    selected_videos = random.sample(all_videos, 30)

    print(f"🎲 Randomly selected 30 MORE videos for smart processing (batch 3)")
    
    success_count = 0
    format_stats = {"400x200": 0, "132x100": 0, "other": 0}
    
    for i, input_video in enumerate(selected_videos, 1):
        print(f"\n🎬 Processing video {i}/30: {input_video.name}")
        print(f"📂 From: {input_video.parent.name}")
        
        # Create output filename with speaker and class info
        speaker_name = input_video.parent.parent.name  # e.g., "speaker_1 "
        class_name = input_video.parent.name           # e.g., "doctor"
        
        output_name = f"smart2_{speaker_name.strip()}_{class_name}_{input_video.stem}.mp4"
        output_video = output_dir / output_name
        
        # Process the video
        success = smart_process_video(input_video, output_video)
        
        if success:
            print(f"✅ Video {i} completed: {output_name}")
            success_count += 1
            
            # Track format statistics (simple check)
            cap = cv2.VideoCapture(str(input_video))
            ret, frame = cap.read()
            if ret:
                h, w = frame.shape[:2]
                if w == 400 and h == 200:
                    format_stats["400x200"] += 1
                elif w == 132 and h == 100:
                    format_stats["132x100"] += 1
                else:
                    format_stats["other"] += 1
            cap.release()
        else:
            print(f"❌ Video {i} failed")
    
    print(f"\n🎉 SMART BATCH PROCESSING COMPLETE!")
    print(f"📊 Successfully processed: {success_count}/30 videos")
    print(f"📁 Output location: {output_dir}")
    print("📏 All videos: 32 frames, 96×64 pixels, grayscale, 15 FPS")
    print(f"\n📈 FORMAT DISTRIBUTION:")
    print(f"• Full-size (400×200): {format_stats['400x200']} videos")
    print(f"• Already-cropped (132×100): {format_stats['132x100']} videos") 
    print(f"• Other formats: {format_stats['other']} videos")
    print("🧠 Used smart adaptive processing - optimal for each format!")

if __name__ == "__main__":
    main()
