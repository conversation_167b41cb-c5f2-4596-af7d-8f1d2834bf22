#!/usr/bin/env python3
"""
Debug script to find where the lips actually are in the videos
"""

import cv2
import numpy as np
from pathlib import Path

def debug_lip_positions():
    """Debug where lips actually are in different video formats"""
    
    # Test videos from different speakers/formats
    test_videos = [
        "speaker_sets/partial_speaker_sets_top7/speaker 9 volunteer lady green shirt 6 done/my_back_hurts/my_back_hurts__useruser01__40to64__female__caucasian__20250716T055041.mp4",
        "speaker_sets/partial_speaker_sets_top7/speaker 10 sarah RFR 4 done/doctor/doctor__useruser01__40to64__female__caucasian__20250830T110719.mp4",
        "speaker_sets/partial_speaker_sets_top7/speaker 2 phan 4 done/my mouth is dry/my_mouth_is_dry_speaker2_video_013.mp4"
    ]
    
    print("🔍 DEBUGGING LIP POSITIONS IN VIDEOS")
    print("=" * 60)
    
    for i, video_path in enumerate(test_videos, 1):
        if not Path(video_path).exists():
            print(f"❌ Video {i} not found: {video_path}")
            continue
            
        print(f"\n📹 VIDEO {i}: {Path(video_path).name}")
        
        cap = cv2.VideoCapture(video_path)
        ret, frame = cap.read()
        
        if not ret:
            print("❌ Could not read video")
            cap.release()
            continue
            
        h, w = frame.shape[:2]
        print(f"   Dimensions: {w}×{h}")
        
        # Show current crop regions being used
        if w == 400 and h == 200:
            # Current "improved" coordinates
            crop_x1 = int(w * 0.20)  # 80
            crop_x2 = int(w * 0.80)  # 320
            crop_y1 = int(h * 0.30)  # 60
            crop_y2 = int(h * 0.70)  # 140
            
            print(f"   Current crop: x={crop_x1}-{crop_x2}, y={crop_y1}-{crop_y2}")
            print(f"   Current crop size: {crop_x2-crop_x1}×{crop_y2-crop_y1}")
            
            # Draw current crop rectangle on frame
            frame_with_crop = frame.copy()
            cv2.rectangle(frame_with_crop, (crop_x1, crop_y1), (crop_x2, crop_y2), (0, 255, 0), 2)
            cv2.putText(frame_with_crop, "CURRENT CROP", (crop_x1, crop_y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
            
            # Try different crop regions to find lips
            # Region 1: Upper area (where lips might be in ICU-style videos)
            upper_y1 = int(h * 0.10)  # 20
            upper_y2 = int(h * 0.50)  # 100
            cv2.rectangle(frame_with_crop, (crop_x1, upper_y1), (crop_x2, upper_y2), (255, 0, 0), 2)
            cv2.putText(frame_with_crop, "UPPER REGION", (crop_x1, upper_y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
            
            # Region 2: Lower area 
            lower_y1 = int(h * 0.50)  # 100
            lower_y2 = int(h * 0.90)  # 180
            cv2.rectangle(frame_with_crop, (crop_x1, lower_y1), (crop_x2, lower_y2), (0, 0, 255), 2)
            cv2.putText(frame_with_crop, "LOWER REGION", (crop_x1, lower_y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
            
            # Region 3: Center area
            center_y1 = int(h * 0.25)  # 50
            center_y2 = int(h * 0.75)  # 150
            cv2.rectangle(frame_with_crop, (crop_x1, center_y1), (crop_x2, center_y2), (255, 255, 0), 2)
            cv2.putText(frame_with_crop, "CENTER REGION", (crop_x1, center_y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
            
        elif w == 1280 and h == 720:
            # Speaker 2 phan videos - proportional coordinates
            crop_x1 = int(w * 0.20)  # 256
            crop_x2 = int(w * 0.80)  # 1024
            crop_y1 = int(h * 0.30)  # 216
            crop_y2 = int(h * 0.70)  # 504
            
            print(f"   Current crop: x={crop_x1}-{crop_x2}, y={crop_y1}-{crop_y2}")
            print(f"   Current crop size: {crop_x2-crop_x1}×{crop_y2-crop_y1}")
            
            # Draw current crop rectangle
            frame_with_crop = frame.copy()
            cv2.rectangle(frame_with_crop, (crop_x1, crop_y1), (crop_x2, crop_y2), (0, 255, 0), 3)
            cv2.putText(frame_with_crop, "CURRENT CROP", (crop_x1, crop_y1-10), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 0), 2)
            
            # Try different regions for 1280x720 videos
            # Upper region
            upper_y1 = int(h * 0.10)  # 72
            upper_y2 = int(h * 0.50)  # 360
            cv2.rectangle(frame_with_crop, (crop_x1, upper_y1), (crop_x2, upper_y2), (255, 0, 0), 3)
            cv2.putText(frame_with_crop, "UPPER", (crop_x1, upper_y1-10), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255, 0, 0), 2)
            
            # Lower region
            lower_y1 = int(h * 0.50)  # 360
            lower_y2 = int(h * 0.90)  # 648
            cv2.rectangle(frame_with_crop, (crop_x1, lower_y1), (crop_x2, lower_y2), (0, 0, 255), 3)
            cv2.putText(frame_with_crop, "LOWER", (crop_x1, lower_y1-10), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 255), 2)
        
        else:
            frame_with_crop = frame.copy()
            print(f"   Unknown format: {w}×{h}")
        
        # Save the frame with crop regions marked
        output_name = f"debug_lip_regions_{i}_{w}x{h}.png"
        cv2.imwrite(output_name, frame_with_crop)
        print(f"   💾 Saved with crop regions: {output_name}")
        
        cap.release()
    
    print(f"\n🎯 ANALYSIS COMPLETE!")
    print("Check the saved PNG files to see where the current crop regions are")
    print("and determine where the lips actually appear in each video format.")
    print("\nLook for:")
    print("• GREEN rectangle = Current crop being used")
    print("• BLUE rectangle = Upper region test")
    print("• RED rectangle = Lower region test") 
    print("• YELLOW rectangle = Center region test (400×200 only)")

if __name__ == "__main__":
    debug_lip_positions()
